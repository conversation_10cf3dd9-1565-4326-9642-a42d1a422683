; Main module name from modules/general/
INDEX_MODULE="taskbar"
; Template name to preload from skins/ like "paper", "void", "miniml", "rpg", etc...
YALF_SKIN="paper"
; Default application language. Loaded from languages directory.
YALF_LANG="english"
; Allow online language switching by remote clients?
; Now supported locale switching by GET callback like ?yalfswitchlocale=ukrainian
YALF_LANG_SWITCHABLE=1
;Is application global menu rendering enabled?
YALF_MENU_ENABLED=1

;XHProf Hierarchical Profiler enabled?
XHPROF=0
; XHProf libs path. Recomended to be masked in production.
XHPROF_PATH="modules/foreign/xhprof"

; Your application logo, name and site URL
YALF_LOGO="skins/wrcolor.png"
YALF_URL="https://eagleview.com"
YALF_APP="EagleView"
YALF_TITLE="EagleView"

; Disabled modules list. Separator - comma.
YALF_DISABLED_MODULES=

; Emulate RCMS users auth and rights system?
YALF_AUTH_ENABLED=1

; Enables "always stay logged in" as default behaviour.
YALF_AUTH_KEEP_DEFAULT=1

; Enables "Stay logged in" checkbox on login form
YALF_AUTH_KEEP_CB=0

; Disables redirect to index module on success login
YALF_AUTH_NOREDIR=0

; Modules which not require any authorization (public modules)
YALF_NO_AUTH_MODULES="remoteapi,pseudostream,manifestor"

; Logging emulation. Possible values: fake(output to nowhere), file(output to content/logs/yalflog.log), mysql (YALF_LOG_TABLE table)
YALF_LOGGING_TYPE="mysql"
YALF_LOG_TABLE="weblogs"

;Configs editable from web with sysconf module. Comma separated.
YALF_EDITABLE_CONFIGS="config/yalf.ini,config/mysql.ini,config/binpaths.ini,config/globalmenu.ini,config/alter.ini"

; List of layers which must be loaded, with their dependencies.
; Format: LAYER_*="lib1,lib2,lib3" will load api/libs/api.lib1.php etc..
LAYER_LOCALE="i18n"
LAYER_NYANORM="mysql,nyanorm,sqldebug"
LAYER_CACHE="ubconfig,ubcache"
LAYER_ZEN="zenflow"
LAYER_WR_CORE="backups,onvif,lair,systeminfo,updatemanager,net"
LAYER_WR="storages,models,acl,cameras,recorder,archive,rotator,export,streamdog,restapi"
; Renderer layers
LAYER_WEBRENDER="webrender,workicons,astral"

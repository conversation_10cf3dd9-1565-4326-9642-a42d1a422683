<!DOCTYPE html>
<html lang="pt-br" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}EagleView{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-dark text-light">
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <img src="{% static 'images/logo.png' %}" alt="EagleView" class="logo">
                <h4>EagleView</h4>
            </div>
            
            <ul class="list-unstyled components">
                <li class="{% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                    <a href="{% url 'dashboard' %}">
                        <i class="fas fa-home"></i>
                        <span>Painel</span>
                    </a>
                </li>
                <li class="{% if request.resolver_match.url_name == 'timelapse_list' %}active{% endif %}">
                    <a href="{% url 'timelapse_list' %}">
                        <i class="fas fa-folder"></i>
                        <span>Projetos</span>
                    </a>
                </li>
                <li class="{% if request.resolver_match.url_name == 'frame_editor' %}active{% endif %}">
                    <a href="{% url 'frame_editor' %}">
                        <i class="fas fa-play"></i>
                        <span>Gerar Timelapse</span>
                    </a>
                </li>
                {% if user.is_staff or user.is_superuser %}
                <li class="{% if 'directory' in request.resolver_match.url_name %}active{% endif %}">
                    <a href="{% url 'server_directory_list' %}">
                        <i class="fas fa-folder-open"></i>
                        <span>Diretórios</span>
                    </a>
                </li>
                {% endif %}
                <li class="{% if 'server' in request.resolver_match.url_name %}active{% endif %}">
                    <a href="{% url 'server_settings' %}">
                        <i class="fas fa-server"></i>
                        <span>Servidores</span>
                    </a>
                </li>
                {% if user.is_staff %}
                <li>
                    <a href="/admin/">
                        <i class="fas fa-cog"></i>
                        <span>Configurações</span>
                    </a>
                </li>
                {% endif %}
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-circle me-2"></i>
                    <span class="text-light">{{ user.get_full_name|default:user.username }}</span>
                </div>
                <form method="post" action="{% url 'logout' %}" style="margin: 0;">
                    {% csrf_token %}
                    <button type="submit" class="logout-btn text-decoration-none border-0 bg-transparent">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Sair</span>
                    </button>
                </form>
            </div>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg top-header">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-outline-secondary" 
                            style="display: flex !important; align-items: center; justify-content: center; min-width: 44px; min-height: 44px; border: 2px solid #ff6a00; background: rgba(255, 106, 0, 0.1);">
                        <i class="fas fa-bars"></i>
                    </button>
                    

                    
                    <!-- CSS para menu hambúrguer responsivo -->
                    <style>
                        /* Comportamento da sidebar e adaptação da página */
                        .sidebar {
                            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                        }
                        
                        #content {
                            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                        }
                        
                        @media (max-width: 768px) {
                            /* Mobile: sidebar oculta por padrão */
                            .sidebar {
                                transform: translateX(-100%) !important;
                            }
                            .sidebar.active {
                                transform: translateX(0) !important;
                            }
                            #content {
                                margin-left: 0 !important;
                            }
                        }
                        
                        @media (min-width: 769px) {
                            /* Desktop: sidebar visível por padrão, mas pode ser ocultada */
                            .sidebar {
                                transform: translateX(0) !important;
                            }
                            .sidebar.active {
                                transform: translateX(-100%) !important;
                            }
                            
                            /* Quando sidebar está visível (padrão) */
                            #content {
                                margin-left: 280px !important;
                            }
                            
                            /* Quando sidebar está oculta (.active em desktop) */
                            .sidebar.active ~ #content {
                                margin-left: 0 !important;
                            }
                        }
                    </style>
                    
                    <div class="ms-auto">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                {% block breadcrumb %}
                                <li class="breadcrumb-item"><a href="{% url 'dashboard' %}" class="text-decoration-none">Home</a></li>
                                {% endblock %}
                            </ol>
                        </nav>
                    </div>
                </div>
            </nav>

            <div class="container-fluid content-area">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <strong>
                                {% if message.tags == 'success' %}<i class="fas fa-check-circle me-2"></i>{% endif %}
                                {% if message.tags == 'error' or message.tags == 'danger' %}<i class="fas fa-exclamation-circle me-2"></i>{% endif %}
                                {% if message.tags == 'warning' %}<i class="fas fa-exclamation-triangle me-2"></i>{% endif %}
                                {% if message.tags == 'info' %}<i class="fas fa-info-circle me-2"></i>{% endif %}
                            </strong>
                            {{ message }}
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/app.js' %}"></script>
    <script src="{% static 'js/notifications.js' %}"></script>
    <script src="{% static 'js/realtime.js' %}"></script>
    {% block extra_js %}{% endblock %}
    
    <script>
        // Função para toggle da sidebar com comportamento responsivo
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const hamburgerBtn = document.getElementById('sidebarCollapse');
            
            if (sidebar) {
                sidebar.classList.toggle('active');
                
                // Feedback visual no botão
                if (hamburgerBtn) {
                    if (sidebar.classList.contains('active')) {
                        hamburgerBtn.title = 'Abrir menu';
                    } else {
                        hamburgerBtn.title = 'Fechar menu';
                    }
                }
                
                // Adicionar classe ao body apenas em mobile
                if (window.innerWidth <= 768) {
                    document.body.classList.toggle('sidebar-open');
                }
            }
        }
        
        // Inicialização do sistema
        document.addEventListener('DOMContentLoaded', function() {
            const hamburgerBtn = document.getElementById('sidebarCollapse');
            const sidebar = document.getElementById('sidebar');
            
            // Configurar menu hambúrguer
            if (hamburgerBtn && sidebar) {
                // Adicionar listener principal
                hamburgerBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleSidebar();
                });
                
                // Backup onclick direto
                hamburgerBtn.onclick = function() {
                    toggleSidebar();
                    return false;
                };
                 
                // Listener para redimensionamento da tela
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        // Desktop: garantir que body não tenha classe sidebar-open
                        document.body.classList.remove('sidebar-open');
                    }
                });
                 
                // Listener para fechar menu ao clicar fora (apenas mobile)
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 768) {
                        if (sidebar && sidebar.classList.contains('active')) {
                            // Se clicou fora da sidebar e não foi no botão hambúrguer
                            if (!sidebar.contains(e.target) && !hamburgerBtn.contains(e.target)) {
                                sidebar.classList.remove('active');
                                document.body.classList.remove('sidebar-open');
                            }
                        }
                    }
                });
            }
             
            // Melhorar contraste, visibilidade e animações
            // Aplicar classes Bootstrap dark mode automaticamente
            document.querySelectorAll('.form-control').forEach(el => {
                el.classList.add('bg-dark', 'text-light', 'border-secondary');
            });
            
            document.querySelectorAll('.form-select').forEach(el => {
                el.classList.add('bg-dark', 'text-light', 'border-secondary');
            });
            
            document.querySelectorAll('.card').forEach((el, index) => {
                el.classList.add('bg-dark', 'text-light', 'border-secondary');
                // Adicionar animação escalonada para os cards
                setTimeout(() => {
                    el.classList.add('animate-in');
                }, index * 100);
            });
            
            document.querySelectorAll('.table').forEach(el => {
                el.classList.add('table-dark');
            });
            
            document.querySelectorAll('.modal-content').forEach(el => {
                el.classList.add('bg-dark', 'text-light');
            });
            
            // Adicionar efeito de hover melhorado para botões
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // Melhorar feedback visual nos formulários
            document.querySelectorAll('.form-control, .form-select').forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
            
            // Adicionar animação suave para alertas
            document.querySelectorAll('.alert').forEach((alert, index) => {
                setTimeout(() => {
                    alert.classList.add('animate-in');
                }, index * 150);
            });
        });
    </script>
</body>
</html> 
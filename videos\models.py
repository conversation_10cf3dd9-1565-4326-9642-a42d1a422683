from django.db import models
from django.contrib.auth.models import Group, User
from django.utils import timezone
import os


class ServerDirectory(models.Model):
    """Configuração de diretórios do servidor para navegação"""
    name = models.CharField('Nome do Diretório', max_length=100)
    path = models.Char<PERSON>ield('Caminho Completo', max_length=500, help_text='Caminho absoluto no servidor')
    description = models.TextField('Descrição', blank=True)
    is_active = models.BooleanField('Ativo', default=True)
    allowed_groups = models.ManyToManyField(Group, verbose_name='Grupos Permitidos', blank=True)
    created_at = models.DateTimeField('Criado em', auto_now_add=True)
    updated_at = models.DateTimeField('Atualizado em', auto_now=True)
    
    class Meta:
        verbose_name = 'Diretório do Servidor'
        verbose_name_plural = 'Diretórios do Servidor'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.path})"
    
    def get_images(self, subdirectory=""):
        """Lista imagens do diretório"""
        full_path = os.path.join(self.path, subdirectory) if subdirectory else self.path
        
        if not os.path.exists(full_path):
            return []
        
        images = []
        supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        try:
            for item in os.listdir(full_path):
                item_path = os.path.join(full_path, item)
                
                if os.path.isfile(item_path):
                    _, ext = os.path.splitext(item.lower())
                    if ext in supported_formats:
                        # Obter informações do arquivo
                        stat = os.stat(item_path)
                        images.append({
                            'name': item,
                            'path': os.path.join(subdirectory, item) if subdirectory else item,
                            'full_path': item_path,
                            'size': stat.st_size,
                            'modified': stat.st_mtime,
                            'type': 'image'
                        })
        except Exception as e:
            print(f"Erro ao listar imagens em {full_path}: {e}")
        
        return sorted(images, key=lambda x: x['name'])
    
    def get_subdirectories(self, subdirectory=""):
        """Lista subdiretórios"""
        full_path = os.path.join(self.path, subdirectory) if subdirectory else self.path
        
        if not os.path.exists(full_path):
            return []
        
        directories = []
        
        try:
            for item in os.listdir(full_path):
                item_path = os.path.join(full_path, item)
                
                if os.path.isdir(item_path):
                    directories.append({
                        'name': item,
                        'path': os.path.join(subdirectory, item) if subdirectory else item,
                        'full_path': item_path,
                        'type': 'directory'
                    })
        except Exception as e:
            print(f"Erro ao listar diretórios em {full_path}: {e}")
        
        return sorted(directories, key=lambda x: x['name'])


class Timelapse(models.Model):
    tenant = models.ForeignKey(Group, on_delete=models.CASCADE, verbose_name="Empresa")
    title = models.CharField(max_length=120, verbose_name="Título")
    video = models.FileField(upload_to='timelapses/', verbose_name="Vídeo", blank=True, null=True)
    video_path = models.CharField('Caminho do Vídeo no Servidor', max_length=500, blank=True, null=True)
    source_directory = models.ForeignKey(ServerDirectory, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="Diretório de Origem")
    source_subdirectory = models.CharField('Subdiretório de Origem', max_length=500, blank=True)
    fps = models.IntegerField('FPS', default=24)
    duration = models.FloatField('Duração (segundos)', blank=True, null=True)
    total_frames = models.IntegerField('Total de Frames', default=0)
    allowed_groups = models.ManyToManyField(Group, verbose_name='Grupos com Acesso', related_name='accessible_timelapses', blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Criado em")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="Criado por")
    
    class Meta:
        verbose_name = "Timelapse"
        verbose_name_plural = "Timelapses"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.tenant.name}"


class FrameImage(models.Model):
    """Modelo para armazenar imagens individuais dos frames"""
    timelapse = models.ForeignKey(Timelapse, on_delete=models.CASCADE, related_name='frames')
    image = models.ImageField(upload_to='frames/')
    filename = models.CharField(max_length=255)
    order = models.PositiveIntegerField(default=0)
    selected = models.BooleanField(default=False)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Frame"
        verbose_name_plural = "Frames"
        ordering = ['order', 'filename']
    
    def __str__(self):
        return f"{self.filename} - {self.timelapse.title}"


class ProcessingJob(models.Model):
    """Modelo para rastrear jobs de processamento de vídeo"""
    STATUS_CHOICES = [
        ('pending', 'Pendente'),
        ('processing', 'Processando'),
        ('completed', 'Concluído'),
        ('failed', 'Falhou'),
    ]
    
    timelapse = models.ForeignKey(Timelapse, on_delete=models.CASCADE, related_name='jobs')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    class Meta:
        verbose_name = "Job de Processamento"
        verbose_name_plural = "Jobs de Processamento"
        ordering = ['-started_at']
    
    def __str__(self):
        return f"Job {self.id} - {self.timelapse.title} ({self.status})"


class ServerConnection(models.Model):
    """Configurações de conexão com servidores remotos"""
    CONNECTION_TYPES = [
        ('ssh', 'SSH'),
        ('ftp', 'FTP'),
        ('sftp', 'SFTP'),
    ]
    
    name = models.CharField('Nome da Conexão', max_length=100)
    connection_type = models.CharField('Tipo de Conexão', max_length=10, choices=CONNECTION_TYPES, default='ssh')
    host = models.CharField('Servidor', max_length=255)
    port = models.IntegerField('Porta', default=22)
    username = models.CharField('Usuário', max_length=100)
    password = models.CharField('Senha', max_length=255, blank=True, help_text='Deixe em branco para usar chave SSH')
    private_key_path = models.CharField('Caminho da Chave SSH', max_length=500, blank=True)
    remote_path = models.CharField('Pasta Remota', max_length=500, help_text='Caminho completo da pasta com as imagens')
    is_active = models.BooleanField('Ativo', default=True)
    last_connected = models.DateTimeField('Última Conexão', null=True, blank=True)
    created_at = models.DateTimeField('Criado em', auto_now_add=True)
    updated_at = models.DateTimeField('Atualizado em', auto_now=True)
    
    # Relacionamento com o grupo (tenant)
    group = models.ForeignKey(Group, on_delete=models.CASCADE, verbose_name='Empresa')
    
    class Meta:
        verbose_name = 'Conexão de Servidor'
        verbose_name_plural = 'Conexões de Servidor'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.host}:{self.port})"
    
    def test_connection(self):
        """Testa a conexão com o servidor"""
        try:
            if self.connection_type == 'ssh':
                import paramiko
                ssh = paramiko.SSHClient()
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                if self.private_key_path:
                    key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
                    ssh.connect(self.host, port=self.port, username=self.username, pkey=key, timeout=10)
                else:
                    ssh.connect(self.host, port=self.port, username=self.username, password=self.password, timeout=10)
                
                # Testa se o diretório existe
                stdin, stdout, stderr = ssh.exec_command(f'ls -la "{self.remote_path}"')
                exit_status = stdout.channel.recv_exit_status()
                ssh.close()
                
                if exit_status == 0:
                    self.last_connected = timezone.now()
                    self.save()
                    return True, "Conexão bem-sucedida"
                else:
                    return False, f"Diretório não encontrado: {self.remote_path}"
                    
            elif self.connection_type in ['ftp', 'sftp']:
                if self.connection_type == 'ftp':
                    import ftplib
                    ftp = ftplib.FTP()
                    ftp.connect(self.host, self.port)
                    ftp.login(self.username, self.password)
                    ftp.cwd(self.remote_path)
                    ftp.quit()
                else:  # sftp
                    import paramiko
                    transport = paramiko.Transport((self.host, self.port))
                    if self.private_key_path:
                        key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
                        transport.connect(username=self.username, pkey=key)
                    else:
                        transport.connect(username=self.username, password=self.password)
                    
                    sftp = paramiko.SFTPClient.from_transport(transport)
                    sftp.listdir(self.remote_path)
                    sftp.close()
                    transport.close()
                
                self.last_connected = timezone.now()
                self.save()
                return True, "Conexão bem-sucedida"
                
        except Exception as e:
            return False, f"Erro na conexão: {str(e)}"
    
    def list_images(self, limit=100):
        """Lista as imagens disponíveis no servidor remoto"""
        try:
            images = []
            
            if self.connection_type == 'ssh':
                import paramiko
                ssh = paramiko.SSHClient()
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                if self.private_key_path:
                    key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
                    ssh.connect(self.host, port=self.port, username=self.username, pkey=key, timeout=10)
                else:
                    ssh.connect(self.host, port=self.port, username=self.username, password=self.password, timeout=10)
                
                # Lista arquivos de imagem
                cmd = f'find "{self.remote_path}" -type f \\( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" \\) | head -{limit}'
                stdin, stdout, stderr = ssh.exec_command(cmd)
                
                for line in stdout:
                    file_path = line.strip()
                    if file_path:
                        images.append({
                            'path': file_path,
                            'name': file_path.split('/')[-1],
                            'size': None  # Pode ser obtido com stat se necessário
                        })
                
                ssh.close()
                
            elif self.connection_type == 'ftp':
                import ftplib
                ftp = ftplib.FTP()
                ftp.connect(self.host, self.port)
                ftp.login(self.username, self.password)
                ftp.cwd(self.remote_path)
                
                files = []
                ftp.retrlines('LIST', files.append)
                
                for file_info in files:
                    parts = file_info.split()
                    if len(parts) >= 9:
                        filename = ' '.join(parts[8:])
                        if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                            images.append({
                                'path': f"{self.remote_path}/{filename}",
                                'name': filename,
                                'size': parts[4] if parts[4].isdigit() else None
                            })
                
                ftp.quit()
                
            elif self.connection_type == 'sftp':
                import paramiko
                transport = paramiko.Transport((self.host, self.port))
                if self.private_key_path:
                    key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
                    transport.connect(username=self.username, pkey=key)
                else:
                    transport.connect(username=self.username, password=self.password)
                
                sftp = paramiko.SFTPClient.from_transport(transport)
                files = sftp.listdir_attr(self.remote_path)
                
                for file_attr in files:
                    if file_attr.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                        images.append({
                            'path': f"{self.remote_path}/{file_attr.filename}",
                            'name': file_attr.filename,
                            'size': file_attr.st_size
                        })
                
                sftp.close()
                transport.close()
            
            return images[:limit]
            
        except Exception as e:
            return []
    
    def download_image(self, remote_path, local_path):
        """Baixa uma imagem específica do servidor remoto"""
        try:
            if self.connection_type == 'ssh':
                import paramiko
                ssh = paramiko.SSHClient()
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                if self.private_key_path:
                    key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
                    ssh.connect(self.host, port=self.port, username=self.username, pkey=key, timeout=10)
                else:
                    ssh.connect(self.host, port=self.port, username=self.username, password=self.password, timeout=10)
                
                sftp = ssh.open_sftp()
                sftp.get(remote_path, local_path)
                sftp.close()
                ssh.close()
                
            elif self.connection_type == 'ftp':
                import ftplib
                ftp = ftplib.FTP()
                ftp.connect(self.host, self.port)
                ftp.login(self.username, self.password)
                
                with open(local_path, 'wb') as f:
                    ftp.retrbinary(f'RETR {remote_path}', f.write)
                
                ftp.quit()
                
            elif self.connection_type == 'sftp':
                import paramiko
                transport = paramiko.Transport((self.host, self.port))
                if self.private_key_path:
                    key = paramiko.RSAKey.from_private_key_file(self.private_key_path)
                    transport.connect(username=self.username, pkey=key)
                else:
                    transport.connect(username=self.username, password=self.password)
                
                sftp = paramiko.SFTPClient.from_transport(transport)
                sftp.get(remote_path, local_path)
                sftp.close()
                transport.close()
            
            return True
            
        except Exception as e:
            return False

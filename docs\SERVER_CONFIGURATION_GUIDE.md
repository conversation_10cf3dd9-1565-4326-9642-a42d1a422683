# 🖥️ Guia de Configurações de Servidor - EagleView Django

## 📋 Visão Geral

O sistema EagleView Django inclui um módulo completo para conectar com servidores remotos via **SSH**, **SFTP** ou **FTP** e importar imagens diretamente para seus projetos de timelapse. Esta funcionalidade é ideal para cenários onde as imagens já estão armazenadas em servidores de produção.

## 🚀 Funcionalidades Principais

### ✅ **Tipos de Conexão Suportados**
- **SSH**: Conexão segura com execução de comandos remotos
- **SFTP**: Transferência segura de arquivos via SSH
- **FTP**: Protocolo tradicional de transferência de arquivos

### ✅ **Métodos de Autenticação**
- **Senha**: Autenticação tradicional com usuário/senha
- **Chave SSH**: Autenticação por chave pública/privada (mais segura)

### ✅ **Recursos Avançados**
- **Teste de conectividade** em tempo real
- **Navegação visual** de imagens remotas
- **Seleção múltipla** com interface intuitiva
- **Importação automática** para projetos existentes ou novos
- **Validação** de tipos de arquivo suportados
- **Multi-tenant**: Configurações isoladas por empresa/grupo

## 🛠️ Como Usar

### 1️⃣ **Acessar Configurações**
1. Faça login no sistema EagleView
2. Clique em **"Servidores"** na sidebar
3. Você verá a lista de configurações existentes

### 2️⃣ **Criar Nova Configuração**
1. Clique em **"Nova Configuração"**
2. Preencha os dados básicos:
   - **Nome**: Nome descritivo (ex: "Câmera Principal")
   - **Tipo**: SSH, SFTP ou FTP
   - **Servidor**: IP ou hostname (ex: *************)
   - **Porta**: 22 (SSH/SFTP) ou 21 (FTP)
   - **Usuário**: Nome de usuário para conexão

3. Configure a autenticação:
   - **Por Senha**: Digite a senha do usuário
   - **Por Chave SSH**: Informe o caminho da chave privada

4. Defina a pasta remota:
   - **Pasta das Imagens**: Caminho completo no servidor (ex: `/home/<USER>/images`)

5. **Teste a conexão** antes de salvar
6. Clique em **"Salvar Configuração"**

### 3️⃣ **Importar Imagens**
1. Na lista de servidores, clique em **"Navegar Imagens"**
2. O sistema listará todas as imagens encontradas na pasta configurada
3. **Selecione as imagens** desejadas (individual ou todas)
4. Clique em **"Importar Selecionadas"**
5. Escolha o destino:
   - **Novo Projeto**: Criar um projeto novo
   - **Projeto Existente**: Adicionar a um projeto existente
6. Confirme a importação

## 🔧 Configurações Técnicas

### **Requisitos do Servidor Remoto**
- **SSH/SFTP**: Servidor SSH ativo na porta 22 (ou personalizada)
- **FTP**: Servidor FTP ativo na porta 21 (ou personalizada)
- **Permissões**: Usuário deve ter acesso de leitura à pasta das imagens
- **Formatos**: Suporte a JPG, JPEG, PNG, BMP

### **Configuração de Chaves SSH**
Para usar autenticação por chave SSH:

1. **Gerar chave SSH** (se não tiver):
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```

2. **Copiar chave pública** para o servidor:
   ```bash
   ssh-copy-id -i ~/.ssh/id_rsa.pub usuario@servidor
   ```

3. **Informar caminho da chave privada** na configuração:
   - Linux/Mac: `/home/<USER>/.ssh/id_rsa`
   - Windows: `C:\Users\<USER>\.ssh\id_rsa`

### **Configurações de Rede**
- **Firewall**: Liberação das portas 22 (SSH) ou 21 (FTP)
- **VPN**: Se necessário, configure VPN antes de usar
- **DNS**: Certifique-se que o hostname resolve corretamente

## 🔐 Segurança

### **Boas Práticas**
- ✅ **Use chaves SSH** sempre que possível (mais seguro que senhas)
- ✅ **Configure usuários específicos** para o EagleView (não use root)
- ✅ **Limite permissões** apenas às pastas necessárias
- ✅ **Use SFTP em vez de FTP** quando possível
- ✅ **Configure timeout** adequado para conexões

### **Armazenamento de Credenciais**
- As **senhas são criptografadas** no banco de dados
- **Chaves SSH** são referenciadas por caminho (não armazenadas)
- **Teste regularmente** as conexões para detectar problemas

## 🚨 Solução de Problemas

### **Erro: "Conexão recusada"**
- ✅ Verifique se o servidor SSH/FTP está ativo
- ✅ Confirme se a porta está correta (22 para SSH, 21 para FTP)
- ✅ Teste conectividade: `telnet servidor porta`

### **Erro: "Autenticação falhou"**
- ✅ Verifique usuário e senha
- ✅ Para SSH: confirme se a chave está no servidor
- ✅ Teste manualmente: `ssh usuario@servidor`

### **Erro: "Diretório não encontrado"**
- ✅ Verifique se o caminho da pasta está correto
- ✅ Confirme se o usuário tem permissão de leitura
- ✅ Teste: `ls -la /caminho/da/pasta`

### **Erro: "Nenhuma imagem encontrada"**
- ✅ Verifique se há arquivos de imagem na pasta
- ✅ Confirme os formatos suportados (JPG, PNG, BMP)
- ✅ Verifique permissões de leitura dos arquivos

## 📊 Monitoramento

### **Status das Conexões**
- **Ativo/Inativo**: Status da configuração
- **Última Conexão**: Timestamp da última conexão bem-sucedida
- **Teste em Tempo Real**: Botão para testar conectividade

### **Logs e Auditoria**
- Todas as **importações são registradas**
- **Erros de conexão** são logados para diagnóstico
- **Histórico de uso** por usuário/empresa

## 🎯 Casos de Uso

### **1. Câmeras de Segurança**
```
Servidor: *************
Tipo: SSH
Pasta: /var/recordings/camera1
Uso: Importar gravações diárias automaticamente
```

### **2. Servidor de Produção**
```
Servidor: prod.empresa.com
Tipo: SFTP
Pasta: /home/<USER>/captures
Uso: Importar capturas de processo industrial
```

### **3. NAS Doméstico**
```
Servidor: nas.local
Tipo: FTP
Pasta: /volume1/photos/timelapse
Uso: Importar fotos de projetos pessoais
```

## 🔄 Fluxo de Trabalho Recomendado

1. **Configure uma vez** as conexões com seus servidores
2. **Teste regularmente** a conectividade
3. **Organize pastas** no servidor por projeto/data
4. **Importe periodicamente** as novas imagens
5. **Mantenha backups** das configurações importantes

## 📞 Suporte

Se encontrar problemas:
1. **Teste a conexão** usando as ferramentas do sistema
2. **Verifique os logs** de erro na interface
3. **Consulte este guia** para soluções comuns
4. **Teste manualmente** a conexão SSH/FTP

---

## 🎉 Conclusão

O sistema de configurações de servidor do EagleView Django oferece uma solução completa e profissional para importação de imagens remotas. Com suporte a múltiplos protocolos, autenticação segura e interface intuitiva, você pode integrar facilmente suas fontes de imagem existentes ao workflow de criação de timelapses.

**Versão**: 1.0  
**Última Atualização**: Junho 2025  
**Compatibilidade**: EagleView Django 1.0+ 
// Funcionalidades principais do EagleView
document.addEventListener('DOMContentLoaded', function() {
    
    // Toggle da sidebar melhorado
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    
    if (sidebarCollapse && sidebar) {
        // Função para toggle da sidebar
        function toggleSidebar() {
            sidebar.classList.toggle('active');
            document.body.classList.toggle('sidebar-open');
            
            if (window.innerWidth <= 768) {
                if (sidebar.classList.contains('active')) {
                    setTimeout(() => {
                        document.addEventListener('click', closeSidebarOnClickOutside);
                    }, 100);
                } else {
                    document.removeEventListener('click', closeSidebarOnClickOutside);
                }
            }
        }
        
        function closeSidebarOnClickOutside(event) {
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !sidebarCollapse.contains(event.target)) {
                sidebar.classList.remove('active');
                document.body.classList.remove('sidebar-open');
                document.removeEventListener('click', closeSidebarOnClickOutside);
            }
        }
        
        sidebarCollapse.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleSidebar();
        });
        
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebar.classList.contains('active') && window.innerWidth <= 768) {
                sidebar.classList.remove('active');
                document.body.classList.remove('sidebar-open');
                document.removeEventListener('click', closeSidebarOnClickOutside);
            }
        });
    }
    
    // Upload de arquivos com drag & drop
    const dropArea = document.getElementById('drop-area');
    const uploadForm = document.getElementById('upload-form');
    const fileInput = document.getElementById('images');
    const uploadBtn = document.getElementById('upload-btn');
    
    if (dropArea && uploadForm && fileInput && uploadBtn) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        // Atualizar botão de upload quando arquivos são selecionados
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                uploadBtn.disabled = false;
                uploadBtn.innerHTML = `<i class="fas fa-upload me-2"></i>Enviar ${this.files.length} Arquivo(s)`;
            } else {
                uploadBtn.disabled = true;
                uploadBtn.innerHTML = `<i class="fas fa-upload me-2"></i>Enviar Arquivos`;
            }
        });
    }
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        dropArea.classList.add('drag-active');
    }
    
    function unhighlight(e) {
        dropArea.classList.remove('drag-active');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = `<i class="fas fa-upload me-2"></i>Enviar ${files.length} Arquivo(s)`;
            
            // Trigger o evento change manualmente
            const event = new Event('change');
            fileInput.dispatchEvent(event);
        }
    }
    
    // Frame selection functionality
    const frameItems = document.querySelectorAll('.frame-item');
    frameItems.forEach(item => {
        item.addEventListener('click', function() {
            this.classList.toggle('selected');
            const checkbox = this.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
            }
        });
    });
    
    // Timeline controls
    const timelineSlider = document.getElementById('timeline-slider');
    if (timelineSlider) {
        timelineSlider.addEventListener('input', function() {
            const value = this.value;
            // Atualizar preview do timeline
            console.log('Timeline position:', value);
        });
    }
    
    // Auto-dismiss alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    });
    
    // Smooth scrolling para âncoras
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Responsividade da sidebar melhorada
    function handleResize() {
        if (window.innerWidth <= 768) {
            if (sidebar && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
                document.body.classList.remove('sidebar-open');
                document.removeEventListener('click', closeSidebarOnClickOutside);
            }
        } else {
            if (sidebar) {
                sidebar.classList.remove('active');
                document.body.classList.remove('sidebar-open');
                document.removeEventListener('click', closeSidebarOnClickOutside);
            }
        }
        
        const hamburgerBtn = document.getElementById('sidebarCollapse');
        if (hamburgerBtn) {
            hamburgerBtn.style.display = 'block';
        }
    }
    
    window.addEventListener('resize', handleResize);
    handleResize();
    
    // Inicializar tooltips do Bootstrap se existirem
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
});

// Funções de atualização de progresso
function updateProgress(jobId, callback) {
    fetch(`/api/job-status/${jobId}/`)
        .then(response => response.json())
        .then(data => {
            if (callback) callback(data);
            if (data.status === 'processing') {
                setTimeout(() => updateProgress(jobId, callback), 1000);
            }
        })
        .catch(error => console.error('Erro ao atualizar progresso:', error));
}

function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
} 
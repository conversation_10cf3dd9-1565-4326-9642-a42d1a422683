from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.core.files.base import ContentFile
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.db.models import Q
import subprocess
import uuid
import os
import tempfile
from PIL import Image
from .models import Timelapse, FrameImage, ProcessingJob, ServerConnection, ServerDirectory
from django.conf import settings
from datetime import timedelta
import ffmpeg
import json


def is_admin_user(user):
    """Verifica se o usuário é administrador"""
    return user.is_superuser or user.is_staff


@login_required
@user_passes_test(is_admin_user)
def server_directory_browse(request, directory_id=None, path=""):
    """Navegação de diretórios do servidor para administradores"""
    if directory_id:
        directory = get_object_or_404(ServerDirectory, id=directory_id, is_active=True)
    else:
        # Listar diretórios disponíveis
        directories = ServerDirectory.objects.filter(is_active=True)
        user_groups = request.user.groups.all()
        
        if not request.user.is_superuser:
            directories = directories.filter(
                Q(allowed_groups__in=user_groups) | Q(allowed_groups__isnull=True)
            ).distinct()
        
        return render(request, 'videos/directory_list.html', {
            'directories': directories
        })
    
    # Navegação dentro do diretório
    current_path = path.replace('..', '')  # Segurança básica
    
    # Obter conteúdo do diretório
    subdirectories = directory.get_subdirectories(current_path)
    images = directory.get_images(current_path)
    
    # Breadcrumb
    breadcrumb = []
    if current_path:
        parts = current_path.split('/')
        accumulated_path = ""
        for part in parts:
            if part:
                accumulated_path = os.path.join(accumulated_path, part)
                breadcrumb.append({
                    'name': part,
                    'path': accumulated_path
                })
    
    # Verificar se pode voltar
    parent_path = os.path.dirname(current_path) if current_path else None
    
    context = {
        'directory': directory,
        'current_path': current_path,
        'subdirectories': subdirectories,
        'images': images,
        'breadcrumb': breadcrumb,
        'parent_path': parent_path,
        'can_create_timelapse': len(images) >= 2
    }
    
    return render(request, 'videos/directory_browse.html', context)


@login_required
@user_passes_test(is_admin_user)
def create_timelapse_from_directory(request):
    """Criar timelapse a partir de imagens do diretório"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            directory_id = data.get('directory_id')
            subdirectory = data.get('subdirectory', '')
            title = data.get('title', '').strip()
            selected_images = data.get('selected_images', [])
            fps = int(data.get('fps', 24))
            
            if not title:
                return JsonResponse({'success': False, 'error': 'Título é obrigatório'})
            
            if len(selected_images) < 2:
                return JsonResponse({'success': False, 'error': 'Selecione pelo menos 2 imagens'})
            
            directory = get_object_or_404(ServerDirectory, id=directory_id)
            
            # Verificar se usuário tem acesso ao diretório
            user_groups = request.user.groups.all()
            if not request.user.is_superuser:
                if directory.allowed_groups.exists() and not directory.allowed_groups.filter(id__in=user_groups.values_list('id', flat=True)).exists():
                    return JsonResponse({'success': False, 'error': 'Sem permissão para acessar este diretório'})
            
            # Obter grupo do usuário para o timelapse
            if user_groups.exists():
                tenant = user_groups.first()
            else:
                return JsonResponse({'success': False, 'error': 'Usuário deve estar associado a um grupo'})
            
            # Criar timelapse
            timelapse = Timelapse.objects.create(
                title=title,
                tenant=tenant,
                created_by=request.user,
                source_directory=directory,
                source_subdirectory=subdirectory,
                fps=fps,
                total_frames=len(selected_images)
            )
            
            # Criar job de processamento
            job = ProcessingJob.objects.create(
                timelapse=timelapse,
                status='pending'
            )
            
            # Processar vídeo em background (você pode usar Celery ou Django-Q aqui)
            try:
                generate_timelapse_from_directory(timelapse, directory, subdirectory, selected_images, fps)
                
                job.status = 'completed'
                job.completed_at = timezone.now()
                job.save()
                
                return JsonResponse({
                    'success': True, 
                    'timelapse_id': timelapse.id,
                    'message': 'Timelapse criado com sucesso!'
                })
            
            except Exception as e:
                job.status = 'failed'
                job.error_message = str(e)
                job.save()
                
                return JsonResponse({'success': False, 'error': f'Erro ao gerar vídeo: {str(e)}'})
                
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    
    return JsonResponse({'success': False, 'error': 'Método não permitido'})


def generate_timelapse_from_directory(timelapse, directory, subdirectory, selected_images, fps):
    """Gera vídeo timelapse a partir de imagens do diretório"""
    
    # Criar diretório de saída se não existir
    output_dir = os.path.join(settings.MEDIA_ROOT, 'generated_timelapses')
    os.makedirs(output_dir, exist_ok=True)
    
    # Nome do arquivo de saída
    output_filename = f"timelapse_{timelapse.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.mp4"
    output_path = os.path.join(output_dir, output_filename)
    
    try:
        # Obter caminhos completos das imagens selecionadas
        base_path = os.path.join(directory.path, subdirectory) if subdirectory else directory.path
        image_paths = []
        
        for image_name in selected_images:
            image_path = os.path.join(base_path, image_name)
            if os.path.exists(image_path):
                image_paths.append(image_path)
        
        if not image_paths:
            raise Exception("Nenhuma imagem válida encontrada")
        
        # Criar arquivo temporário com lista de imagens
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
            for image_path in sorted(image_paths):
                temp_file.write(f"file '{image_path}'\n")
                temp_file.write(f"duration {1/fps}\n")
            
            # Última imagem para finalizar
            if image_paths:
                temp_file.write(f"file '{sorted(image_paths)[-1]}'\n")
            
            temp_list_path = temp_file.name
        
        try:
            # Executar FFmpeg
            command = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_path,
                '-vsync', 'vfr',
                '-pix_fmt', 'yuv420p',
                '-vf', f'fps={fps}',
                output_path
            ]
            
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            
            # Verificar se o arquivo foi criado
            if os.path.exists(output_path):
                # Obter informações do vídeo
                file_size = os.path.getsize(output_path)
                
                # Atualizar timelapse
                timelapse.video_path = output_path
                timelapse.duration = len(image_paths) / fps
                timelapse.save()
                
                return output_path
            else:
                raise Exception("Arquivo de vídeo não foi criado")
                
        finally:
            # Limpar arquivo temporário
            if os.path.exists(temp_list_path):
                os.unlink(temp_list_path)
                
    except subprocess.CalledProcessError as e:
        raise Exception(f"Erro no FFmpeg: {e.stderr}")
    except Exception as e:
        raise Exception(f"Erro na geração do vídeo: {str(e)}")


@login_required
def dashboard(request):
    """Dashboard principal com estatísticas"""
    user_groups = request.user.groups.all()
    
    if user_groups.exists():
        user_group = user_groups.first()
        recent_timelapses = Timelapse.objects.filter(tenant=user_group)[:5]
        total_timelapses = Timelapse.objects.filter(tenant=user_group).count()
        processing_jobs = ProcessingJob.objects.filter(
            timelapse__tenant=user_group,
            status='processing'
        ).count()
    else:
        recent_timelapses = []
        total_timelapses = 0
        processing_jobs = 0
    
    context = {
        'recent_timelapses': recent_timelapses,
        'total_timelapses': total_timelapses,
        'processing_jobs': processing_jobs,
    }
    return render(request, 'videos/dashboard.html', context)


@login_required
def timelapse_list(request):
    """Lista timelapses baseado nas permissões do usuário"""
    user_groups = request.user.groups.all()
    
    if request.user.is_superuser:
        # Superusuário vê todos os timelapses
        timelapses = Timelapse.objects.all()
    elif user_groups.exists():
        # Usuário comum vê timelapses do seu tenant ou com acesso específico
        user_group = user_groups.first()
        timelapses = Timelapse.objects.filter(
            Q(tenant=user_group) | Q(allowed_groups__in=user_groups)
        ).distinct()
    else:
        timelapses = Timelapse.objects.none()
    
    # Filtro de busca
    search = request.GET.get('search')
    if search:
        timelapses = timelapses.filter(
            Q(title__icontains=search) | Q(created_by__username__icontains=search)
        )
    
    context = {
        'timelapses': timelapses,
        'search': search or '',
    }
    return render(request, 'videos/timelapse_list.html', context)


@login_required
def timelapse_detail(request, pk):
    """Detalhes de um timelapse específico"""
    user_groups = request.user.groups.all()
    if not user_groups.exists():
        messages.error(request, 'Você não está associado a nenhuma empresa.')
        return redirect('dashboard')
    
    user_group = user_groups.first()
    timelapse = get_object_or_404(Timelapse, pk=pk, tenant=user_group)
    
    context = {
        'timelapse': timelapse,
        'frames': timelapse.frames.all(),
        'jobs': timelapse.jobs.all()[:5],
    }
    return render(request, 'videos/timelapse_detail.html', context)


@login_required
def frame_editor(request, timelapse_id=None):
    """Editor de frames para seleção e criação de timelapses"""
    user_groups = request.user.groups.all()
    if not user_groups.exists():
        messages.error(request, 'Você não está associado a nenhuma empresa.')
        return redirect('dashboard')
    
    user_group = user_groups.first()
    timelapse = None
    frames = []
    
    # Se timelapse_id foi fornecido, buscar o timelapse
    if timelapse_id:
        try:
            timelapse = get_object_or_404(Timelapse, id=timelapse_id, tenant=user_group)
            frames = timelapse.frames.all().order_by('order')
        except:
            messages.error(request, 'Timelapse não encontrado.')
            return redirect('frame_editor')
    
    if request.method == 'POST':
        if 'create_timelapse' in request.POST:
            # Criar novo timelapse
            title = request.POST.get('title', '').strip()
            if not title:
                messages.error(request, 'Título é obrigatório.')
            else:
                timelapse = Timelapse.objects.create(
                    title=title,
                    tenant=user_group,
                    created_by=request.user
                )
                messages.success(request, f'Timelapse "{title}" criado com sucesso!')
                return redirect('frame_editor_with_id', timelapse_id=timelapse.id)
        
        elif 'upload_images' in request.POST and timelapse:
            # Upload de imagens
            uploaded_files = request.FILES.getlist('images')
            if not uploaded_files:
                messages.error(request, 'Nenhuma imagem foi selecionada.')
            else:
                uploaded_count = 0
                errors = []
                
                for file in uploaded_files:
                    try:
                        # Validar se é uma imagem
                        img = Image.open(file)
                        img.verify()
                        file.seek(0)  # Reset file pointer
                        
                        # Determinar próxima ordem
                        last_frame = timelapse.frames.order_by('-order').first()
                        next_order = (last_frame.order + 1) if last_frame else 1
                        
                        FrameImage.objects.create(
                            timelapse=timelapse,
                            image=file,
                            filename=file.name,
                            order=next_order,
                            selected=True  # Por padrão, selecionar todas
                        )
                        uploaded_count += 1
                    except Exception as e:
                        errors.append(f'{file.name}: {str(e)}')
                
                if uploaded_count > 0:
                    messages.success(request, f'{uploaded_count} imagem(ns) carregada(s) com sucesso!')
                    frames = timelapse.frames.all().order_by('order')
                
                for error in errors:
                    messages.warning(request, f'Erro: {error}')
        
        elif 'update_selection' in request.POST and timelapse:
            # Atualizar seleção de frames
            selected_frames = request.POST.getlist('selected_frames')
            timelapse.frames.update(selected=False)
            if selected_frames:
                timelapse.frames.filter(id__in=selected_frames).update(selected=True)
            messages.success(request, 'Seleção de frames atualizada!')
            frames = timelapse.frames.all().order_by('order')
        
        elif 'generate_video' in request.POST and timelapse:
            # Gerar vídeo
            fps = int(request.POST.get('fps', 10))
            selected_frames = timelapse.frames.filter(selected=True).order_by('order')
            
            if selected_frames.count() < 2:
                messages.error(request, 'Selecione pelo menos 2 frames para gerar o timelapse.')
            else:
                # Criar job de processamento
                job = ProcessingJob.objects.create(
                    timelapse=timelapse,
                    status='PROCESSING',
                    started_at=timezone.now()
                )
                
                # Gerar o vídeo
                try:
                    video_path = generate_video_from_frames(selected_frames, fps)
                    
                    if video_path and os.path.exists(video_path):
                        # Salvar o vídeo no modelo
                        with open(video_path, 'rb') as video_file:
                            timelapse.video.save(
                                f'timelapse_{timelapse.id}.mp4',
                                ContentFile(video_file.read())
                            )
                        
                        # Atualizar status do job
                        job.status = 'COMPLETED'
                        job.completed_at = timezone.now()
                        job.save()
                        
                        # Limpar arquivo temporário
                        os.unlink(video_path)
                        
                        messages.success(request, 'Timelapse gerado com sucesso!')
                        return redirect('timelapse_detail', pk=timelapse.id)
                    else:
                        job.status = 'FAILED'
                        job.error_message = 'Erro ao gerar arquivo de vídeo'
                        job.completed_at = timezone.now()
                        job.save()
                        messages.error(request, 'Erro ao gerar timelapse.')
                        
                except Exception as e:
                    job.status = 'FAILED'
                    job.error_message = str(e)
                    job.completed_at = timezone.now()
                    job.save()
                    messages.error(request, f'Erro ao gerar timelapse: {str(e)}')
        
        elif 'upload_frames' in request.POST:
            return handle_frame_upload(request, user_group)
        elif 'generate_timelapse' in request.POST:
            return handle_timelapse_generation(request, user_group)
    
    # Buscar timelapses recentes para seleção
    recent_timelapses = Timelapse.objects.filter(
        tenant=user_group
    ).order_by('-created_at')[:10]
    
    context = {
        'timelapse': timelapse,
        'frames': frames,
        'recent_timelapses': recent_timelapses,
        'page_title': f'Editor de Frames - {timelapse.title}' if timelapse else 'Editor de Frames'
    }
    return render(request, 'videos/frame_editor.html', context)


def handle_frame_upload(request, user_group):
    """Processa upload de frames"""
    uploaded_files = request.FILES.getlist('frames')
    
    if not uploaded_files:
        messages.error(request, 'Nenhum arquivo foi selecionado.')
        return redirect('frame_editor')
    
    # Criar um novo timelapse temporário
    timelapse = Timelapse.objects.create(
        tenant=user_group,
        title=f"Projeto {timezone.now().strftime('%Y-%m-%d %H:%M')}",
        created_by=request.user
    )
    
    uploaded_count = 0
    for i, uploaded_file in enumerate(uploaded_files):
        try:
            # Validar se é uma imagem
            img = Image.open(uploaded_file)
            img.verify()
            
            # Resetar o ponteiro do arquivo
            uploaded_file.seek(0)
            
            frame = FrameImage.objects.create(
                timelapse=timelapse,
                image=uploaded_file,
                filename=uploaded_file.name,
                order=i,
                selected=True  # Selecionar por padrão
            )
            uploaded_count += 1
        except Exception as e:
            messages.warning(request, f'Erro ao processar {uploaded_file.name}: {str(e)}')
    
    messages.success(request, f'{uploaded_count} frames foram enviados com sucesso.')
    return redirect('timelapse_detail', pk=timelapse.pk)


def handle_timelapse_generation(request, user_group):
    """Processa geração de timelapse"""
    selected_frame_ids = request.POST.getlist('selected_frames')
    title = request.POST.get('title', f'Timelapse {timezone.now().strftime("%Y-%m-%d %H:%M")}')
    fps = int(request.POST.get('fps', 24))
    
    if not selected_frame_ids:
        messages.error(request, 'Selecione pelo menos um frame.')
        return redirect('frame_editor')
    
    try:
        # Criar o timelapse
        timelapse = Timelapse.objects.create(
            tenant=user_group,
            title=title,
            created_by=request.user
        )
        
        # Criar job de processamento
        job = ProcessingJob.objects.create(
            timelapse=timelapse,
            status='pending'
        )
        
        # Processar frames selecionados
        frames = FrameImage.objects.filter(id__in=selected_frame_ids).order_by('order')
        
        # Gerar o vídeo
        video_path = generate_video_from_frames(frames, fps)
        
        if video_path:
            # Salvar o vídeo no modelo
            with open(video_path, 'rb') as video_file:
                timelapse.video.save(
                    f'timelapse_{timelapse.id}.mp4',
                    ContentFile(video_file.read())
                )
            
            # Atualizar status do job
            job.status = 'completed'
            job.completed_at = timezone.now()
            job.save()
            
            # Limpar arquivo temporário
            os.unlink(video_path)
            
            messages.success(request, 'Timelapse gerado com sucesso!')
            return redirect('timelapse_detail', pk=timelapse.pk)
        else:
            job.status = 'failed'
            job.error_message = 'Erro na geração do vídeo'
            job.save()
            messages.error(request, 'Erro ao gerar o timelapse.')
    
    except Exception as e:
        messages.error(request, f'Erro ao processar: {str(e)}')
    
    return redirect('frame_editor')


def generate_video_from_frames(frames, fps=24):
    """Gera vídeo usando FFmpeg a partir dos frames"""
    if not frames or frames.count() == 0:
        print("Nenhum frame fornecido")
        return None
    
    temp_dir = None
    try:
        # Criar diretório temporário com permissões adequadas
        temp_dir = tempfile.mkdtemp()
        os.chmod(temp_dir, 0o755)  # Garantir permissões de leitura/escrita
        print(f"Diretório temporário criado: {temp_dir}")
        
        frame_count = 0
        # Processar frames sequencialmente
        for i, frame in enumerate(frames):
            if not frame.image:
                continue
                
            try:
                src_path = frame.image.path
                dst_path = os.path.join(temp_dir, f'frame_{i:05d}.jpg')
                
                # Abrir e processar imagem
                with Image.open(src_path) as img:
                    # Converter para RGB se necessário (remove transparência)
                    if img.mode in ('RGBA', 'LA', 'P'):
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        if img.mode == 'RGBA':
                            background.paste(img, mask=img.split()[-1])
                        else:
                            background.paste(img)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Redimensionar mantendo proporção para 1920x1080
                    target_width, target_height = 1920, 1080
                    img_ratio = img.width / img.height
                    target_ratio = target_width / target_height
                    
                    if img_ratio > target_ratio:
                        # Imagem mais larga, ajustar por altura
                        new_height = target_height
                        new_width = int(new_height * img_ratio)
                    else:
                        # Imagem mais alta, ajustar por largura
                        new_width = target_width
                        new_height = int(new_width / img_ratio)
                    
                    # Redimensionar
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    
                    # Criar canvas 1920x1080 e centralizar imagem
                    canvas = Image.new('RGB', (target_width, target_height), (0, 0, 0))
                    paste_x = (target_width - new_width) // 2
                    paste_y = (target_height - new_height) // 2
                    canvas.paste(img, (paste_x, paste_y))
                    
                    # Salvar frame processado
                    canvas.save(dst_path, 'JPEG', quality=85, optimize=True)
                    # Garantir permissões de leitura
                    os.chmod(dst_path, 0o644)
                    frame_count += 1
                    
            except Exception as e:
                print(f"Erro ao processar frame {i}: {str(e)}")
                continue
        
        if frame_count == 0:
            print("Nenhum frame válido processado")
            return None
        
        print(f"Processados {frame_count} frames válidos")
        
        # Gerar vídeo com FFmpeg
        output_filename = f'timelapse_{uuid.uuid4().hex}.mp4'
        output_path = os.path.join(tempfile.gettempdir(), output_filename)
        
        # Comando FFmpeg otimizado com parâmetros ajustados
        cmd = [
            'ffmpeg',
            '-y',  # Sobrescrever arquivo
            '-framerate', str(fps),
            '-i', os.path.join(temp_dir, 'frame_%05d.jpg'),
            '-c:v', 'libx264',
            '-preset', 'fast',  # Usar preset mais rápido
            '-crf', '23',  # Qualidade constante
            '-pix_fmt', 'yuv420p',  # Compatibilidade máxima
            '-movflags', '+faststart',  # Otimização para web
            '-vf', 'format=yuv420p',  # Forçar formato de pixel
            output_path
        ]
        
        print(f"Executando FFmpeg: {' '.join(cmd)}")
        
        # Criar ambiente com variáveis de ambiente necessárias
        env = os.environ.copy()
        env['FFREPORT'] = f'file={temp_dir}/ffmpeg_report.log:level=32'
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300,
            env=env
        )
        
        # Verificar se há log de erro do FFmpeg
        if os.path.exists(os.path.join(temp_dir, 'ffmpeg_report.log')):
            with open(os.path.join(temp_dir, 'ffmpeg_report.log'), 'r') as f:
                print("Log FFmpeg:", f.read())
        
        if result.returncode == 0:
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                print(f"Vídeo gerado com sucesso: {output_path} ({os.path.getsize(output_path)} bytes)")
                return output_path
            else:
                print("Arquivo de vídeo não foi criado ou está vazio")
                return None
        else:
            print(f"Erro FFmpeg (código {result.returncode}):")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return None
    
    except subprocess.TimeoutExpired:
        print("Timeout: FFmpeg demorou mais de 5 minutos")
        return None
    except Exception as e:
        print(f"Erro geral ao gerar vídeo: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Limpar diretório temporário
        if temp_dir and os.path.exists(temp_dir):
            try:
                import shutil
                shutil.rmtree(temp_dir)
                print(f"Diretório temporário removido: {temp_dir}")
            except Exception as e:
                print(f"Erro ao limpar diretório temporário: {str(e)}")


def generate_timelapse_sync(timelapse, fps):
    """Versão síncrona da geração de timelapse para fallback"""
    try:
        selected_frames = timelapse.frames.filter(selected=True).order_by('order')
        
        if selected_frames.count() < 2:
            print("Não há frames suficientes selecionados")
            return False
        
        video_path = generate_video_from_frames(selected_frames, fps)
        
        if video_path and os.path.exists(video_path):
            # Salvar o vídeo no modelo
            with open(video_path, 'rb') as video_file:
                timelapse.video.save(
                    f'timelapse_{timelapse.id}.mp4',
                    ContentFile(video_file.read())
                )
            
            # Limpar arquivo temporário
            os.unlink(video_path)
            print("Timelapse gerado e salvo com sucesso")
            return True
        
        print("Falha na geração do vídeo")
        return False
        
    except Exception as e:
        print(f"Erro na geração síncrona: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


@login_required
def job_status(request, job_id):
    """API endpoint para verificar status de um job de processamento"""
    try:
        job = ProcessingJob.objects.get(id=job_id, timelapse__tenant=request.user.groups.first())
        return JsonResponse({
            'status': job.status,
            'progress': 100 if job.status == 'COMPLETED' else (50 if job.status == 'PROCESSING' else 0),
            'error_message': job.error_message,
            'completed_at': job.completed_at.isoformat() if job.completed_at else None,
            'video_url': job.timelapse.video.url if job.timelapse.video and job.status == 'COMPLETED' else None
        })
    except ProcessingJob.DoesNotExist:
        return JsonResponse({'error': 'Job não encontrado'}, status=404)


@login_required
@csrf_exempt
def delete_timelapse(request, pk):
    """Deleta um timelapse"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    user_groups = request.user.groups.all()
    if not user_groups.exists():
        return JsonResponse({'error': 'Unauthorized'}, status=401)
    
    user_group = user_groups.first()
    
    try:
        timelapse = Timelapse.objects.get(pk=pk, tenant=user_group)
        title = timelapse.title
        timelapse.delete()
        return JsonResponse({'success': True, 'message': f'Timelapse "{title}" deletado com sucesso.'})
    except Timelapse.DoesNotExist:
        return JsonResponse({'error': 'Timelapse not found'}, status=404)


@login_required
def server_settings(request):
    """View para gerenciar configurões de servidor"""
    user_groups = request.user.groups.all()
    servers = ServerConnection.objects.filter(group__in=user_groups)
    
    context = {
        'servers': servers,
        'page_title': 'Configurações de Servidor'
    }
    return render(request, 'videos/server_settings.html', context)


@login_required
def server_create(request):
    """View para criar nova configuração de servidor"""
    if request.method == 'POST':
        try:
            # Validar dados do formulário
            name = request.POST.get('name')
            connection_type = request.POST.get('connection_type')
            host = request.POST.get('host')
            port = int(request.POST.get('port', 22))
            username = request.POST.get('username')
            password = request.POST.get('password', '')
            private_key_path = request.POST.get('private_key_path', '')
            remote_path = request.POST.get('remote_path')
            
            # Obter o primeiro grupo do usuário
            user_group = request.user.groups.first()
            if not user_group:
                messages.error(request, 'Usuário deve estar associado a um grupo.')
                return redirect('server_settings')
            
            # Criar conexão
            server = ServerConnection.objects.create(
                name=name,
                connection_type=connection_type,
                host=host,
                port=port,
                username=username,
                password=password,
                private_key_path=private_key_path,
                remote_path=remote_path,
                group=user_group
            )
            
            messages.success(request, f'Servidor "{name}" criado com sucesso!')
            return redirect('server_settings')
            
        except Exception as e:
            messages.error(request, f'Erro ao criar servidor: {str(e)}')
    
    context = {
        'page_title': 'Nova Configuração de Servidor'
    }
    return render(request, 'videos/server_create.html', context)


@login_required
def server_edit(request, server_id):
    """View para editar configuração de servidor"""
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    if request.method == 'POST':
        try:
            server.name = request.POST.get('name')
            server.connection_type = request.POST.get('connection_type')
            server.host = request.POST.get('host')
            server.port = int(request.POST.get('port', 22))
            server.username = request.POST.get('username')
            server.password = request.POST.get('password', '')
            server.private_key_path = request.POST.get('private_key_path', '')
            server.remote_path = request.POST.get('remote_path')
            server.is_active = request.POST.get('is_active') == 'on'
            
            server.save()
            messages.success(request, f'Servidor "{server.name}" atualizado com sucesso!')
            return redirect('server_settings')
            
        except Exception as e:
            messages.error(request, f'Erro ao atualizar servidor: {str(e)}')
    
    context = {
        'server': server,
        'page_title': f'Editar {server.name}'
    }
    return render(request, 'videos/server_edit.html', context)


@login_required
def server_delete(request, server_id):
    """View para deletar configuração de servidor"""
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    if request.method == 'POST':
        server_name = server.name
        server.delete()
        messages.success(request, f'Servidor "{server_name}" removido com sucesso!')
    
    return redirect('server_settings')


@login_required
def server_test(request, server_id):
    """View para testar conexão com servidor"""
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    try:
        success, message = server.test_connection()
        if success:
            messages.success(request, f'✅ {message}')
        else:
            messages.error(request, f'❌ {message}')
    except Exception as e:
        messages.error(request, f'❌ Erro inesperado: {str(e)}')
    
    return redirect('server_settings')


@login_required
def server_browse(request, server_id):
    """View para navegar e selecionar imagens do servidor remoto"""
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    try:
        images = server.list_images(limit=50)
        
        context = {
            'server': server,
            'images': images,
            'page_title': f'Imagens - {server.name}'
        }
        return render(request, 'videos/server_browse.html', context)
        
    except Exception as e:
        messages.error(request, f'Erro ao listar imagens: {str(e)}')
        return redirect('server_settings')


@login_required
def server_import_images(request, server_id):
    """View para importar imagens selecionadas do servidor remoto"""
    if request.method != 'POST':
        return redirect('server_settings')
    
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    try:
        selected_images = request.POST.getlist('selected_images')
        timelapse_id = request.POST.get('timelapse_id')
        
        if not selected_images:
            messages.warning(request, 'Nenhuma imagem foi selecionada.')
            return redirect('server_browse', server_id=server_id)
        
        # Criar ou obter timelapse
        if timelapse_id:
            timelapse = get_object_or_404(Timelapse, id=timelapse_id, tenant__in=user_groups)
        else:
            timelapse_name = request.POST.get('timelapse_name', f'Importado de {server.name}')
            timelapse = Timelapse.objects.create(
                title=timelapse_name,
                tenant=user_groups.first(),
                created_by=request.user
            )
        
        # Importar imagens
        imported_count = 0
        for remote_path in selected_images:
            try:
                # Gerar nome do arquivo local
                filename = remote_path.split('/')[-1]
                local_path = os.path.join(settings.MEDIA_ROOT, 'frames', filename)
                
                # Criar diretório se não existir
                os.makedirs(os.path.dirname(local_path), exist_ok=True)
                
                # Baixar imagem
                if server.download_image(remote_path, local_path):
                    # Criar objeto FrameImage
                    with open(local_path, 'rb') as f:
                        frame = FrameImage.objects.create(
                            timelapse=timelapse,
                            filename=filename,
                            order=imported_count + 1
                        )
                        frame.image.save(filename, ContentFile(f.read()))
                    
                    imported_count += 1
                else:
                    messages.warning(request, f'Falha ao baixar: {filename}')
                    
            except Exception as e:
                messages.warning(request, f'Erro ao importar {filename}: {str(e)}')
        
        if imported_count > 0:
            messages.success(request, f'✅ {imported_count} imagens importadas com sucesso para "{timelapse.title}"!')
            return redirect('timelapse_detail', timelapse_id=timelapse.id)
        else:
            messages.error(request, 'Nenhuma imagem foi importada.')
            return redirect('server_browse', server_id=server_id)
            
    except Exception as e:
        messages.error(request, f'Erro durante importação: {str(e)}')
        return redirect('server_browse', server_id=server_id)


from django.http import JsonResponse

@login_required
def server_api_test(request, server_id):
    """API endpoint para testar conexão via AJAX"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Método não permitido'})
    
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    try:
        success, message = server.test_connection()
        return JsonResponse({
            'success': success,
            'message': message
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Erro inesperado: {str(e)}'
        })


@login_required
def server_api_list_images(request, server_id):
    """API endpoint para listar imagens via AJAX"""
    user_groups = request.user.groups.all()
    server = get_object_or_404(ServerConnection, id=server_id, group__in=user_groups)
    
    try:
        images = server.list_images(limit=100)
        return JsonResponse({
            'success': True,
            'images': images
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Erro ao listar imagens: {str(e)}'
        })


@login_required
def preview_timelapse(request, timelapse_id):
    """View para gerar preview rápido do timelapse"""
    try:
        timelapse = Timelapse.objects.get(id=timelapse_id, tenant=request.user.groups.first())
        
        if request.method == 'POST':
            # Gerar preview com menos frames para velocidade
            frames = timelapse.frames.filter(selected=True).order_by('order')[:20]  # Máximo 20 frames
            
            if frames.count() < 2:
                return JsonResponse({'error': 'Selecione pelo menos 2 frames'}, status=400)
            
            try:
                # Criar um preview mais rápido
                import tempfile
                import ffmpeg
                
                with tempfile.TemporaryDirectory() as temp_dir:
                    # Copiar apenas os frames selecionados
                    frame_paths = []
                    for i, frame in enumerate(frames):
                        frame_path = os.path.join(temp_dir, f"frame_{i:04d}.jpg")
                        with open(frame_path, 'wb') as f:
                            frame.image.seek(0)
                            f.write(frame.image.read())
                        frame_paths.append(frame_path)
                    
                    # Gerar preview menor e mais rápido
                    preview_path = os.path.join(settings.MEDIA_ROOT, 'previews', f'preview_{timelapse.id}.mp4')
                    os.makedirs(os.path.dirname(preview_path), exist_ok=True)
                    
                    # Input pattern para ffmpeg
                    input_pattern = os.path.join(temp_dir, "frame_%04d.jpg")
                    
                    # Comando ffmpeg otimizado para preview
                    (
                        ffmpeg
                        .input(input_pattern, framerate=8)  # 8 FPS para preview rápido
                        .output(
                            preview_path,
                            vcodec='libx264',
                            crf=28,  # Qualidade menor para speed
                            preset='ultrafast',  # Preset mais rápido
                            vf='scale=640:360',  # Resolução menor
                            pix_fmt='yuv420p'
                        )
                        .overwrite_output()
                        .run(capture_stdout=True, capture_stderr=True)
                    )
                
                # Retornar URL do preview
                preview_url = f'/media/previews/preview_{timelapse.id}.mp4'
                return JsonResponse({
                    'success': True,
                    'preview_url': preview_url,
                    'frame_count': frames.count()
                })
                
            except Exception as e:
                return JsonResponse({'error': f'Erro ao gerar preview: {str(e)}'}, status=500)
        
        return JsonResponse({'error': 'Método não permitido'}, status=405)
        
    except Timelapse.DoesNotExist:
        return JsonResponse({'error': 'Timelapse não encontrado'}, status=404)


@login_required 
def notifications_status(request):
    """API endpoint para notificações em tempo real"""
    user_group = request.user.groups.first()
    if not user_group:
        return JsonResponse({'notifications': []})
    
    # Buscar jobs em andamento dos últimos 30 minutos
    recent_jobs = ProcessingJob.objects.filter(
        timelapse__tenant=user_group,
        started_at__gte=timezone.now() - timedelta(minutes=30)
    ).order_by('-started_at')[:10]
    
    notifications = []
    for job in recent_jobs:
        notification = {
            'id': job.id,
            'timelapse_id': job.timelapse.id,
            'timelapse_title': job.timelapse.title,
            'status': job.status,
            'started_at': job.started_at.isoformat(),
            'completed_at': job.completed_at.isoformat() if job.completed_at else None,
            'error_message': job.error_message
        }
        notifications.append(notification)
    
    return JsonResponse({'notifications': notifications})

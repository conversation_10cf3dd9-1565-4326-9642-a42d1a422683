/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        eagle: {
          orange: '#FF6B00',
          dark: '#333333',
          light: '#E5E5E5',
          white: '#FFFFFF',
        },
        primary: {
          DEFAULT: '#2563EB', // Azul principal
          dark: '#1E40AF',    // Versão mais escura para hover
          light: '#60A5FA',   // Versão mais clara
        },
        success: {
          DEFAULT: '#10B981', // Verde
        },
      },
      animation: {
        'ripple': 'ripple 0.6s linear forwards',
      },
      keyframes: {
        ripple: {
          '0%': { opacity: '0.7', transform: 'scale(0)' },
          '100%': { opacity: '0', transform: 'scale(2)' },
        },
      },
    },
  },
  plugins: [],
} 
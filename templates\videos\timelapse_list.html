{% extends 'base.html' %}

{% block title %}Projetos - EagleView{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item active">Projetos</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <div>
                <h1 class="mb-2 d-flex align-items-center">
                    <i class="fas fa-folder me-3 text-primary"></i>
                    Meus Projetos
                </h1>
                <p class="text-muted mb-0">Gerencie todos os seus timelapses em um só lugar</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'frame_editor' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Novo Projeto
                </a>
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filterModal">
                    <i class="fas fa-filter me-2"></i>
                    Filtros
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filtros e Busca Modernos -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card search-card">
            <div class="card-body">
                <form method="get" class="row g-4 align-items-end">
                    <div class="col-lg-6 col-md-8">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-search me-2 text-primary"></i>
                            Buscar Projetos
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" 
                                   class="form-control" 
                                   name="search" 
                                   value="{{ search }}"
                                   placeholder="Digite o título ou nome do criador...">
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>
                                Buscar
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-12">
                        <div class="d-grid">
                            <a href="{% url 'timelapse_list' %}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-refresh me-2"></i>
                                Limpar Filtros
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Timelapses -->
<div class="row">
    <div class="col-12">
        {% if timelapses %}
            <div class="row g-4">
                {% for timelapse in timelapses %}
                <div class="col-xl-4 col-lg-6 col-md-6 mb-4">
                    <div class="card h-100 project-card">
                        {% if timelapse.video %}
                            <div class="project-thumbnail position-relative" style="height: 220px; overflow: hidden;">
                                <video class="w-100 h-100 rounded-top video-preview" style="object-fit: cover;" muted preload="metadata">
                                    <source src="{{ timelapse.video.url }}" type="video/mp4">
                                </video>
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check me-1"></i>
                                        Concluído
                                    </span>
                                </div>
                                <div class="position-absolute bottom-0 start-0 m-3">
                                    <span class="badge bg-dark bg-opacity-75">
                                        <i class="fas fa-play me-1"></i>
                                        Vídeo
                                    </span>
                                </div>
                            </div>
                        {% else %}
                            <div class="project-thumbnail d-flex align-items-center justify-content-center bg-gradient-secondary rounded-top" style="height: 220px;">
                                <div class="text-center">
                                    {% if timelapse.frames.count > 0 %}
                                        <div class="spinner-border text-warning mb-3" role="status">
                                            <span class="visually-hidden">Processando...</span>
                                        </div>
                                        <h6 class="text-light">Processando Vídeo</h6>
                                        <p class="text-muted small mb-0">{{ timelapse.frames.count }} frames carregados</p>
                                    {% else %}
                                        <i class="fas fa-upload fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">Aguardando Frames</h6>
                                        <p class="text-muted small mb-0">Faça upload das imagens</p>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title fw-bold mb-3">{{ timelapse.title }}</h5>
                            
                            <div class="project-meta mb-4">
                                <div class="row g-2">
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar text-primary me-2"></i>
                                            <small class="text-muted">{{ timelapse.created_at|date:"d/m/Y H:i" }}</small>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-user text-info me-2"></i>
                                            <small class="text-muted">{{ timelapse.created_by.get_full_name|default:timelapse.created_by.username }}</small>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-images text-warning me-2"></i>
                                                <small class="text-muted">{{ timelapse.frames.count }} frame{{ timelapse.frames.count|pluralize }}</small>
                                            </div>
                                            {% if timelapse.video %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    Pronto
                                                </span>
                                            {% elif timelapse.frames.count > 0 %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>
                                                    Processando
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-upload me-1"></i>
                                                    Aguardando
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-auto">
                                <div class="d-grid gap-2 mb-2">
                                    <a href="{% url 'timelapse_detail' timelapse.pk %}" class="btn btn-primary">
                                        <i class="fas fa-eye me-2"></i>
                                        Ver Detalhes
                                    </a>
                                </div>
                                <div class="row g-2">
                                    {% if timelapse.frames.count >= 2 %}
                                        <div class="col-4">
                                            <button class="btn btn-outline-info btn-sm w-100 btn-preview" 
                                                    data-timelapse-id="{{ timelapse.pk }}"
                                                    title="Preview Rápido">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </div>
                                    {% endif %}
                                    {% if timelapse.video %}
                                        <div class="col-4">
                                            <a href="{{ timelapse.video.url }}" download="{{ timelapse.title }}.mp4" class="btn btn-outline-secondary btn-sm w-100" title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    {% endif %}
                                    <div class="col-4">
                                        <button class="btn btn-outline-danger btn-sm w-100" 
                                                onclick="deleteTimelapse({{ timelapse.pk }}, '{{ timelapse.title }}')"
                                                title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Paginação (para futura implementação) -->
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <p class="text-muted">{{ timelapses.count }} projeto{{ timelapses.count|pluralize }} encontrado{{ timelapses.count|pluralize }}</p>
                </div>
            </div>
        {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    {% if search %}
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum projeto encontrado</h5>
                        <p class="text-muted">Não foram encontrados projetos com o termo "{{ search }}".</p>
                        <a href="{% url 'timelapse_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Ver Todos os Projetos
                        </a>
                    {% else %}
                        <i class="fas fa-video fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum projeto criado ainda</h5>
                        <p class="text-muted">Comece criando seu primeiro timelapse!</p>
                        <a href="{% url 'frame_editor' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Criar Primeiro Projeto
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de Confirmação de Exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash me-2"></i>
                    Confirmar Exclusão
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o projeto <strong id="deleteTitle"></strong>?</p>
                <p class="text-muted small">Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-1"></i>
                    Excluir
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let deleteTimelapseId = null;

function deleteTimelapse(id, title) {
    deleteTimelapseId = id;
    document.getElementById('deleteTitle').textContent = title;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (deleteTimelapseId) {
        fetch(`/api/delete/${deleteTimelapseId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro ao excluir o projeto: ' + data.error);
            }
        })
        .catch(error => {
            alert('Erro ao excluir o projeto');
        });
        
        bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
    }
});

// Adicionar hover effect nos vídeos
document.querySelectorAll('video').forEach(video => {
    video.addEventListener('mouseenter', function() {
        this.play();
    });
    
    video.addEventListener('mouseleave', function() {
        this.pause();
        this.currentTime = 0;
    });
});
</script>
{% endblock %} 
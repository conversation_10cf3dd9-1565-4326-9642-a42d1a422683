{% extends 'base.html' %}
{% load static %}

{% block title %}Editar {{ server.name }} - EagleView{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-section h5 {
        color: var(--highlight-orange);
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--border-primary);
    }
    
    .form-control, .form-select {
        background: var(--surface-tertiary);
        border: 1px solid var(--border-primary);
        color: var(--text-primary);
    }
    
    .form-control:focus, .form-select:focus {
        background: var(--surface-tertiary);
        border-color: var(--highlight-orange);
        box-shadow: 0 0 0 0.2rem rgba(255, 106, 0, 0.25);
        color: var(--text-primary);
    }
    
    .form-label {
        color: var(--text-primary);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .form-text {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }
    
    .connection-preview {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-primary);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .test-connection-area {
        background: var(--surface-tertiary);
        border: 2px dashed var(--border-primary);
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        margin-top: 1rem;
    }
    
    .auth-method-toggle {
        display: flex;
        background: var(--surface-tertiary);
        border-radius: 8px;
        padding: 0.25rem;
        margin-bottom: 1rem;
    }
    
    .auth-method-toggle button {
        flex: 1;
        background: transparent;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        color: var(--text-secondary);
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .auth-method-toggle button.active {
        background: var(--highlight-orange);
        color: white;
    }
    
    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 1rem;
    }
    
    .status-indicator.active {
        background: rgba(35, 134, 54, 0.2);
        color: var(--success);
        border: 1px solid var(--success);
    }
    
    .status-indicator.inactive {
        background: rgba(218, 54, 51, 0.2);
        color: var(--danger);
        border: 1px solid var(--danger);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <!-- Cabeçalho -->
            <div class="d-flex align-items-center mb-4">
                <a href="{% url 'server_settings' %}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-edit me-2 text-primary"></i>
                        Editar {{ server.name }}
                    </h1>
                    <p class="text-muted mb-0">Modifique as configurações de conexão</p>
                </div>
            </div>

            <!-- Status da Conexão -->
            <div class="status-indicator {% if server.is_active %}active{% else %}inactive{% endif %}">
                <i class="fas fa-circle"></i>
                {% if server.is_active %}
                    Configuração Ativa
                    {% if server.last_connected %}
                        - Última conexão: {{ server.last_connected|date:"d/m/Y H:i" }}
                    {% endif %}
                {% else %}
                    Configuração Inativa
                {% endif %}
            </div>

            <form method="post" id="serverForm">
                {% csrf_token %}
                
                <!-- Informações Básicas -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>Informações Básicas</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nome da Configuração</label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       value="{{ server.name }}">
                                <div class="form-text">Nome descritivo para identificar esta configuração</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="connection_type" class="form-label">Tipo de Conexão</label>
                                <select class="form-select" id="connection_type" name="connection_type" required>
                                    <option value="ssh" {% if server.connection_type == 'ssh' %}selected{% endif %}>SSH</option>
                                    <option value="sftp" {% if server.connection_type == 'sftp' %}selected{% endif %}>SFTP</option>
                                    <option value="ftp" {% if server.connection_type == 'ftp' %}selected{% endif %}>FTP</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">Status</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if server.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        Ativo
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configurações de Conexão -->
                <div class="form-section">
                    <h5><i class="fas fa-network-wired me-2"></i>Configurações de Conexão</h5>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="host" class="form-label">Servidor</label>
                                <input type="text" class="form-control" id="host" name="host" required
                                       value="{{ server.host }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="port" class="form-label">Porta</label>
                                <input type="number" class="form-control" id="port" name="port" 
                                       value="{{ server.port }}" min="1" max="65535">
                                <div class="form-text">SSH: 22, FTP: 21</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Usuário</label>
                        <input type="text" class="form-control" id="username" name="username" required
                               value="{{ server.username }}">
                    </div>
                </div>

                <!-- Autenticação -->
                <div class="form-section">
                    <h5><i class="fas fa-key me-2"></i>Autenticação</h5>
                    
                    <div class="auth-method-toggle" id="authToggle">
                        <button type="button" {% if not server.private_key_path %}class="active"{% endif %} data-method="password">
                            <i class="fas fa-lock me-2"></i>Senha
                        </button>
                        <button type="button" {% if server.private_key_path %}class="active"{% endif %} data-method="key">
                            <i class="fas fa-key me-2"></i>Chave SSH
                        </button>
                    </div>

                    <div id="passwordAuth" {% if server.private_key_path %}style="display: none;"{% endif %}>
                        <div class="mb-3">
                            <label for="password" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Digite a nova senha ou deixe em branco para manter">
                            <div class="form-text">Deixe em branco para manter a senha atual</div>
                        </div>
                    </div>

                    <div id="keyAuth" {% if not server.private_key_path %}style="display: none;"{% endif %}>
                        <div class="mb-3">
                            <label for="private_key_path" class="form-label">Caminho da Chave SSH</label>
                            <input type="text" class="form-control" id="private_key_path" name="private_key_path"
                                   value="{{ server.private_key_path }}">
                            <div class="form-text">Caminho completo para o arquivo de chave privada SSH</div>
                        </div>
                    </div>
                </div>

                <!-- Configurações Remotas -->
                <div class="form-section">
                    <h5><i class="fas fa-folder me-2"></i>Configurações Remotas</h5>
                    
                    <div class="mb-3">
                        <label for="remote_path" class="form-label">Pasta das Imagens</label>
                        <input type="text" class="form-control" id="remote_path" name="remote_path" required
                               value="{{ server.remote_path }}">
                        <div class="form-text">Caminho completo da pasta que contém as imagens no servidor</div>
                    </div>

                    <!-- Preview da Conexão -->
                    <div class="connection-preview">
                        <h6 class="mb-2">Preview da Conexão:</h6>
                        <code id="connectionString">{{ server.connection_type }} {{ server.username }}@{{ server.host }}:{{ server.port }}</code>
                    </div>
                </div>

                <!-- Teste de Conexão -->
                <div class="form-section">
                    <h5><i class="fas fa-plug me-2"></i>Teste de Conexão</h5>
                    
                    <div class="test-connection-area">
                        <p class="text-muted mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Teste a conexão para verificar se as configurações estão corretas
                        </p>
                        <button type="button" class="btn btn-outline-success" onclick="testConnection({{ server.id }})">
                            <i class="fas fa-plug me-2"></i>
                            Testar Conexão
                        </button>
                        <div id="testResult" class="mt-3" style="display: none;"></div>
                    </div>
                </div>

                <!-- Botões de Ação -->
                <div class="d-flex gap-3 justify-content-end">
                    <a href="{% url 'server_settings' %}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('serverForm');
    const authToggle = document.getElementById('authToggle');
    const passwordAuth = document.getElementById('passwordAuth');
    const keyAuth = document.getElementById('keyAuth');
    
    // Toggle de autenticação
    authToggle.addEventListener('click', function(e) {
        if (e.target.closest('button')) {
            const method = e.target.closest('button').dataset.method;
            
            // Atualizar botões ativos
            authToggle.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
            e.target.closest('button').classList.add('active');
            
            // Mostrar/ocultar campos
            if (method === 'password') {
                passwordAuth.style.display = 'block';
                keyAuth.style.display = 'none';
                document.getElementById('private_key_path').value = '';
            } else {
                passwordAuth.style.display = 'none';
                keyAuth.style.display = 'block';
                document.getElementById('password').value = '';
            }
        }
    });
    
    // Atualizar preview da conexão
    function updateConnectionPreview() {
        const type = document.getElementById('connection_type').value;
        const username = document.getElementById('username').value || 'usuario';
        const host = document.getElementById('host').value || 'servidor';
        const port = document.getElementById('port').value || '22';
        
        let connectionString = '';
        if (type === 'ssh') {
            connectionString = `ssh ${username}@${host}:${port}`;
        } else if (type === 'sftp') {
            connectionString = `sftp ${username}@${host}:${port}`;
        } else {
            connectionString = `ftp ${username}@${host}:${port}`;
        }
        
        document.getElementById('connectionString').textContent = connectionString;
    }
    
    // Atualizar porta padrão baseada no tipo
    document.getElementById('connection_type').addEventListener('change', function() {
        const type = this.value;
        const portField = document.getElementById('port');
        
        if (type === 'ftp') {
            portField.value = '21';
        } else {
            portField.value = '22';
        }
        
        updateConnectionPreview();
    });
    
    // Atualizar preview quando campos mudam
    ['username', 'host', 'port'].forEach(field => {
        document.getElementById(field).addEventListener('input', updateConnectionPreview);
    });
    
    // Inicializar preview
    updateConnectionPreview();
});

function testConnection(serverId) {
    const button = event.target;
    const originalContent = button.innerHTML;
    const testResult = document.getElementById('testResult');
    
    // Mostrar loading
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando...';
    button.disabled = true;
    testResult.style.display = 'none';
    
    fetch(`/api/servidor/${serverId}/testar/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            testResult.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                </div>
            `;
            NotificationSystem.show('✅ ' + data.message, 'success');
        } else {
            testResult.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${data.message}
                </div>
            `;
            NotificationSystem.show('❌ ' + data.message, 'error');
        }
        testResult.style.display = 'block';
    })
    .catch(error => {
        testResult.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Erro ao testar conexão
            </div>
        `;
        testResult.style.display = 'block';
        NotificationSystem.show('❌ Erro ao testar conexão', 'error');
    })
    .finally(() => {
        // Restaurar botão
        button.innerHTML = originalContent;
        button.disabled = false;
    });
}
</script>
{% endblock %} 
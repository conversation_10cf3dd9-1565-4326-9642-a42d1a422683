import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import type { DB } from '@/services/db.service';

const DB_PATH = path.join(process.cwd(), 'src', 'db.json');

async function getDB(): Promise<DB> {
  try {
    const data = await fs.readFile(DB_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (err) {
    console.error('Erro ao carregar banco de dados:', err);
    throw new Error('Erro ao carregar banco de dados');
  }
}

async function saveDB(data: DB): Promise<void> {
  try {
    await fs.writeFile(DB_PATH, JSON.stringify(data, null, 2));
  } catch (err) {
    console.error('Erro ao salvar banco de dados:', err);
    throw new Error('Erro ao salvar banco de dados');
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const id = searchParams.get('id');
    const email = searchParams.get('email');

    const db = await getDB();

    switch (action) {
      case 'getUsers':
        return NextResponse.json(db.users);
      case 'getUserById':
        const userById = db.users.find(u => u.id === Number(id));
        return NextResponse.json(userById || null);
      case 'getUserByEmail':
        const userByEmail = db.users.find(u => u.email === email);
        return NextResponse.json(userByEmail || null);
      case 'getCameras':
        return NextResponse.json(db.cameras);
      case 'getCameraById':
        const camera = db.cameras.find(c => c.id === Number(id));
        return NextResponse.json(camera || null);
      case 'getSettings':
        return NextResponse.json(db.settings);
      default:
        return NextResponse.json({ error: 'Ação não suportada' }, { status: 400 });
    }
  } catch (err) {
    console.error('Erro na requisição GET:', err);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const { action, data } = await request.json();
    const db = await getDB();

    switch (action) {
      case 'createUser':
        const newUser = {
          ...data,
          id: db.users.length + 1,
          createdAt: new Date().toISOString()
        };
        db.users.push(newUser);
        await saveDB(db);
        return NextResponse.json(newUser);

      case 'createCamera':
        const newCamera = {
          ...data,
          id: db.cameras.length + 1,
          createdAt: new Date().toISOString(),
          lastConnection: new Date().toISOString()
        };
        db.cameras.push(newCamera);
        await saveDB(db);
        return NextResponse.json(newCamera);

      case 'createLog':
        const newLog = {
          ...data,
          id: db.logs.length + 1,
          timestamp: new Date().toISOString()
        };
        db.logs.push(newLog);
        await saveDB(db);
        return NextResponse.json(newLog);

      case 'updateSettings':
        db.settings = { ...db.settings, ...data };
        await saveDB(db);
        return NextResponse.json(db.settings);

      default:
        return NextResponse.json({ error: 'Ação não suportada' }, { status: 400 });
    }
  } catch (err) {
    console.error('Erro na requisição POST:', err);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

export async function PATCH(request: Request) {
  try {
    const { action, id, data } = await request.json();
    const db = await getDB();

    switch (action) {
      case 'updateCameraStatus':
        const cameraStatusIndex = db.cameras.findIndex(c => c.id === Number(id));
        if (cameraStatusIndex === -1) {
          return NextResponse.json({ error: 'Câmera não encontrada' }, { status: 404 });
        }
        db.cameras[cameraStatusIndex] = { ...db.cameras[cameraStatusIndex], ...data };
        await saveDB(db);
        return NextResponse.json(db.cameras[cameraStatusIndex]);

      case 'updateCamera':
        const cameraIndex = db.cameras.findIndex(c => c.id === Number(id));
        if (cameraIndex === -1) {
          return NextResponse.json({ error: 'Câmera não encontrada' }, { status: 404 });
        }
        db.cameras[cameraIndex] = { ...db.cameras[cameraIndex], ...data };
        await saveDB(db);
        return NextResponse.json(db.cameras[cameraIndex]);

      case 'updateUserPassword':
        const userIndex = db.users.findIndex(u => u.id === Number(id));
        if (userIndex === -1) {
          return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }
        db.users[userIndex] = {
          ...db.users[userIndex],
          ...data,
          updatedAt: new Date().toISOString()
        };
        await saveDB(db);
        return NextResponse.json(db.users[userIndex]);

      default:
        return NextResponse.json({ error: 'Ação não suportada' }, { status: 400 });
    }
  } catch (err) {
    console.error('Erro na requisição PATCH:', err);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { action, id } = await request.json();
    const db = await getDB();

    switch (action) {
      case 'deleteCamera':
        const cameraIndex = db.cameras.findIndex(c => c.id === Number(id));
        if (cameraIndex === -1) {
          return NextResponse.json({ error: 'Câmera não encontrada' }, { status: 404 });
        }

        // Remover a câmera do array
        db.cameras.splice(cameraIndex, 1);

        // Adicionar log de exclusão
        db.logs.push({
          id: db.logs.length > 0 ? Math.max(...db.logs.map(l => l.id)) + 1 : 1,
          type: 'camera_deleted',
          message: `Câmera ID ${id} foi excluída`,
          timestamp: new Date().toISOString(),
          userId: null,
          details: { cameraId: id }
        });

        await saveDB(db);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Ação não suportada' }, { status: 400 });
    }
  } catch (err) {
    console.error('Erro na requisição DELETE:', err);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
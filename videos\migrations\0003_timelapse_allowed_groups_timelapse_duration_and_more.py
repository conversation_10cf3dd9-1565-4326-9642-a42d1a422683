# Generated by Django 4.2.7 on 2025-07-03 20:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("videos", "0002_serverconnection"),
    ]

    operations = [
        migrations.AddField(
            model_name="timelapse",
            name="allowed_groups",
            field=models.ManyToManyField(
                blank=True,
                related_name="accessible_timelapses",
                to="auth.group",
                verbose_name="Grupos com Acesso",
            ),
        ),
        migrations.AddField(
            model_name="timelapse",
            name="duration",
            field=models.FloatField(
                blank=True, null=True, verbose_name="Dura<PERSON> (segundos)"
            ),
        ),
        migrations.AddField(
            model_name="timelapse",
            name="fps",
            field=models.IntegerField(default=24, verbose_name="FPS"),
        ),
        migrations.AddField(
            model_name="timelapse",
            name="source_subdirectory",
            field=models.CharField(
                blank=True, max_length=500, verbose_name="Subdiretório de Origem"
            ),
        ),
        migrations.AddField(
            model_name="timelapse",
            name="total_frames",
            field=models.IntegerField(default=0, verbose_name="Total de Frames"),
        ),
        migrations.AddField(
            model_name="timelapse",
            name="video_path",
            field=models.CharField(
                blank=True,
                max_length=500,
                null=True,
                verbose_name="Caminho do Vídeo no Servidor",
            ),
        ),
        migrations.AlterField(
            model_name="timelapse",
            name="video",
            field=models.FileField(
                blank=True, null=True, upload_to="timelapses/", verbose_name="Vídeo"
            ),
        ),
        migrations.CreateModel(
            name="ServerDirectory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Nome do Diretório"),
                ),
                (
                    "path",
                    models.CharField(
                        help_text="Caminho absoluto no servidor",
                        max_length=500,
                        verbose_name="Caminho Completo",
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="Descrição")),
                ("is_active", models.BooleanField(default=True, verbose_name="Ativo")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Criado em"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Atualizado em"),
                ),
                (
                    "allowed_groups",
                    models.ManyToManyField(
                        blank=True, to="auth.group", verbose_name="Grupos Permitidos"
                    ),
                ),
            ],
            options={
                "verbose_name": "Diretório do Servidor",
                "verbose_name_plural": "Diretórios do Servidor",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="timelapse",
            name="source_directory",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="videos.serverdirectory",
                verbose_name="Diretório de Origem",
            ),
        ),
    ]

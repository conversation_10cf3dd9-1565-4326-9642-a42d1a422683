# Instruções para Implantação no Servidor

## Pré-requisitos

- Acesso SSH ao servidor (fornecido: `ssh root@38.102.126.144`)
- Node.js v20 ou superior
- PM2 (gerenciador de processos para Node.js)
- <PERSON><PERSON><PERSON> (para proxy reverso)

## Passos para Implantação

### 1. Conectar ao Servidor

```bash
ssh root@38.102.126.144
```

### 2. At<PERSON><PERSON><PERSON> <PERSON>

```bash
apt update && apt upgrade -y
```

### 3. Instalar Node.js (se não estiver instalado)

```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt install -y nodejs
```

### 4. Instalar PM2 Globalmente

```bash
npm install -g pm2
```

### 5. Instalar Nginx (se não estiver instalado)

```bash
apt install -y nginx
```

### 6. <PERSON><PERSON>r <PERSON>retó<PERSON> do Projeto

```bash
mkdir -p /opt/poc-eagle
```

### 7. Transferir Arquivos para o Servidor

Existem duas opções:

#### Opção 1: Usando SCP
No seu computador local (não no servidor), execute:

```bash
scp -r /caminho/local/POC-EAGLE/* root@38.102.126.144:/opt/poc-eagle/
```

#### Opção 2: Usando Git
No servidor:

```bash
cd /opt
git clone [URL_DO_REPOSITÓRIO] poc-eagle
```

### 8. Configurar o Projeto

```bash
cd /opt/poc-eagle
npm ci
npm run build
```

### 9. Configurar o Nginx

Copie o arquivo de configuração do Nginx:

```bash
cp /opt/poc-eagle/nginx-config.conf /etc/nginx/sites-available/poc-eagle
ln -s /etc/nginx/sites-available/poc-eagle /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default # Remover configuração padrão se necessário
nginx -t # Testar configuração
systemctl restart nginx
```

### 10. Iniciar com PM2

```bash
cd /opt/poc-eagle
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 11. Verificar Status

```bash
pm2 status
```

## Comandos Úteis do PM2

- Reiniciar aplicação: `pm2 restart poc-eagle`
- Parar aplicação: `pm2 stop poc-eagle`
- Ver logs: `pm2 logs poc-eagle`
- Monitorar recursos: `pm2 monit`

## Acesso à Aplicação

Após a implantação, a aplicação estará acessível em:

```
http://38.102.126.144
```

## Resolução de Problemas

### Verificar Logs do PM2

```bash
pm2 logs poc-eagle
```

### Verificar Logs do Nginx

```bash
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log
```

### Verificar Status do PM2

```bash
pm2 status
```

### Reiniciar a Aplicação

```bash
pm2 restart poc-eagle
```

### Verificar Portas em Uso

```bash
ss -tuln
```

### Reiniciar Nginx

```bash
systemctl restart nginx
``` 
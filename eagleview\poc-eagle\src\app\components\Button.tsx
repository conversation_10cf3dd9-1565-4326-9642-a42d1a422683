'use client';
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

// Definindo as variantes do botão usando class-variance-authority
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none relative overflow-hidden",
  {
    variants: {
      variant: {
        primary: "bg-blue-600 text-white shadow-md hover:shadow-lg hover:bg-blue-700 focus-visible:ring-blue-500 active:translate-y-0.5 active:shadow",
        secondary: "bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500 active:translate-y-0.5",
        outline: "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-blue-500 active:translate-y-0.5",
        danger: "bg-red-600 text-white shadow-md hover:shadow-lg hover:bg-red-700 focus-visible:ring-red-600 active:translate-y-0.5 active:shadow",
        success: "bg-emerald-600 text-white shadow-md hover:shadow-lg hover:bg-emerald-700 focus-visible:ring-emerald-600 active:translate-y-0.5 active:shadow",
        glass: "backdrop-blur-sm bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/30 shadow-md",
      },
      size: {
        xs: "text-xs px-2.5 py-1 h-7 rounded",
        sm: "text-xs px-3 py-1.5 h-8 rounded-md",
        md: "text-sm px-4 py-2 h-10 rounded-md",
        lg: "text-base px-5 py-2.5 h-12 rounded-md",
        xl: "text-lg px-6 py-3 h-14 rounded-lg",
      },
      fullWidth: {
        true: "w-full",
      },
      withRipple: {
        true: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
      fullWidth: false,
      withRipple: true,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, fullWidth, withRipple, isLoading, leftIcon, rightIcon, children, onClick, ...props }, ref) => {
    const [ripples, setRipples] = React.useState<{ id: number; x: number; y: number; size: number }[]>([]);
    const buttonRef = React.useRef<HTMLButtonElement>(null);
    const rippleRef = React.useRef(0);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (!withRipple) {
        onClick?.(e);
        return;
      }

      const button = buttonRef.current;
      if (!button) return;

      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height) * 2;
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      const id = rippleRef.current;
      rippleRef.current += 1;

      setRipples((prevRipples) => [...prevRipples, { id, x, y, size }]);

      onClick?.(e);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples((prevRipples) => prevRipples.filter((ripple) => ripple.id !== id));
      }, 600);
    };

    React.useImperativeHandle(ref, () => buttonRef.current as HTMLButtonElement);

    return (
      <button
        className={cn(buttonVariants({ variant, size, fullWidth, withRipple, className }))}
        ref={buttonRef}
        disabled={isLoading || props.disabled}
        onClick={handleClick}
        {...props}
        style={{
          ...props.style,
          color: variant === 'primary' ? 'white' : undefined,
        }}
      >
        {/* Ripple effect */}
        {withRipple && ripples.map((ripple) => (
          <span
            key={ripple.id}
            className="absolute rounded-full bg-white/30 animate-ripple"
            style={{
              left: ripple.x,
              top: ripple.y,
              width: ripple.size,
              height: ripple.size,
            }}
          />
        ))}
        
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        <span>{children}</span>
        {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants }; 
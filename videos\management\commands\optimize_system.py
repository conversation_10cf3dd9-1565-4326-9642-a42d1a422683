"""
Comando para otimização e limpeza do sistema EagleView
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from videos.utils import cleanup_temp_files, check_system_resources
from videos.models import FrameImage, Timelapse
import os

class Command(BaseCommand):
    help = 'Otimiza o sistema: limpa arquivos temporários, otimiza banco de dados e gera relatório'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup-hours',
            type=int,
            default=24,
            help='Idade máxima dos arquivos temporários em horas (padrão: 24)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simular operações sem executar (apenas mostrar o que seria feito)'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Saída detalhada'
        )

    def handle(self, *args, **options):
        self.verbose = options['verbose']
        self.dry_run = options['dry_run']
        cleanup_hours = options['cleanup_hours']
        
        self.stdout.write(self.style.SUCCESS('🚀 Iniciando otimização do sistema EagleView...'))
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('⚠️  MODO SIMULAÇÃO - Nenhuma alteração será feita'))
        
        # 1. Verificar recursos do sistema
        self._check_system_resources()
        
        # 2. Limpar arquivos temporários
        self._cleanup_temporary_files(cleanup_hours)
        
        # 3. Otimizar banco de dados
        self._optimize_database()
        
        # 4. Verificar integridade dos arquivos
        self._check_file_integrity()
        
        # 5. Gerar relatório de uso
        self._generate_usage_report()
        
        self.stdout.write(self.style.SUCCESS('✅ Otimização concluída com sucesso!'))

    def _check_system_resources(self):
        """Verificar recursos do sistema"""
        self.stdout.write('\n📊 Verificando recursos do sistema...')
        
        try:
            resources = check_system_resources()
            
            self.stdout.write(f'💻 CPU: {resources["cpu_percent"]:.1f}%')
            self.stdout.write(f'🧠 Memória: {resources["memory_percent"]:.1f}%')
            self.stdout.write(f'💾 Espaço livre: {resources["disk_free_gb"]:.1f} GB')
            self.stdout.write(f'⚙️  Núcleos CPU: {resources["cpu_count"]}')
            
            # Alertas
            if resources['memory_percent'] > 80:
                self.stdout.write(self.style.WARNING('⚠️  Alto uso de memória detectado!'))
            
            if resources['disk_free_gb'] < 1:
                self.stdout.write(self.style.ERROR('🚨 Pouco espaço em disco disponível!'))
                
        except ImportError:
            self.stdout.write(self.style.WARNING('⚠️  psutil não disponível - pulando verificação de recursos'))

    def _cleanup_temporary_files(self, max_age_hours):
        """Limpar arquivos temporários"""
        self.stdout.write(f'\n🧹 Limpando arquivos temporários (>{max_age_hours}h)...')
        
        if not self.dry_run:
            cleaned_count = cleanup_temp_files(max_age_hours)
            self.stdout.write(f'✅ {cleaned_count} arquivos temporários removidos')
        else:
            self.stdout.write('🔍 [SIMULAÇÃO] Arquivos temporários seriam removidos')

    def _optimize_database(self):
        """Otimizar banco de dados"""
        self.stdout.write('\n🗃️  Otimizando banco de dados...')
        
        # Estatísticas do banco
        total_timelapses = Timelapse.objects.count()
        total_frames = FrameImage.objects.count()
        
        self.stdout.write(f'📁 Total de projetos: {total_timelapses}')
        self.stdout.write(f'🖼️  Total de frames: {total_frames}')
        
        # Verificar projetos órfãos (sem frames)
        orphan_timelapses = Timelapse.objects.filter(frames__isnull=True).distinct()
        orphan_count = orphan_timelapses.count()
        
        if orphan_count > 0:
            self.stdout.write(f'⚠️  {orphan_count} projeto(s) sem frames encontrado(s)')
            if not self.dry_run:
                # Remover projetos órfãos (opcional - descomentado para segurança)
                # orphan_timelapses.delete()
                # self.stdout.write(f'✅ {orphan_count} projeto(s) órfão(s) removido(s)')
                self.stdout.write('💡 Use o admin para revisar projetos órfãos manualmente')
            else:
                self.stdout.write('🔍 [SIMULAÇÃO] Projetos órfãos seriam removidos')

    def _check_file_integrity(self):
        """Verificar integridade dos arquivos"""
        self.stdout.write('\n🔍 Verificando integridade dos arquivos...')
        
        missing_files = []
        
        # Verificar frames
        for frame in FrameImage.objects.all():
            if not os.path.exists(frame.image.path):
                missing_files.append(f'Frame {frame.id}: {frame.image.path}')
        
        # Verificar vídeos
        for timelapse in Timelapse.objects.exclude(video=''):
            if timelapse.video and not os.path.exists(timelapse.video.path):
                missing_files.append(f'Vídeo {timelapse.id}: {timelapse.video.path}')
        
        if missing_files:
            self.stdout.write(self.style.ERROR(f'🚨 {len(missing_files)} arquivo(s) ausente(s):'))
            for missing in missing_files[:10]:  # Mostrar apenas os primeiros 10
                self.stdout.write(f'   - {missing}')
            if len(missing_files) > 10:
                self.stdout.write(f'   ... e mais {len(missing_files) - 10} arquivo(s)')
        else:
            self.stdout.write('✅ Todos os arquivos estão íntegros')

    def _generate_usage_report(self):
        """Gerar relatório de uso"""
        self.stdout.write('\n📋 Relatório de uso:')
        
        # Calcular tamanhos
        total_size = 0
        frame_size = 0
        video_size = 0
        
        # Tamanho dos frames
        for frame in FrameImage.objects.all():
            try:
                if os.path.exists(frame.image.path):
                    size = os.path.getsize(frame.image.path)
                    frame_size += size
                    total_size += size
            except:
                pass
        
        # Tamanho dos vídeos
        for timelapse in Timelapse.objects.exclude(video=''):
            try:
                if timelapse.video and os.path.exists(timelapse.video.path):
                    size = os.path.getsize(timelapse.video.path)
                    video_size += size
                    total_size += size
            except:
                pass
        
        def format_size(bytes_size):
            """Formatar tamanho em bytes para unidade legível"""
            for unit in ['B', 'KB', 'MB', 'GB']:
                if bytes_size < 1024.0:
                    return f"{bytes_size:.1f} {unit}"
                bytes_size /= 1024.0
            return f"{bytes_size:.1f} TB"
        
        self.stdout.write(f'📦 Espaço total usado: {format_size(total_size)}')
        self.stdout.write(f'🖼️  Frames: {format_size(frame_size)}')
        self.stdout.write(f'🎬 Vídeos: {format_size(video_size)}')
        
        # Estatísticas por tenant
        from django.contrib.auth.models import Group
        self.stdout.write('\n🏢 Uso por organização:')
        
        for group in Group.objects.all():
            group_timelapses = Timelapse.objects.filter(tenant=group).count()
            group_frames = FrameImage.objects.filter(timelapse__tenant=group).count()
            
            if group_timelapses > 0:
                self.stdout.write(f'   {group.name}: {group_timelapses} projetos, {group_frames} frames')

    def _log_with_style(self, message, style='SUCCESS'):
        """Log com estilo"""
        if self.verbose:
            if style == 'SUCCESS':
                self.stdout.write(self.style.SUCCESS(message))
            elif style == 'WARNING':
                self.stdout.write(self.style.WARNING(message))
            elif style == 'ERROR':
                self.stdout.write(self.style.ERROR(message))
            else:
                self.stdout.write(message) 
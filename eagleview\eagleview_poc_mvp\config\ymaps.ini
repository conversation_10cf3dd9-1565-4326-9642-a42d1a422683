;map center coordinates. Geo location if empty. For example: "50.4437, 30.5162"
CENTER="48.52584946874239, 25.03798691368117"
;map language
LANG="ru-RU"
;default map zoom
ZOOM=15
;default map type. may be map, satellite, hybrid for yandex or roadmap, satellite, hybrid, terrain for google
TYPE="map"
;show dead devices label?
DEAD_LABEL=1
;show alive devices label?
ALIVE_LABEL=0
;Use canvas rendering
CANVAS_RENDER=1
;device finder zoom
FINDING_ZOOM=17
;Hightlight area with circle when device found
FINDING_CIRCLE=1
;ignore canvas renderer for labeled placemarks (slower)
CANVAS_RENDER_IGNORE_LABELED=1
;Default maps service. yandex, google and leaflet currently supported
MAPS_SERVICE="leaflet"
;JS Google maps API key
GMAPS_APIKEY="YOUR_API_KEY_HERE"
;Leaflet custom tile layer
LEAFLET_TILE_LAYER="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
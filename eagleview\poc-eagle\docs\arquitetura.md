# Arquitetura da POC - Eagle View Camera

## 🎯 Objetivo
Criar uma prova de conceito (POC) para visualização em tempo real de uma câmera IP através de uma interface web simples.

## 🏗️ Estrutura do Projeto

```mermaid
graph TB
    A[Frontend Next.js] -->|HTTPS| B[Câmera IP]
    A -->|Autenticação| B
    
    subgraph Frontend
        C[Página de Login] --> D[Página de Streaming]
        D -->|Exibe| E[Video Stream]
    end
```

## 🔧 Componentes Principais

### 1. Frontend (Next.js)
- **Tecnologias:**
  - Next.js 14+
  - React 18+
  - TailwindCSS (para estilização)
  - Axios (para requisições HTTP)

- **Páginas:**
  - Login (`/pages/login.tsx`)
  - Streaming (`/pages/streaming.tsx`)

### 2. Autenticação
- Sistema de autenticação básica utilizando:
  - Senha: `Timelapse@1!`
  - Código de <PERSON>sso: `952700083KBZSG2O`

### 3. Conexão com a Câmera
- **Configurações:**
  - Endereço: `https://**************`
  - Porta: `443`
  - Protocolo: `HTTPS`

## 📝 Requisitos Funcionais

1. **Autenticação**
   - Interface de login com campos para senha
   - Validação do código de acesso
   - Persistência da sessão

2. **Visualização do Stream**
   - Player de vídeo em tempo real
   - Controles básicos (play/pause)
   - Indicador de status da conexão

3. **Interface**
   - Design responsivo
   - Loading states
   - Tratamento de erros

## 🛠️ Estrutura de Diretórios

```
poc-eagle/
├── src/
│   ├── components/
│   │   ├── LoginForm/
│   │   ├── VideoPlayer/
│   │   └── Layout/
│   ├── pages/
│   │   ├── index.tsx
│   │   ├── login.tsx
│   │   └── streaming.tsx
│   ├── styles/
│   └── utils/
├── public/
├── docs/
└── package.json
```

## 🔒 Segurança

1. **Autenticação**
   - Validação de credenciais no cliente
   - Tokens de sessão
   - Proteção de rotas

2. **Conexão**
   - Uso exclusivo de HTTPS
   - Validação de certificados
   - Timeouts apropriados

## 📱 Interface do Usuário

### Página de Login
- Campo de senha
- Botão de login
- Feedback visual de erros
- Loading state durante autenticação

### Página de Streaming
- Player de vídeo em tamanho adequado
- Indicadores de status
- Controles básicos
- Botão de logout

## 🚀 Próximos Passos

1. **Fase 1 - Setup Inicial**
   - Criar projeto Next.js
   - Configurar TailwindCSS
   - Estruturar diretórios

2. **Fase 2 - Autenticação**
   - Implementar página de login
   - Configurar sistema de autenticação
   - Testar fluxo de login

3. **Fase 3 - Streaming**
   - Implementar player de vídeo
   - Conectar com a câmera
   - Testar streaming

4. **Fase 4 - Refinamentos**
   - Melhorar UI/UX
   - Adicionar tratamento de erros
   - Otimizar performance
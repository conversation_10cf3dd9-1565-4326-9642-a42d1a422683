# POC Eagle View Camera

## 🎥 Acesso ao Vivo
<S><UID>952700083KBZSG2O</UID><OPE>1</OPE></S>
A senha é: Timelapse@1

### 📡 **Configurações de Rede**

- **Endereço IP:** `**************`  
- **Máscara de sub-rede:** `*************`  
- **Gateway padrão:** `**************`  
- **Endereço MAC:** `ec:71:db:a3:92:13`  
- **DNS preferido:** `**************`  
- **DNS alternativo:** *(não especificado)*  

### 🔐 **Serviços de Acesso**

- **RTMP:** Ativado
  - **Porta:** `1935`
- **HTTP:** Desativado  
- **HTTPS:** Ativado  
  - **Porta:** `443`  
- **RTSP:** Ativado
  - **Porta:** `554`

### 🎦 **Protocolos de Streaming Disponíveis**

1. **FLV (Flash Video)**
   - <PERSON><PERSON> padrão
   - Porta: 1935
   - URL: `/flv?port=1935&app=bcs&stream=channel0_main.bcs`
   - Melhor compatibilidade com navegadores

2. **RTSP (Real Time Streaming Protocol)**
   - Porta: 554
   - URL: `/h264Preview_01_main`
   - Menor latência
   - Requer player compatível

3. **WebRTC (Web Real-Time Communication)**
   - Porta: 443
   - URL: `/api.cgi?cmd=GetWebRTCStream`
   - Baixa latência
   - Suporte moderno de navegadores

4. **MJPEG (Motion JPEG)**
   - Porta: 443
   - URL: `/cgi-bin/api.cgi?cmd=Snap`
   - Alta compatibilidade
   - Maior uso de banda

### 🔑 **Código de Acesso / QR Code**

- **Código:** `952700083KBZSG2O`  
- **QR Code:** Disponível em `acessos/camera1/qrcode1.png`

### 📱 **Aplicativos Compatíveis**

1. **Reolink App**
   - Recomendado para acesso móvel
   - Disponível para iOS e Android
   - Suporte a todos os recursos da câmera

2. **Reolink Client**
   - Software para Windows/Mac
   - Interface completa de gerenciamento
   - Suporte a múltiplos streams

### 💻 **Acesso via Navegador**

1. Acesse `https://**************`
2. Use as credenciais:
   - Usuário: `admin`
   - Senha: `Timelapse@1`
3. Escolha um dos protocolos disponíveis na interface

### ⚠️ **Observações Importantes**

1. Para melhor performance:
   - Use RTSP ou WebRTC para baixa latência
   - Use FLV para melhor compatibilidade
   - Use MJPEG como última opção

2. Requisitos de Rede:
   - Conexão local recomendada
   - Portas necessárias abertas (443, 554, 1935)
   - Largura de banda suficiente para streaming

3. Troubleshooting:
   - Verifique se a câmera está na mesma rede
   - Teste diferentes protocolos
   - Confirme as credenciais de acesso
   - Verifique firewall e configurações de rede

### 🔧 **Suporte**

Para problemas de acesso ou dúvidas:
1. Verifique as dicas de troubleshooting
2. Teste com o aplicativo Reolink
3. Entre em contato com o suporte técnico

import cv2
from urllib.parse import quote
import argparse
import time
import sys
import os
import requests
from requests.exceptions import RequestException

# Configuração do parser de argumentos para permitir escolha entre câmeras
parser = argparse.ArgumentParser(description='Acesso à câmera Reolink via RTSP')
parser.add_argument('--modo', type=str, default='local', 
                    choices=['local', 'remoto1', 'remoto2', 'ftp', 'eagleview'],
                    help='Modo de acesso: local, remoto1, remoto2, ftp ou eagleview')
parser.add_argument('--camera', type=str, default=None,
                    help='Nome da câmera FTP: aurel1a, aurel1b, vitrujk_cam01, etc.')
parser.add_argument('--porta', type=int, default=554,
                    help='Porta para conexão RTSP (normalmente 554)')
parser.add_argument('--debug', action='store_true',
                    help='Ativar modo de depuração com mais logs')
args = parser.parse_args()

# Ativar modo de depuração, se solicitado
DEBUG = args.debug

# Credenciais
USER = "admin"
PASS = quote("Timelapse@1", safe="")
FTP_USER = "eagleview"
FTP_PASS = quote("Timelapse@1", safe="")

# Configurações dos diferentes endereços
CONFIG = {
    "local": {
        "ip": "**************",
        "porta": 554,
        "stream": "h264Preview_01_main"
    },
    "remoto1": {
        "ip": "**************",
        "porta": 554,
        "stream": "h264Preview_01_main"
    },
    "remoto2": {
        "ip": "*************",
        "porta": 554,
        "stream": "h264Preview_01_main"
    },
    "ftp": {
        "ip": "**************",
        "porta": args.porta,
        "stream": "h264Preview_01_main",
        "user": FTP_USER,
        "pass": FTP_PASS
    },
    "eagleview": {
        "ip": "**************",
        "porta": args.porta,
        "stream": args.camera if args.camera else "h264Preview_01_main",
        "user": FTP_USER,
        "pass": FTP_PASS
    }
}

# Lista de câmeras disponíveis no FTP (observadas na imagem)
FTP_CAMERAS = [
    "aurel1a", "aurel1b", "aurel4a", "aurel4b", "aurel4d", 
    "aure_11a2", "aure_12", "aure_12a", "fitesa01", "fitesa03",
    "keepe_bradesco", "nomad_itaim", "testemotoroff", 
    "vitrujk_cam01", "vitrujk_cam02", "voiter11", "voiter14b2",
    "voiter15a2", "voiter15a3", "voiter29a2", "voiter30b",
    "voiter_29a3", "voiter_29b2"
]

# Usar configuração baseada no modo selecionado
modo = args.modo
config = CONFIG[modo]

# Configurar IP e credenciais com base no modo
if modo in ["ftp", "eagleview"]:
    USER = config.get("user", USER)
    PASS = config.get("pass", PASS)
    
    # Se o modo for eagleview e nenhuma câmera foi especificada, listar as disponíveis
    if modo == "eagleview" and not args.camera:
        print("\nCâmeras disponíveis no servidor FTP:")
        for i, cam in enumerate(FTP_CAMERAS, 1):
            print(f"{i:2d}. {cam}")
        
        try:
            escolha = input("\nEscolha o número da câmera (ou 0 para sair): ")
            if escolha.strip() == "0" or not escolha.strip():
                print("Saindo...")
                sys.exit(0)
                
            idx = int(escolha) - 1
            if 0 <= idx < len(FTP_CAMERAS):
                config["stream"] = FTP_CAMERAS[idx]
            else:
                print("Escolha inválida. Usando configuração padrão.")
        except (ValueError, IndexError):
            print("Entrada inválida. Usando configuração padrão.")
        except KeyboardInterrupt:
            print("\nOperação cancelada pelo usuário.")
            sys.exit(0)

IP = config["ip"]
PORTA = config["porta"]
STREAM = config["stream"]

# Diferentes formatos de URL para tentar
URL_TEMPLATES = [
    # Formatos padrão RTSP para câmeras Reolink
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/{STREAM}",
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/h264Preview_01_main",
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/h264Preview_01_sub",
    
    # Tentativas com nome de câmera como parte do caminho
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/cam/{STREAM}/h264Preview_01_main",
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/cam/{STREAM}/main",
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/stream/{STREAM}",
    
    # Tentativas alternativas
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/live/ch0",
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/live/main",
    f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/media/video1",
]

# Se estamos no modo de câmera específica, tente URL específicas para esse modelo
if modo in ["ftp", "eagleview"] and STREAM in FTP_CAMERAS:
    URL_TEMPLATES = [
        f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/{STREAM}",
        f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/cameras/{STREAM}",
        f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/cam/{STREAM}/main",
        f"rtsp://{USER}:{PASS}@{IP}:{PORTA}/live/{STREAM}",
    ] + URL_TEMPLATES

print(f"\n{'=' * 50}")
print(f"Conectando à câmera no modo: {modo}")
if modo in ["ftp", "eagleview"]:
    print(f"Câmera selecionada: {STREAM}")
print(f"IP: {IP}, Porta: {PORTA}")
print(f"{'=' * 50}\n")

# Função para tentar conectar com vários formatos de URL
def try_connect():
    global cap
    success = False
    
    print("Tentando diferentes formatos de URL RTSP...")
    
    for i, rtsp_url in enumerate(URL_TEMPLATES, 1):
        if DEBUG:
            print(f"Tentativa {i}/{len(URL_TEMPLATES)}: {rtsp_url}")
        else:
            print(f"Tentativa {i}/{len(URL_TEMPLATES)}...")
            
        cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
        time.sleep(1)  # Dar tempo para conexão
        
        if cap.isOpened():
            print(f"\nConexão bem-sucedida com URL: {rtsp_url}")
            success = True
            break
        else:
            if DEBUG:
                print(f"Falha ao conectar com: {rtsp_url}")
            cap.release()
            time.sleep(1)  # Esperar antes da próxima tentativa
    
    return success

# Abrir a conexão com a câmera tentando diferentes formatos
cap = None
success = try_connect()

# Se falhou, tentar novamente com delay
if not success:
    print("\nFalha ao conectar. Tentando novamente com delay maior...")
    time.sleep(3)
    success = try_connect()

# Adicionar função para verificar disponibilidade de imagens estáticas via HTTP
def check_http_snapshots():
    print("\nVerificando se há imagens disponíveis via HTTP...")
    
    # Padrões de URL para tentar
    http_templates = [
        f"http://{IP}/cgi-bin/api.cgi?cmd=Snap&channel=0&user={USER}&password={PASS}",
        f"http://{IP}/snapshot.jpg",
        f"http://{IP}/cgi-bin/snapshot.cgi",
        f"http://{IP}/cameras/{STREAM}/snapshot.jpg",
        f"http://{IP}/cameras/{STREAM}/current.jpg"
    ]
    
    # Verifique cada URL
    for url in http_templates:
        try:
            print(f"Tentando: {url}")
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200 and response.headers.get('Content-Type', '').startswith('image/'):
                print(f"Imagem encontrada em: {url}")
                
                # Criar pasta para salvar as imagens
                os.makedirs("snapshots", exist_ok=True)
                
                # Salvar imagem
                timestamp = time.strftime("%Y%m%d-%H%M%S")
                img_path = f"snapshots/{STREAM}_{timestamp}.jpg"
                
                with open(img_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"Imagem salva em: {img_path}")
                return True
                
        except RequestException as e:
            print(f"Erro ao acessar {url}: {e}")
    
    print("Não foi possível obter imagens via HTTP.")
    return False

# Adicionar chamada à função após falha de RTSP
if not success:
    print("\nFalha ao conectar via RTSP. Tentando obter imagens estáticas...")
    check_http_snapshots()
    
    print("\nNão foi possível conectar à câmera via RTSP.")
    print(f"Verifique:\n- Se a câmera {STREAM} está online\n- Se o IP {IP} e porta {PORTA} estão corretos\n- Se as credenciais estão corretas")
    sys.exit(1)

print("\nStream aberto com sucesso. Pressione 'q' para sair.")

# Função para tentar reconectar em caso de falha
def reconnect():
    global cap
    print("Tentando reconectar...")
    
    # Tenta reconectar usando o último URL bem-sucedido (o primeiro da lista após sucesso)
    cap = cv2.VideoCapture(URL_TEMPLATES[0], cv2.CAP_FFMPEG)
    
    # Se a reconexão falhar, tenta todos os URLs novamente
    if not cap.isOpened():
        print("Falha na reconexão rápida. Tentando todos os URLs...")
        return try_connect()
    
    return cap.isOpened()

# Loop principal para capturar e exibir o vídeo
try:
    frame_count = 0
    error_count = 0
    
    while True:
        ok, frame = cap.read()
        
        if not ok:
            error_count += 1
            print(f"Erro ao ler frame ({error_count}/3)...")
            
            if error_count >= 3:
                print("Muitos erros consecutivos. Tentando reconectar...")
                cap.release()
                time.sleep(2)
                
                if not reconnect():
                    print("Reconexão falhou. Encerrando.")
                    break
                
                error_count = 0
            else:
                time.sleep(1)
                continue
        else:
            error_count = 0  # Resetar contador de erros
        
        # Contar frames para status
        frame_count += 1
        if frame_count % 30 == 0 and DEBUG:
            print(f"Frames recebidos: {frame_count}")
        
        # Redimensionar o frame se for muito grande
        altura, largura = frame.shape[:2]
        if largura > 1280:
            escala = 1280 / largura
            frame = cv2.resize(frame, (int(largura * escala), int(altura * escala)))
        
        # Adicionar texto indicando o modo de acesso
        cv2.putText(frame, f"Modo: {modo} | Câmera: {STREAM}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imshow("Reolink • Live", frame)
        key = cv2.waitKey(1) & 0xFF
        
        # 'q' para sair, 's' para salvar screenshot
        if key == ord("q"):
            break
        elif key == ord("s"):
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            filename = f"screenshot_{STREAM}_{timestamp}.jpg"
            cv2.imwrite(filename, frame)
            print(f"Screenshot salvo: {filename}")
        
except KeyboardInterrupt:
    print("\nEncerrando por interrupção do usuário...")
except Exception as e:
    print(f"Erro: {e}")
finally:
    # Liberar recursos
    if cap is not None:
        cap.release()
    cv2.destroyAllWindows()
    print("Stream encerrado.")

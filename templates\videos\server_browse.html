{% extends 'base.html' %}
{% load static %}

{% block title %}{{ server.name }} - Na<PERSON>gar Imagens - EagleView{% endblock %}

{% block extra_css %}
<style>
    /* Server Info Bar Moderno */
    .server-info-bar-modern {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .server-info-bar-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--highlight-orange), var(--highlight-blue));
    }
    
    .server-status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .path-breadcrumb {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        margin-top: 1rem;
    }
    
    /* Grid de Imagens Moderno */
    .image-grid-modern {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 1.5rem;
        padding: 2rem 0;
    }
    
    .image-card-modern {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 1rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .image-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--highlight-orange), var(--highlight-blue));
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .image-card-modern:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 106, 0, 0.3);
    }
    
    .image-card-modern:hover::before {
        opacity: 1;
    }
    
    .image-card-modern.selected {
        border-color: var(--highlight-orange);
        background: linear-gradient(135deg, rgba(255, 106, 0, 0.2) 0%, rgba(255, 106, 0, 0.1) 100%);
        transform: translateY(-2px);
    }
    
    .image-card-modern.selected::before {
        opacity: 1;
    }
    
    /* Preview de Imagem Moderno */
    .image-preview-modern {
        width: 100%;
        height: 140px;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        overflow: hidden;
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .image-preview-modern::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(45deg, rgba(255, 106, 0, 0.05), rgba(0, 91, 150, 0.05));
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .image-card-modern:hover .image-preview-modern::before {
        opacity: 1;
    }
    
    .image-preview-modern i {
        font-size: 2.5rem;
        background: linear-gradient(135deg, var(--highlight-orange), var(--highlight-blue));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        filter: drop-shadow(0 2px 4px rgba(255, 106, 0, 0.3));
    }
    
    .image-preview-modern img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px;
        transition: transform 0.3s ease;
    }
    
    .image-card-modern:hover .image-preview-modern img {
        transform: scale(1.05);
    }
    
    /* Informações da Imagem */
    .image-info-modern {
        text-align: center;
        position: relative;
    }
    
    .image-name-modern {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.3;
    }
    
    .image-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .image-size-modern {
        font-size: 0.75rem;
        color: var(--text-secondary);
        background: rgba(255, 255, 255, 0.05);
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }
    
    .image-date {
        font-size: 0.75rem;
        color: var(--text-secondary);
        font-style: italic;
    }
    
    /* Checkbox de Seleção Moderno */
    .selection-checkbox-modern {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        width: 24px;
        height: 24px;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        z-index: 2;
    }
    
    .selection-checkbox-modern:hover {
        background: rgba(255, 106, 0, 0.2);
        border-color: var(--highlight-orange);
        transform: scale(1.1);
    }
    
    .selection-checkbox-modern.checked {
        background: linear-gradient(135deg, var(--highlight-orange), #e85d00);
        border-color: var(--highlight-orange);
        color: white;
        transform: scale(1.1);
    }
    
    .selection-checkbox-modern i {
        font-size: 0.875rem;
        font-weight: bold;
    }
    
    .selection-controls {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        position: sticky;
        top: 20px;
        z-index: 100;
    }
    
    .import-section {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: 12px;
        padding: 2rem;
        margin-top: 2rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--surface-secondary);
        border-radius: 12px;
        border: 2px dashed var(--border-primary);
    }
    
    .empty-state i {
        font-size: 4rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        display: none;
    }
    
    .loading-content {
        background: var(--surface-secondary);
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        max-width: 400px;
    }
    
    .connection-status {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .connection-status.success {
        background: rgba(35, 134, 54, 0.2);
        color: var(--success);
        border: 1px solid var(--success);
    }
    
    .connection-status.error {
        background: rgba(218, 54, 51, 0.2);
        color: var(--danger);
        border: 1px solid var(--danger);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Cabeçalho Moderno -->
    <div class="d-flex align-items-center mb-5">
        <a href="{% url 'server_settings' %}" class="btn btn-outline-secondary rounded-circle me-4" style="width: 48px; height: 48px;">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="flex-grow-1">
            <h1 class="mb-2 d-flex align-items-center">
                <i class="fas fa-images me-3 text-primary float"></i>
                Navegar Imagens
            </h1>
            <p class="text-muted mb-0">
                Servidor: <strong>{{ server.name }}</strong> • 
                Selecione as imagens para importar no seu projeto
            </p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-info" onclick="refreshImages()">
                <i class="fas fa-sync-alt me-2"></i>
                Atualizar
            </button>
            <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filtersModal">
                <i class="fas fa-filter me-2"></i>
                Filtros
            </button>
        </div>
    </div>

    <!-- Informações do Servidor Modernas -->
    <div class="server-info-bar-modern">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="d-flex align-items-center gap-3 flex-wrap">
                    <span class="server-status-indicator">
                        <i class="fas fa-plug"></i>
                        Conectado
                    </span>
                    <div class="d-flex align-items-center gap-2">
                        <i class="fas fa-server text-primary"></i>
                        <strong>{{ server.host }}:{{ server.port }}</strong>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <i class="fas fa-folder text-warning"></i>
                        <code class="path-breadcrumb">{{ server.remote_path|default:"/home/" }}</code>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-end mt-3 mt-lg-0">
                <div class="d-flex gap-2 justify-content-end">
                    <span class="badge bg-info">
                        <i class="fas fa-images me-1"></i>
                        {{ images|length }} imagens
                    </span>
                    <button class="btn btn-outline-success btn-sm" onclick="refreshImages()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Atualizar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Controles de Seleção Modernos -->
    <div class="card glassmorphism">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center gap-3 flex-wrap">
                        <button class="btn btn-primary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-double me-1"></i>
                            Selecionar Todas
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectNone()">
                            <i class="fas fa-times me-1"></i>
                            Desmarcar Todas
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="selectRecent()">
                            <i class="fas fa-clock me-1"></i>
                            Últimas 24h
                        </button>
                        <div class="text-muted ms-2">
                            <i class="fas fa-info-circle me-1"></i>
                            <span id="selectedCount">0</span> de <span id="totalCount">{{ images|length }}</span> selecionadas
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-end mt-3 mt-lg-0">
                    <button class="btn btn-gradient" onclick="showImportModal()" id="importBtn" disabled>
                        <i class="fas fa-download me-2"></i>
                        Importar Selecionadas
                        <span class="badge bg-white text-dark ms-2" id="importCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Grid de Imagens Moderna -->
    {% if images %}
        <div class="image-grid-modern" id="imageGrid">
            {% for image in images %}
            <div class="image-card-modern" data-path="{{ image.path }}" data-name="{{ image.name }}" data-size="{{ image.size }}" onclick="toggleImage(this)">
                <div class="selection-checkbox-modern">
                    <i class="fas fa-check" style="display: none;"></i>
                </div>
                
                <div class="image-preview-modern">
                    {% if image.thumbnail %}
                        <img src="{{ image.thumbnail }}" alt="{{ image.name }}" loading="lazy">
                    {% else %}
                        <i class="fas fa-image"></i>
                    {% endif %}
                </div>
                
                <div class="image-info-modern">
                    <div class="image-name-modern" title="{{ image.name }}">{{ image.name }}</div>
                    <div class="image-meta">
                        {% if image.size %}
                            <span class="image-size-modern">{{ image.size|filesizeformat }}</span>
                        {% endif %}
                        {% if image.modified %}
                            <span class="image-date">{{ image.modified|date:"d/m H:i" }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <!-- Estado Vazio Moderno -->
        <div class="empty-state-modern">
            <div class="empty-state-content">
                <i class="fas fa-folder-open empty-icon"></i>
                <h3 class="mb-3">Nenhuma imagem encontrada</h3>
                <p class="text-muted mb-4">
                    Não foi possível encontrar imagens na pasta<br>
                    <code class="path-breadcrumb">{{ server.remote_path|default:"/home/" }}</code>
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <button class="btn btn-primary btn-lg" onclick="refreshImages()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Tentar Novamente
                    </button>
                    <button class="btn btn-outline-info btn-lg" onclick="changeDirectory()">
                        <i class="fas fa-folder me-2"></i>
                        Alterar Pasta
                    </button>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Modal de Importação -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title">
                    <i class="fas fa-download me-2"></i>
                    Importar Imagens
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'server_import_images' server.id %}" id="importForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importOption" class="form-label">Destino das Imagens</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="import_option" id="newProject" value="new" checked>
                            <label class="form-check-label" for="newProject">
                                Criar novo projeto
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="import_option" id="existingProject" value="existing">
                            <label class="form-check-label" for="existingProject">
                                Adicionar a projeto existente
                            </label>
                        </div>
                    </div>

                    <div id="newProjectSection">
                        <div class="mb-3">
                            <label for="timelapse_name" class="form-label">Nome do Novo Projeto</label>
                            <input type="text" class="form-control" id="timelapse_name" name="timelapse_name" 
                                   value="Importado de {{ server.name }}" required>
                        </div>
                    </div>

                    <div id="existingProjectSection" style="display: none;">
                        <div class="mb-3">
                            <label for="timelapse_id" class="form-label">Projeto Existente</label>
                            <select class="form-select" id="timelapse_id" name="timelapse_id">
                                <option value="">Selecione um projeto...</option>
                                <!-- Será preenchido via JavaScript -->
                            </select>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="importSummary">0 imagens serão importadas</span>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>
                        Importar Agora
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <h5>Importando Imagens</h5>
        <p class="text-muted">Por favor, aguarde...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedImages = new Set();

function toggleImage(card) {
    const path = card.dataset.path;
    const checkbox = card.querySelector('.selection-checkbox-modern');
    const checkIcon = checkbox.querySelector('i');
    
    if (selectedImages.has(path)) {
        selectedImages.delete(path);
        card.classList.remove('selected');
        checkbox.classList.remove('checked');
        checkIcon.style.display = 'none';
    } else {
        selectedImages.add(path);
        card.classList.add('selected');
        checkbox.classList.add('checked');
        checkIcon.style.display = 'block';
    }
    
    updateSelectionCount();
}

function selectAll() {
    document.querySelectorAll('.image-card-modern').forEach(card => {
        const path = card.dataset.path;
        if (!selectedImages.has(path)) {
            selectedImages.add(path);
            card.classList.add('selected');
            card.querySelector('.selection-checkbox-modern').classList.add('checked');
            card.querySelector('.selection-checkbox-modern i').style.display = 'block';
        }
    });
    updateSelectionCount();
}

function selectNone() {
    selectedImages.clear();
    document.querySelectorAll('.image-card-modern').forEach(card => {
        card.classList.remove('selected');
        card.querySelector('.selection-checkbox-modern').classList.remove('checked');
        card.querySelector('.selection-checkbox-modern i').style.display = 'none';
    });
    updateSelectionCount();
}

function selectRecent() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    document.querySelectorAll('.image-card-modern').forEach(card => {
        const imageName = card.dataset.name;
        // Lógica para selecionar imagens das últimas 24h baseado no nome/data
        // Implementar conforme formato dos nomes dos arquivos
    });
    updateSelectionCount();
}

function updateSelectionCount() {
    const count = selectedImages.size;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('importCount').textContent = count;
    document.getElementById('importBtn').disabled = count === 0;
}

function refreshImages() {
    const button = event.target;
    const originalContent = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Atualizando...';
    button.disabled = true;
    
    // Simular refresh
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function changeDirectory() {
    const newPath = prompt('Digite o caminho da pasta:', '{{ server.remote_path|default:"/home/" }}');
    if (newPath) {
        // Implementar mudança de diretório
        window.location.href = `?path=${encodeURIComponent(newPath)}`;
    }
}

function showImportModal() {
    if (selectedImages.size === 0) {
        NotificationSystem.show('Selecione pelo menos uma imagem', 'warning');
        return;
    }
    
    // Atualizar resumo
    document.getElementById('importSummary').textContent = 
        `${selectedImages.size} imagens serão importadas`;
    
    // Mostrar modal
    new bootstrap.Modal(document.getElementById('importModal')).show();
}

function refreshImages() {
    const button = event.target;
    const originalContent = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Atualizando...';
    button.disabled = true;
    
    // Recarregar página após delay
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Controle do modal de importação
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('importForm');
    const newProjectRadio = document.getElementById('newProject');
    const existingProjectRadio = document.getElementById('existingProject');
    const newProjectSection = document.getElementById('newProjectSection');
    const existingProjectSection = document.getElementById('existingProjectSection');
    
    // Toggle entre novo/existente
    [newProjectRadio, existingProjectRadio].forEach(radio => {
        radio.addEventListener('change', function() {
            if (newProjectRadio.checked) {
                newProjectSection.style.display = 'block';
                existingProjectSection.style.display = 'none';
                document.getElementById('timelapse_name').required = true;
                document.getElementById('timelapse_id').required = false;
            } else {
                newProjectSection.style.display = 'none';
                existingProjectSection.style.display = 'block';
                document.getElementById('timelapse_name').required = false;
                document.getElementById('timelapse_id').required = true;
            }
        });
    });
    
    // Submissão do formulário
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (selectedImages.size === 0) {
            NotificationSystem.show('Selecione pelo menos uma imagem', 'warning');
            return;
        }
        
        // Adicionar imagens selecionadas ao formulário
        selectedImages.forEach(path => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_images';
            input.value = path;
            importForm.appendChild(input);
        });
        
        // Mostrar loading
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        // Submeter formulário
        importForm.submit();
    });
    
    // Inicializar contadores
    updateSelectionCount();
});
</script>
{% endblock %} 
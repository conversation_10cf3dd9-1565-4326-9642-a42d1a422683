#!/usr/bin/env python3
import requests
import socket
import sys
import time
import platform
import subprocess
from datetime import datetime
import json
from requests.auth import HTTPBasicAuth
from typing import Dict, List, Tuple, Optional
import urllib3
import ipaddress
import netifaces

# Desabilita avisos de SSL não verificado
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class NetworkDiagnostic:
    def __init__(self):
        self.camera_ip = "**************"
        self.gateway = "**************"
        self.subnet_mask = "*************"

    def get_network_interfaces(self) -> List[Dict]:
        """Obtém informações das interfaces de rede"""
        interfaces = []
        for iface in netifaces.interfaces():
            try:
                addrs = netifaces.ifaddresses(iface)
                if netifaces.AF_INET in addrs:  # Se tem IPv4
                    for addr in addrs[netifaces.AF_INET]:
                        # Tenta obter o gateway
                        gateway = None
                        try:
                            gws = netifaces.gateways()
                            default_gw = gws.get('default', {}).get(netifaces.AF_INET)
                            if default_gw and default_gw[1] == iface:
                                gateway = default_gw[0]
                        except:
                            pass

                        interfaces.append({
                            'name': iface,
                            'ip': addr.get('addr', ''),
                            'netmask': addr.get('netmask', ''),
                            'broadcast': addr.get('broadcast', ''),
                            'gateway': gateway
                        })
            except Exception as e:
                print(f"Erro ao obter informações da interface {iface}: {str(e)}")
                continue
        return interfaces

    def get_network_suggestions(self, interfaces: List[Dict]) -> List[str]:
        """Gera sugestões de configuração de rede"""
        suggestions = []
        camera_network = ipaddress.IPv4Network(f"{self.camera_ip}/{self.subnet_mask}", strict=False)
        
        for iface in interfaces:
            if iface['ip'] and iface['netmask']:
                current_network = ipaddress.IPv4Network(f"{iface['ip']}/{iface['netmask']}", strict=False)
                
                if iface['gateway']:
                    suggestions.append(f"\nPara a interface {iface['name']} (atual):")
                    suggestions.append(f"  IP atual: {iface['ip']}")
                    suggestions.append(f"  Máscara: {iface['netmask']}")
                    suggestions.append(f"  Gateway: {iface['gateway']}")
                    
                    suggestions.append(f"\nPara conectar à câmera, você pode:")
                    suggestions.append("1. Mudar a câmera para sua rede (Recomendado):")
                    suggestions.append(f"  IP: {current_network.network_address + 200}")  # .200 como sugestão
                    suggestions.append(f"  Máscara: {iface['netmask']}")
                    suggestions.append(f"  Gateway: {iface['gateway']}")
                    
                    suggestions.append("\n2. Ou mudar seu computador para a rede da câmera:")
                    suggestions.append(f"  IP: {camera_network.network_address + 101}")  # .101 como sugestão
                    suggestions.append(f"  Máscara: {self.subnet_mask}")
                    suggestions.append(f"  Gateway: {self.gateway}")
        
        return suggestions

    def ping(self, host: str) -> Tuple[bool, float]:
        """Executa ping para um host"""
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        command = ['ping', param, '1', host]
        try:
            output = subprocess.check_output(command, stderr=subprocess.STDOUT, universal_newlines=True)
            if platform.system().lower() == 'windows':
                return 'TTL=' in output, float(output.split('Average = ')[-1].split('ms')[0]) if 'Average = ' in output else 0.0
            else:
                return 'bytes from' in output, float(output.split('time=')[-1].split(' ')[0]) if 'time=' in output else 0.0
        except:
            return False, 0.0

    def check_same_network(self, ip1: str, ip2: str, netmask: str) -> bool:
        """Verifica se dois IPs estão na mesma rede"""
        try:
            network = ipaddress.IPv4Network(f"{ip1}/{netmask}", strict=False)
            return ipaddress.IPv4Address(ip2) in network
        except:
            return False

    def traceroute(self, host: str, max_hops: int = 30) -> List[Dict]:
        """Executa traceroute para um host"""
        results = []
        if platform.system().lower() == 'windows':
            command = ['tracert', '-d', '-h', str(max_hops), host]
        else:
            command = ['traceroute', '-n', '-m', str(max_hops), host]
        
        try:
            output = subprocess.check_output(command, stderr=subprocess.STDOUT, universal_newlines=True)
            for line in output.split('\n'):
                if line.strip():
                    results.append({'hop': line.strip()})
        except:
            pass
        return results

class CameraChecker:
    def __init__(self):
        self.camera_ip = "**************"
        self.username = "admin"
        self.password = "Timelapse@1!"
        self.ports = {
            "HTTPS": 443,
            "RTSP": 554,
            "RTMP": 1935
        }
        self.network = NetworkDiagnostic()
        
    def check_port(self, port: int) -> Tuple[bool, str]:
        """Verifica se uma porta está aberta"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        try:
            result = sock.connect_ex((self.camera_ip, port))
            sock.close()
            return result == 0, "Porta aberta" if result == 0 else "Porta fechada"
        except Exception as e:
            return False, f"Erro ao verificar porta: {str(e)}"

    def check_camera_status(self) -> Dict:
        """Verifica o status geral da câmera"""
        status = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "camera_ip": self.camera_ip,
            "network_info": {},
            "connectivity": {},
            "ports": {},
            "protocols": {},
            "diagnostics": {},
            "error": None
        }

        # Informações de rede
        interfaces = self.network.get_network_interfaces()
        status["network_info"]["interfaces"] = interfaces
        status["network_info"]["suggestions"] = self.network.get_network_suggestions(interfaces)
        
        # Verifica se alguma interface está na mesma rede da câmera
        same_network = False
        for iface in interfaces:
            if self.network.check_same_network(iface['ip'], self.camera_ip, iface['netmask']):
                same_network = True
                status["network_info"]["local_ip"] = iface['ip']
                status["network_info"]["netmask"] = iface['netmask']
                status["network_info"]["gateway"] = iface.get('gateway')
                break
        
        status["network_info"]["same_network"] = same_network

        # Diagnóstico de rede
        ping_success, ping_time = self.network.ping(self.camera_ip)
        status["diagnostics"]["ping"] = {
            "success": ping_success,
            "time_ms": ping_time
        }

        if not ping_success:
            status["diagnostics"]["traceroute"] = self.network.traceroute(self.camera_ip)

        # Verifica conectividade básica (web)
        try:
            response = requests.get(f"https://{self.camera_ip}", 
                                  verify=False, 
                                  timeout=5,
                                  auth=HTTPBasicAuth(self.username, self.password))
            status["connectivity"]["web_interface"] = response.status_code == 200
        except requests.exceptions.RequestException as e:
            status["connectivity"]["web_interface"] = False
            status["error"] = str(e)

        # Verifica portas
        for name, port in self.ports.items():
            is_open, message = self.check_port(port)
            status["ports"][name] = {
                "port": port,
                "open": is_open,
                "message": message
            }

        # Tenta acessar diferentes protocolos
        protocols = {
            "RTSP": f"rtsp://{self.username}:{self.password}@{self.camera_ip}:554/h264Preview_01_main",
            "FLV": f"http://{self.camera_ip}:1935/flv?port=1935&app=bcs&stream=channel0_main.bcs",
            "MJPEG": f"https://{self.camera_ip}/cgi-bin/api.cgi?cmd=Snap&channel=0",
            "WebRTC": f"https://{self.camera_ip}/api.cgi?cmd=GetWebRTCStream&channel=0"
        }

        for protocol, url in protocols.items():
            try:
                if protocol == "RTSP":
                    status["protocols"][protocol] = status["ports"]["RTSP"]["open"]
                else:
                    response = requests.get(
                        url,
                        verify=False,
                        timeout=5,
                        auth=HTTPBasicAuth(self.username, self.password)
                    )
                    status["protocols"][protocol] = response.status_code == 200
            except requests.exceptions.RequestException:
                status["protocols"][protocol] = False

        return status

    def print_status(self, status: Dict) -> None:
        """Imprime o status da câmera de forma formatada"""
        print("\n=== Status da Câmera Eagle View ===")
        print(f"Timestamp: {status['timestamp']}")
        print(f"IP da Câmera: {status['camera_ip']}")

        print("\n--- Informações de Rede ---")
        if status["network_info"].get("same_network"):
            print(f"✅ Câmera na mesma rede")
            print(f"IP Local: {status['network_info'].get('local_ip')}")
            print(f"Máscara: {status['network_info'].get('netmask')}")
            print(f"Gateway: {status['network_info'].get('gateway')}")
        else:
            print("❌ Câmera em rede diferente!")
            print("\nInterfaces disponíveis:")
            for iface in status["network_info"].get("interfaces", []):
                if iface.get('gateway'):  # Mostra apenas interfaces com gateway (conectadas)
                    print(f"  - {iface['name']}: {iface['ip']} (Máscara: {iface['netmask']}, Gateway: {iface.get('gateway', 'N/A')})")

            print("\n🔧 Sugestões de Configuração:")
            for suggestion in status["network_info"].get("suggestions", []):
                print(suggestion)

        print("\n--- Diagnóstico ---")
        ping_info = status["diagnostics"].get("ping", {})
        print(f"Ping: {'✅' if ping_info.get('success') else '❌'}", end='')
        if ping_info.get("time_ms"):
            print(f" ({ping_info['time_ms']}ms)")
        else:
            print()

        if "traceroute" in status["diagnostics"]:
            print("\nTraceroute:")
            for hop in status["diagnostics"]["traceroute"]:
                print(f"  {hop['hop']}")
        
        print("\n--- Conectividade ---")
        for name, value in status["connectivity"].items():
            print(f"{name}: {'✅' if value else '❌'}")
        
        print("\n--- Portas ---")
        for name, info in status["ports"].items():
            print(f"{name} (Porta {info['port']}): {'✅' if info['open'] else '❌'} - {info['message']}")
        
        print("\n--- Protocolos ---")
        for protocol, available in status["protocols"].items():
            print(f"{protocol}: {'✅' if available else '❌'}")
        
        if status["error"]:
            print(f"\n⚠️ Erro: {status['error']}")
            print("\n🔧 Passos para Resolução:")
            print("1. Primeiro, configure a rede conforme sugerido acima")
            print("2. Verifique se a câmera está ligada e conectada ao cabo de rede")
            print("3. Tente acessar a câmera pelo aplicativo Reolink para confirmar que está funcionando")
            print("4. Se necessário, reinicie a câmera")
            print("5. Verifique se há firewall bloqueando as conexões")
            print("\n💡 Dica: Use o aplicativo Reolink para mudar as configurações de rede da câmera")

    def monitor(self, interval: int = 60) -> None:
        """Monitora a câmera continuamente"""
        try:
            while True:
                status = self.check_camera_status()
                self.print_status(status)
                
                # Salva o log
                with open("camera_status.log", "a") as f:
                    f.write(json.dumps(status) + "\n")
                
                print(f"\nPróxima verificação em {interval} segundos...")
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\nMonitoramento interrompido pelo usuário.")
        except Exception as e:
            print(f"\nErro durante o monitoramento: {str(e)}")

def main():
    checker = CameraChecker()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--monitor":
        interval = int(sys.argv[2]) if len(sys.argv) > 2 else 60
        checker.monitor(interval)
    else:
        status = checker.check_camera_status()
        checker.print_status(status)

if __name__ == "__main__":
    main() 
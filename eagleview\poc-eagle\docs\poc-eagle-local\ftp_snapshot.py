import os
import sys
import time
import ftplib
import argparse
from datetime import datetime

# Configuração dos argumentos
parser = argparse.ArgumentParser(description='Acessar câmeras via FTP e baixar imagens')
parser.add_argument('--camera', type=str, default=None, help='Nome da câmera específica')
parser.add_argument('--listar', action='store_true', help='Apenas listar as câmeras disponíveis')
args = parser.parse_args()

# Configuração do servidor FTP
FTP_SERVER = "**************"
FTP_USER = "eagleview"
FTP_PASS = "Timelapse@1"
FTP_PORT = 21

# Lista de câmeras (observadas na imagem)
CAMERAS = [
    "aurel1a", "aurel1b", "aurel4a", "aurel4b", "aurel4d", 
    "aure_11a2", "aure_12", "aure_12a", "fitesa01", "fitesa03",
    "keepe_bradesco", "nomad_itaim", "testemotoroff", 
    "vitrujk_cam01", "vitrujk_cam02", "voiter11", "voiter14b2",
    "voiter15a2", "voiter15a3", "voiter29a2", "voiter30b",
    "voiter_29a3", "voiter_29b2"
]

def connect_ftp():
    """Conecta ao servidor FTP e retorna a conexão"""
    try:
        print(f"Conectando ao servidor FTP {FTP_SERVER}...")
        ftp = ftplib.FTP()
        ftp.connect(FTP_SERVER, FTP_PORT)
        ftp.login(FTP_USER, FTP_PASS)
        print(f"Conectado como {FTP_USER}")
        print(f"Mensagem do servidor: {ftp.getwelcome()}")
        return ftp
    except ftplib.all_errors as e:
        print(f"Erro ao conectar ao FTP: {e}")
        sys.exit(1)

def list_directory(ftp, path='.'):
    """Lista os arquivos e diretórios no caminho especificado"""
    try:
        print(f"\nListando conteúdo de: {path}")
        files = []
        ftp.dir(path, files.append)
        
        for item in files:
            print(f"  {item}")
        
        return files
    except ftplib.all_errors as e:
        print(f"Erro ao listar diretório {path}: {e}")
        return []

def explore_camera(ftp, camera_name):
    """Explora o diretório da câmera e baixa as imagens mais recentes"""
    try:
        # Primeiro, verificar se este diretório existe
        dirs = []
        ftp.dir(dirs.append)
        
        # Verificar estrutura de diretórios
        if any(d.endswith(camera_name) for d in dirs):
            print(f"Câmera encontrada: {camera_name}")
            
            # Entrar no diretório da câmera
            ftp.cwd(camera_name)
            list_directory(ftp)
            
            # Verificar subdiretórios comuns
            potential_dirs = ['snapshots', 'images', 'pictures', 'current', 'latest', 'records', 'record']
            
            for subdir in potential_dirs:
                try:
                    ftp.cwd(subdir)
                    print(f"\nEntrando em {camera_name}/{subdir}")
                    
                    # Listar arquivos
                    files = []
                    ftp.dir(files.append)
                    
                    # Procurar por arquivos de imagem
                    image_files = [f.split()[-1] for f in files if f.endswith('.jpg') or f.endswith('.jpeg') or f.endswith('.png')]
                    
                    if image_files:
                        print(f"Encontradas {len(image_files)} imagens")
                        
                        # Criar pasta para salvar as imagens
                        save_dir = f"snapshots/{camera_name}"
                        os.makedirs(save_dir, exist_ok=True)
                        
                        # Baixar algumas imagens (no máximo 5)
                        for i, img_file in enumerate(image_files[:5]):
                            save_path = f"{save_dir}/{img_file}"
                            print(f"Baixando {img_file}...")
                            
                            with open(save_path, 'wb') as f:
                                ftp.retrbinary(f'RETR {img_file}', f.write)
                            
                            print(f"Imagem salva em {save_path}")
                    
                    # Voltar ao diretório da câmera
                    ftp.cwd('..')
                    
                except ftplib.all_errors:
                    # Se não conseguir entrar no diretório, continua para o próximo
                    continue
            
            # Voltar ao diretório raiz
            ftp.cwd('/')
            
        else:
            print(f"Câmera não encontrada: {camera_name}")
    
    except ftplib.all_errors as e:
        print(f"Erro ao explorar câmera {camera_name}: {e}")

def main():
    # Conectar ao servidor FTP
    ftp = connect_ftp()
    
    # Criar diretório para salvar as imagens
    os.makedirs("snapshots", exist_ok=True)
    
    # Se apenas listar câmeras
    if args.listar:
        print("\nCâmeras disponíveis:")
        list_directory(ftp)
        ftp.quit()
        return
    
    # Se uma câmera específica foi solicitada
    if args.camera:
        explore_camera(ftp, args.camera)
    else:
        # Perguntar qual câmera explorar
        print("\nCâmeras disponíveis:")
        for i, cam in enumerate(CAMERAS, 1):
            print(f"{i:2d}. {cam}")
        
        try:
            choice = input("\nEscolha o número da câmera (ou 0 para sair): ")
            if choice == "0":
                print("Saindo...")
            else:
                idx = int(choice) - 1
                if 0 <= idx < len(CAMERAS):
                    camera = CAMERAS[idx]
                    explore_camera(ftp, camera)
                else:
                    print("Escolha inválida.")
        except (ValueError, IndexError):
            print("Entrada inválida.")
        except KeyboardInterrupt:
            print("\nOperação cancelada pelo usuário.")
    
    # Fechar conexão FTP
    ftp.quit()
    print("\nConexão FTP encerrada.")

if __name__ == "__main__":
    main() 
<p align="center">
  <img src="public/logo_eagle_view_simbolo_color_icone.png" alt="Eagle View Logo" width="150"/>
</p>

<h1 align="center">Eagle View Camera</h1>

<p align="center">
  <b>Sistema de visualização em tempo real de câmera IP com interface web</b>
</p>

<p align="center">
  <a href="#-sobre-o-projeto">Sobre</a> •
  <a href="#-funcionalidades-principais">Funcionalidades</a> •
  <a href="#-tecnologias-utilizadas">Tecnologias</a> •
  <a href="#-instalação-e-configuração">Instalação</a> •
  <a href="#-configurações-de-segurança">Segurança</a> •
  <a href="#-documentação-adicional">Documentação</a>
</p>

<br />

<div align="center">

![Next.js](https://img.shields.io/badge/Next.js-14%2B-black?style=flat-square&logo=next.js)
![React](https://img.shields.io/badge/React-18%2B-61dafb?style=flat-square&logo=react)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-latest-38b2ac?style=flat-square&logo=tailwind-css)
![Axios](https://img.shields.io/badge/Axios-latest-5a29e4?style=flat-square&logo=axios)
![License](https://img.shields.io/badge/Licença-MIT-green?style=flat-square)

</div>

<br />

## 📋 Sobre o Projeto

O **Eagle View Camera** é uma solução web moderna para visualização de streams de câmeras IP em tempo real. Desenvolvido com tecnologias de ponta, o sistema oferece uma interface intuitiva e segura para monitoramento via browser.

<div align="center">
  <img src="public/logo_eagle_view_simbolo_color_icone.png" alt="Interface da Aplicação" width="400"/>
  <br />
  <i>Interface da aplicação Eagle View Camera</i>
</div>

<br />

## 🚀 Funcionalidades Principais

<table>
  <tr>
    <td>✅ <b>Visualização em Tempo Real</b></td>
    <td>Stream de vídeo com baixa latência</td>
  </tr>
  <tr>
    <td>✅ <b>Sistema de Autenticação</b></td>
    <td>Login seguro com validação em duas etapas</td>
  </tr>
  <tr>
    <td>✅ <b>Interface Responsiva</b></td>
    <td>Adaptável a diferentes dispositivos e telas</td>
  </tr>
  <tr>
    <td>✅ <b>Controles de Player</b></td>
    <td>Play, pause e indicadores de status</td>
  </tr>
  <tr>
    <td>✅ <b>Monitoramento de Conexão</b></td>
    <td>Indicadores visuais do estado da conexão</td>
  </tr>
</table>

<br />

## 🛠️ Tecnologias Utilizadas

<details open>
  <summary><b>Frontend</b></summary>
  <br />
  <ul>
    <li>Next.js 14+</li>
    <li>React 18+</li>
    <li>TailwindCSS</li>
    <li>Axios</li>
  </ul>
</details>

<details>
  <summary><b>Segurança</b></summary>
  <br />
  <ul>
    <li>HTTPS</li>
    <li>Autenticação em duas etapas</li>
    <li>Proteção de rotas</li>
    <li>Validação de certificados</li>
  </ul>
</details>

<br />

## 💻 Requisitos do Sistema

- Node.js 18+
- NPM ou Yarn
- Navegador moderno com suporte a ES6+
- Conexão estável com a rede da câmera

<br />

## 🔧 Instalação e Configuração

<details>
  <summary><b>1. Clone o repositório</b></summary>
  
  ```bash
  git clone [url-do-repositorio]
  cd poc-eagle
  ```
</details>

<details>
  <summary><b>2. Instale as dependências</b></summary>
  
  ```bash
  npm install
  # ou
  yarn install
  ```
</details>

<details>
  <summary><b>3. Configure as variáveis de ambiente</b></summary>
  
  ```bash
  cp .env.example .env
  ```
  Edite o arquivo .env com suas configurações
</details>

<details>
  <summary><b>4. Inicie o servidor de desenvolvimento</b></summary>
  
  ```bash
  npm run dev
  # ou
  yarn dev
  ```
</details>

<br />

## 🔒 Configurações de Segurança

<table>
  <tr>
    <th colspan="2">Autenticação</th>
  </tr>
  <tr>
    <td>Senha padrão do sistema</td>
    <td><code>Timelapse@1!</code></td>
  </tr>
  <tr>
    <td>Código de acesso</td>
    <td><code>952700083KBZSG2O</code></td>
  </tr>
  <tr>
    <th colspan="2">Câmera</th>
  </tr>
  <tr>
    <td>Endereço</td>
    <td><code>https://192.168.10.100</code></td>
  </tr>
  <tr>
    <td>Porta</td>
    <td><code>443</code></td>
  </tr>
  <tr>
    <td>Protocolo</td>
    <td><code>HTTPS</code></td>
  </tr>
</table>

<br />

## 📁 Estrutura do Projeto

```
poc-eagle/
├── src/
│   ├── components/    # Componentes React reutilizáveis
│   ├── pages/        # Páginas da aplicação
│   ├── styles/       # Estilos e configurações do Tailwind
│   └── utils/        # Funções utilitárias
├── public/           # Arquivos estáticos
├── docs/            # Documentação
└── package.json
```

<br />

## 🖥️ Páginas Principais

<table>
  <tr>
    <th>Página de Login</th>
    <th>Página de Streaming</th>
  </tr>
  <tr>
    <td>
      <ul>
        <li>Interface de autenticação</li>
        <li>Validação em duas etapas</li>
        <li>Feedback visual de erros</li>
        <li>Estados de carregamento</li>
      </ul>
    </td>
    <td>
      <ul>
        <li>Player de vídeo otimizado</li>
        <li>Controles de reprodução</li>
        <li>Indicadores de status</li>
        <li>Opção de logout</li>
      </ul>
    </td>
  </tr>
</table>

<br />

## ⚡ Performance

<div align="center">
  <table>
    <tr>
      <td align="center">🚀</td>
      <td>Otimização de carregamento de assets</td>
    </tr>
    <tr>
      <td align="center">📹</td>
      <td>Streaming eficiente de vídeo</td>
    </tr>
    <tr>
      <td align="center">💾</td>
      <td>Caching estratégico</td>
    </tr>
    <tr>
      <td align="center">⏳</td>
      <td>Lazy loading de componentes</td>
    </tr>
  </table>
</div>

<br />

## 🔐 Boas Práticas de Segurança

- Uso exclusivo de HTTPS
- Validação de certificados SSL
- Timeouts apropriados
- Proteção contra ataques comuns
- Sessões com expiração automática

<br />

## 🌐 Compatibilidade

<div align="center">
  <table>
    <tr>
      <td align="center"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" width="24px" /></td>
      <td>Chrome 90+</td>
    </tr>
    <tr>
      <td align="center"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" width="24px" /></td>
      <td>Firefox 88+</td>
    </tr>
    <tr>
      <td align="center"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" width="24px" /></td>
      <td>Safari 14+</td>
    </tr>
    <tr>
      <td align="center"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" width="24px" /></td>
      <td>Edge 90+</td>
    </tr>
  </table>
</div>

<br />

## 📝 Documentação Adicional

Para mais detalhes sobre a arquitetura e implementação, consulte:
- [Documentação de Arquitetura](docs/arquitetura.md)
- [Guia Rápido](GUIA_RAPIDO.md)
- [Instruções do Servidor](INSTRUCOES_SERVIDOR.md)

<br />

## 🤝 Contribuição

1. Faça um Fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

<br />

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

<br />

<p align="center">
  <img src="public/logo_eagle_view_simbolo_color_icone.png" alt="Eagle View Logo" width="70"/>
  <br />
  <sub>© 2023 Eagle View Camera. Todos os direitos reservados.</sub>
</p>

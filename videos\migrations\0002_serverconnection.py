# Generated by Django 5.2.3 on 2025-06-25 00:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("videos", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ServerConnection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(max_length=100, verbose_name="Nome da Conexão"),
                ),
                (
                    "connection_type",
                    models.CharField(
                        choices=[("ssh", "SSH"), ("ftp", "FTP"), ("sftp", "SFTP")],
                        default="ssh",
                        max_length=10,
                        verbose_name="Tipo de Conexão",
                    ),
                ),
                ("host", models.Char<PERSON><PERSON>(max_length=255, verbose_name="Servidor")),
                ("port", models.IntegerField(default=22, verbose_name="<PERSON>a")),
                ("username", models.CharField(max_length=100, verbose_name="Usu<PERSON>rio")),
                (
                    "password",
                    models.CharField(
                        blank=True,
                        help_text="Deixe em branco para usar chave SSH",
                        max_length=255,
                        verbose_name="Senha",
                    ),
                ),
                (
                    "private_key_path",
                    models.CharField(
                        blank=True, max_length=500, verbose_name="Caminho da Chave SSH"
                    ),
                ),
                (
                    "remote_path",
                    models.CharField(
                        help_text="Caminho completo da pasta com as imagens",
                        max_length=500,
                        verbose_name="Pasta Remota",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Ativo")),
                (
                    "last_connected",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Última Conexão"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Criado em"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Atualizado em"),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="auth.group",
                        verbose_name="Empresa",
                    ),
                ),
            ],
            options={
                "verbose_name": "Conexão de Servidor",
                "verbose_name_plural": "Conexões de Servidor",
                "ordering": ["-created_at"],
            },
        ),
    ]

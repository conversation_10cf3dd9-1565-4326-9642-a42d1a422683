'use client';
import { useContext, useState, useEffect, useRef } from 'react';
import { AuthContext } from '../layout';
import type { AuthContextType } from '../layout';
import { useRouter, useSearchParams } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import { dbService } from '@/services/db.service';
import Image from 'next/image';
import Script from 'next/script';

type StreamProtocol = 'flv' | 'rtsp' | 'webrtc' | 'mjpeg' | 'reolink' | 'tcp' | 'udp' | 'http' | 'hls' | 'onvif';

interface CameraConfig {
  id?: number;
  nome?: string;
  ip: string;
  username: string;
  password: string;
  port: string;
  protocol?: StreamProtocol;
  url_path?: string;
  connection_options?: string;
}

const VideoPlayer = ({
  url,
  protocol,
  onError
}: {
  url: string;
  protocol: StreamProtocol;
  onError: (error: string) => void;
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const [isPlayerLoaded, setIsPlayerLoaded] = useState(false);

  useEffect(() => {
    // Cleanup function para remover player quando componente for desmontado
    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
    };
  }, []);

  // Inicializar player quando flvjs estiver carregado
  useEffect(() => {
    if (!isPlayerLoaded || !videoRef.current || protocol !== 'flv') return;

    try {
      // Verificar se flvjs está disponível
      if (typeof window.flvjs !== 'undefined' && window.flvjs.isSupported()) {
        if (playerRef.current) {
          playerRef.current.destroy();
        }

        const flvPlayer = window.flvjs.createPlayer({
          type: 'flv',
          url: url,
          isLive: true,
          cors: true,
          withCredentials: false,
          hasAudio: true,
          hasVideo: true
        }, {
          enableStashBuffer: false,
          stashInitialSize: 128, // Diminuindo o buffer para streams ao vivo
          enableWorker: true,
          lazyLoad: false
        });

        flvPlayer.attachMediaElement(videoRef.current);
        flvPlayer.load();
        flvPlayer.play().catch((e: Error) => {
          console.error('Erro ao reproduzir vídeo:', e);
          onError("Erro ao iniciar reprodução. Verifique se a câmera está acessível.");
        });

        // Lidar com erros do player
        flvPlayer.on(window.flvjs.Events.ERROR, (errorType: string, errorDetail: string) => {
          console.error(`Erro FLV.js: tipo=${errorType}, detalhe=`, errorDetail);
          onError(`Erro na stream FLV: ${errorDetail}`);
        });

        playerRef.current = flvPlayer;
      } else {
        onError("Seu navegador não suporta FLV.js. Use outro protocolo ou navegador.");
      }
    } catch (error) {
      console.error('Erro ao inicializar FLV.js:', error);
      onError('Erro ao inicializar player de vídeo. Tente outro protocolo.');
    }
  }, [isPlayerLoaded, url, protocol, onError]);

  const handleVideoError = () => {
    onError('Erro na reprodução do vídeo. Verifique a URL da stream ou tente outro protocolo.');
  };

  if (protocol === 'flv') {
    return (
      <div className="relative w-full h-full bg-black">
        <Script
          src="https://cdn.jsdelivr.net/npm/flv.js@1.6.2/dist/flv.min.js"
          onLoad={() => setIsPlayerLoaded(true)}
          onError={() => onError('Erro ao carregar biblioteca FLV.js')}
        />
        <video
          ref={videoRef}
          className="w-full h-full"
          controls
          autoPlay
          playsInline
          muted
          onError={handleVideoError}
        ></video>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full bg-black">
      <video
        ref={videoRef}
        className="w-full h-full"
        controls
        autoPlay
        playsInline
        muted
        src={url}
        onError={handleVideoError}
      ></video>
    </div>
  );
};

// Adicionar tipo global para flvjs
declare global {
  interface Window {
    flvjs: any;
  }
}

const Streaming = () => {
  const auth = useContext(AuthContext) as AuthContextType;
  const router = useRouter();
  const videoRef = useRef<HTMLVideoElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [streamStatus, setStreamStatus] = useState<'conectando' | 'conectado' | 'erro'>('conectando');
  const [selectedProtocol, setSelectedProtocol] = useState<StreamProtocol>('rtsp');
  const [showStream, setShowStream] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const [isCheckingConnectivity, setIsCheckingConnectivity] = useState(false);
  const [connectivityStatus, setConnectivityStatus] = useState<{
    server: boolean;
    internet: boolean;
    local: boolean;
  }>({
    server: true,
    internet: true,
    local: false,
  });
  const [cameraConfig, setCameraConfig] = useState<CameraConfig>({
    ip: '',
    username: '',
    password: '',
    port: ''
  });
  const [availableCameras, setAvailableCameras] = useState<CameraConfig[]>([]);
  const [selectedCameraId, setSelectedCameraId] = useState<number | null>(null);
  const [fullscreen, setFullscreen] = useState(false);
  const [showAddCameraModal, setShowAddCameraModal] = useState(false);
  const [isReolinkMode, setIsReolinkMode] = useState(false);
  const [isRemoteAccess, setIsRemoteAccess] = useState(false);
  const [newCamera, setNewCamera] = useState<CameraConfig>({
    nome: '',
    ip: '',
    username: '',
    password: '',
    port: '554',
    protocol: 'rtsp',
    url_path: '',
    connection_options: ''
  });
  const [savingCamera, setSavingCamera] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  if (!auth) {
    throw new Error('AuthContext must be used within an AuthProvider');
  }

  const { isAuthenticated, user } = auth as AuthContextType;

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    setIsLoading(false);
  }, [isAuthenticated, router]);

  const searchParams = useSearchParams();

  useEffect(() => {
    const loadCameras = async () => {
      try {
        const cameras = await dbService.getCameras();

        if (cameras.length > 0) {
          // Mapeia as câmeras para o formato da interface
          const mappedCameras = cameras.map(camera => ({
            id: camera.id,
            nome: camera.nome,
            ip: camera.ip,
            username: camera.username,
            password: camera.password,
            port: camera.port,
            protocol: camera.protocol as StreamProtocol
          }));

          setAvailableCameras(mappedCameras);

          // Verifica se há um parâmetro de câmera na URL
          const cameraId = searchParams.get('camera');
          if (cameraId) {
            const selectedCamera = mappedCameras.find(cam => cam.id === parseInt(cameraId));
            if (selectedCamera) {
              setCameraConfig(selectedCamera);
              setSelectedCameraId(parseInt(cameraId));
              setSelectedProtocol(selectedCamera.protocol || 'webrtc');
              return;
            }
          }

          // Se não houver parâmetro ou a câmera não for encontrada, usa a primeira
          const defaultCamera = mappedCameras[0];
          setCameraConfig(defaultCamera);
          setSelectedCameraId(defaultCamera.id);
          setSelectedProtocol(defaultCamera.protocol || 'webrtc');
        }
      } catch (error) {
        console.error('Erro ao carregar câmeras:', error);
        setError('Não foi possível carregar as câmeras. Por favor, tente novamente.');
      }
    };

    loadCameras();
  }, [searchParams]);

  const getStreamUrl = (protocol: StreamProtocol): string => {
    const { ip, username, password, port, url_path, connection_options } = cameraConfig;

    // Usar o IP configurado na câmera
    const directIp = ip;
    const baseUrl = `http://${directIp}`;
    const secureUrl = `https://${directIp}`;
    const customPort = port || '554';
    const customPath = url_path || '';
    const options = connection_options || '';

    // Usar as credenciais configuradas na câmera
    const directUsername = username;
    const directPassword = password;
    const authString = directUsername && directPassword ? `${directUsername}:${directPassword}@` : '';

    switch (protocol) {
      case 'tcp':
        // Para TCP, geralmente usamos o formato rtsp:// com opções de transporte
        return `rtsp://${authString}${directIp}:${customPort}${customPath}${options ? `?${options}` : ''}`;

      case 'udp':
        // Para UDP, similar ao TCP mas com opções diferentes
        return `rtsp://${authString}${directIp}:${customPort}${customPath}${options ? `?${options}` : ''}`;

      case 'http':
        // Para HTTP, usamos o formato http:// (autenticação opcional)
        // Verificamos se o caminho já começa com / para evitar duplicação
        const httpPath = customPath ? (customPath.startsWith('/') ? customPath : `/${customPath}`) : '';

        // Verificação especial para DroidCam (porta 4747)
        if (customPort === '4747') {
          console.log('Detectado DroidCam, usando URL direta');
          // Para DroidCam, usamos a URL direta sem caminho adicional e sem autenticação
          // Isso é importante porque o DroidCam não suporta autenticação na URL
          // Adicionamos um timestamp para evitar cache e garantir que a imagem seja atualizada
          const timestamp = new Date().getTime();
          // O DroidCam usa a URL /video para o stream principal
          return `http://${directIp}:${customPort}/video?t=${timestamp}`;
        }

        // Verificamos se o caminho já inclui http:// ou https://
        if (directIp.startsWith('http://') || directIp.startsWith('https://')) {
          // Se o IP já inclui o protocolo, não adicionamos novamente
          const baseUrl = directIp.includes(':') ? directIp : `${directIp}:${customPort}`;
          return `${baseUrl}${httpPath}${options ? `?${options}` : ''}`;
        } else {
          // Caso contrário, construímos a URL normalmente
          if (directUsername && directPassword) {
            return `http://${authString}${directIp}:${customPort}${httpPath}${options ? `?${options}` : ''}`;
          } else {
            return `http://${directIp}:${customPort}${httpPath}${options ? `?${options}` : ''}`;
          }
        }

      case 'flv':
        return `${baseUrl}/flv?port=${customPort || '1935'}&app=bcs&stream=channel0_main.bcs?channel=0&stream=0&user=${directUsername}&password=${directPassword}${options ? `&${options}` : ''}`;

      case 'rtsp':
        // Caminho padrão se não for especificado
        const rtspPath = customPath || '/h264Preview_01_main';
        return `rtsp://${authString}${directIp}:${customPort}${rtspPath}${options ? `?${options}` : ''}`;

      case 'webrtc':
        const webrtcPath = customPath || '/api/v1/WebRTC/channels/0/live';
        return `${baseUrl}${webrtcPath}?user=${directUsername}&password=${directPassword}${options ? `&${options}` : ''}`;

      case 'mjpeg':
        const mjpegPath = customPath || '/cgi-bin/api.cgi';
        return `${baseUrl}${mjpegPath}?cmd=Snap&channel=0&rs=${Date.now()}&user=${directUsername}&password=${directPassword}${options ? `&${options}` : ''}`;

      case 'hls':
        const hlsPath = customPath || '/hls/stream.m3u8';
        return `${baseUrl}${hlsPath}${options ? `?${options}` : ''}`;

      case 'reolink':
        if (username && username.length === 18 && /^[0-9A-Z]+$/.test(username)) {
          return `https://p2p.reolink.com/live.html?uid=${username}&password=${password}`;
        } else {
          // Fallback para acesso direto via WebRTC se não for UID Reolink válido
          return `${secureUrl}/api/v1/WebRTC/channels/0/live?user=${directUsername}&password=${directPassword}${options ? `&${options}` : ''}`;
        }

      case 'onvif':
        return `${baseUrl}/onvif/device_service${options ? `?${options}` : ''}`;

      default:
        return `${baseUrl}${customPath}${options ? `?${options}` : ''}`;
    }
  };

  const handleCameraChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const cameraId = parseInt(e.target.value);
    const selectedCamera = availableCameras.find(cam => cam.id === cameraId);

    if (selectedCamera) {
      setCameraConfig(selectedCamera);
      setSelectedCameraId(cameraId);
      setSelectedProtocol('reolink');

      // Se já estiver mostrando stream, reinicia para a nova câmera
      if (showStream) {
        setShowStream(false);
        setTimeout(() => {
          setShowStream(true);
          initStream();
        }, 500);
      }
    }
  };

  const handleConfigSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setShowStream(true);

    try {
      // Atualiza o status da câmera no banco
      if (selectedCameraId) {
        await dbService.updateCameraStatus(selectedCameraId, 'connecting');
      }

      // Registra o log de conexão
      if (user) {
        await dbService.createLog({
          userId: user.id,
          cameraId: selectedCameraId || undefined,
          action: 'CAMERA_CONNECT',
          category: 'camera',
          severity: 'info',
          details: `Conectando à câmera ${cameraConfig.nome || cameraConfig.ip}`,
          metadata: {
            protocol: selectedProtocol,
            timestamp: new Date().toISOString()
          },
          ip: window.location.hostname
        });
      }

      initStream();
    } catch (error) {
      console.error('Erro ao atualizar status da câmera:', error);
    }
  };

  const toggleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  const disconnectStream = async () => {
    setShowStream(false);
    setStreamStatus('conectando');
    setError(null);

    // Limpar qualquer timer de reconexão
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }

    // Limpar recursos de vídeo/imagem
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.src = '';
      videoRef.current.load();
    }

    if (imgRef.current) {
      imgRef.current.src = '';
    }

    // Atualiza o status da câmera no banco
    if (selectedCameraId) {
      await dbService.updateCameraStatus(selectedCameraId, 'inactive');
    }

    // Registra o log de desconexão
    if (user) {
      await dbService.createLog({
        userId: user.id,
        cameraId: selectedCameraId || undefined,
        action: 'CAMERA_DISCONNECT',
        category: 'camera',
        severity: 'info',
        details: `Desconectando da câmera ${cameraConfig.nome || cameraConfig.ip}`,
        ip: window.location.hostname
      });
    }
  };

  // Função para verificar a conectividade
  const checkConnectivity = async () => {
    setIsCheckingConnectivity(true);
    const status = {
      server: false,
      internet: false,
      local: false
    };

    try {
      // Verificar conectividade com a Internet
      const internetCheck = await fetch('https://www.google.com', {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      }).then(() => true).catch(() => false);

      status.internet = internetCheck;

      // Verificar conectividade com o servidor Reolink apenas se não for HTTP
      if (selectedProtocol !== 'http') {
        const reolinkCheck = await fetch('https://p2p.reolink.com', {
          method: 'HEAD',
          mode: 'no-cors',
          cache: 'no-cache'
        }).then(() => true).catch(() => false);

        status.server = reolinkCheck;
      } else {
        // Para HTTP, não precisamos verificar o servidor Reolink
        status.server = true;
      }

      // Verificar conectividade com a câmera configurada
      if (navigator.onLine && cameraConfig.ip) {
        try {
          // Verificação especial para DroidCam (porta 4747)
          if (selectedProtocol === 'http' && cameraConfig.port === '4747') {
            console.log('Detectado DroidCam, tentando verificar conectividade');
            try {
              // Para DroidCam, tentamos acessar a URL /video diretamente
              const url = `http://${cameraConfig.ip}:${cameraConfig.port}/video`;
              console.log('Verificando conectividade com DroidCam:', url);

              const droidCamCheck = await fetch(url, {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache',
                signal: AbortSignal.timeout(3000) // Timeout mais curto para DroidCam
              }).then(() => true).catch((error) => {
                console.log('Erro ao verificar DroidCam:', error);
                // Mesmo com erro, vamos assumir que está conectado para DroidCam
                return true;
              });

              status.local = droidCamCheck;
            } catch (error) {
              console.error('Erro ao verificar DroidCam:', error);
              // Mesmo com erro, vamos assumir que está conectado para DroidCam
              status.local = true;
            }
          } else {
            // Para outras câmeras, tentamos acessar a URL completa incluindo a porta
            const url = selectedProtocol === 'http'
              ? `http://${cameraConfig.ip}:${cameraConfig.port}${cameraConfig.url_path || ''}`
              : `http://${cameraConfig.ip}`;

            console.log('Verificando conectividade com:', url);

            const cameraCheck = await fetch(url, {
              method: 'HEAD',
              mode: 'no-cors',
              cache: 'no-cache',
              signal: AbortSignal.timeout(5000) // Aumentamos o timeout para 5 segundos
            }).then(() => true).catch((error) => {
              console.log('Erro ao verificar câmera:', error);
              return false;
            });

            status.local = cameraCheck;
          }
        } catch (error) {
          console.error('Erro ao verificar câmera:', error);
          status.local = false;
        }
      }
    } catch (err) {
      console.error('Erro ao verificar conectividade:', err);
    } finally {
      setConnectivityStatus(status);
      setIsCheckingConnectivity(false);

      // Definir mensagens de erro apropriadas com base no protocolo
      if (selectedProtocol === 'http') {
        if (!status.local) {
          // Mensagem mais específica para HTTP
          setError(`Não foi possível conectar à câmera via HTTP. Verifique se o endereço ${cameraConfig.ip}:${cameraConfig.port} está correto e se a câmera está acessível na rede. Algumas câmeras podem bloquear requisições de verificação de conectividade.`);
        }
      } else {
        // Mensagens para outros protocolos
        if (!status.server && status.internet && status.local) {
          setError('O servidor p2p.reolink.com está indisponível, mas a câmera está acessível. Tente usar a conexão direta.');
        } else if (!status.server && !status.internet) {
          setError('Não foi possível conectar à internet. Verifique sua conexão.');
        } else if (!status.server && status.internet && !status.local) {
          setError('O servidor p2p.reolink.com está indisponível e a câmera não foi encontrada. Verifique a conectividade da câmera.');
        }
      }
    }
  };

  const initStream = async () => {
    try {
      setStreamStatus('conectando');
      setError(null);
      setLoadingTimeout(false);

      // Verificar conectividade primeiro
      await checkConnectivity();

      // Se for HTTP, vamos tentar iniciar o stream diretamente
      if (selectedProtocol === 'http') {
        // Para HTTP, consideramos que a conexão foi bem-sucedida mesmo que a verificação de conectividade falhe
        // Isso porque muitas câmeras HTTP não respondem a requisições HEAD, mas funcionam para streaming
        console.log('Iniciando stream HTTP para:', getStreamUrl('http'));

        // Verificação especial para DroidCam (porta 4747)
        if (cameraConfig.port === '4747') {
          console.log('Detectado DroidCam, forçando status conectado');
          // Para DroidCam, forçamos o status para conectado imediatamente
          setStreamStatus('conectado');

          // Registramos o log de conexão bem-sucedida
          if (selectedCameraId) {
            await dbService.updateCameraStatus(selectedCameraId, 'active');
          }
        } else {
          // Para outras câmeras HTTP, definimos um timeout mais curto
          setTimeout(() => {
            // Se ainda estiver conectando, consideramos que o stream está ativo
            if (streamStatus === 'conectando') {
              setStreamStatus('conectado');
            }
          }, 3000);
        }
      }

      // Definir um timeout para verificar se a conexão está demorando muito
      const timeoutId = setTimeout(() => {
        if (streamStatus === 'conectando') {
          setLoadingTimeout(true);
          if (selectedProtocol === 'http') {
            setError('A conexão com a câmera está demorando muito. Verifique se o endereço IP e a porta estão corretos. Algumas câmeras HTTP podem não responder à verificação de conectividade, mas ainda assim funcionar para streaming.');
          } else {
            setError('A conexão com p2p.reolink.com está demorando muito. Verifique sua conexão com a internet ou tente novamente mais tarde.');
          }
          setStreamStatus('erro');
        }
      }, 15000); // 15 segundos

      const streamUrl = getStreamUrl(selectedProtocol);
      setStreamStatus('conectado');

      if (selectedCameraId) {
        await dbService.updateCameraStatus(selectedCameraId, 'active');
      }

      if (selectedProtocol === 'mjpeg' && imgRef.current) {
        imgRef.current.onload = async () => {
          setStreamStatus('conectado');
          if (selectedCameraId) {
            await dbService.updateCameraStatus(selectedCameraId, 'active');
          }
        };

        imgRef.current.onerror = async (e) => {
          console.error('Erro ao carregar imagem:', e);
          setStreamStatus('erro');
          setError('Erro na conexão com a câmera. Tentando reconectar...');

          if (selectedCameraId) {
            await dbService.updateCameraStatus(selectedCameraId, 'error');
          }

          if (user) {
            await dbService.createLog({
              userId: user.id,
              cameraId: selectedCameraId || undefined,
              action: 'CAMERA_ERROR',
              category: 'camera',
              severity: 'error',
              details: `Erro ao conectar à câmera ${cameraConfig.nome || cameraConfig.ip}`,
              ip: window.location.hostname
            });
          }

          reconnectTimerRef.current = setTimeout(initStream, 2000);
        };

        imgRef.current.src = streamUrl;
      } else if (videoRef.current) {
        videoRef.current.src = streamUrl;

        videoRef.current.onloadeddata = async () => {
          setStreamStatus('conectado');
          if (selectedCameraId) {
            await dbService.updateCameraStatus(selectedCameraId, 'active');
          }
        };

        videoRef.current.onerror = async (e) => {
          console.error('Erro ao carregar vídeo:', e);
          setStreamStatus('erro');
          setError('Erro na conexão com a câmera. Tentando reconectar...');

          if (selectedCameraId) {
            await dbService.updateCameraStatus(selectedCameraId, 'error');
          }

          if (user) {
            await dbService.createLog({
              userId: user.id,
              cameraId: selectedCameraId || undefined,
              action: 'CAMERA_ERROR',
              category: 'camera',
              severity: 'error',
              details: `Erro ao conectar à câmera ${cameraConfig.nome || cameraConfig.ip}`,
              ip: window.location.hostname
            });
          }

          reconnectTimerRef.current = setTimeout(initStream, 2000);
        };

        try {
          await videoRef.current.play();
        } catch (playError) {
          console.error('Erro ao iniciar reprodução:', playError);
          setError('Erro ao iniciar reprodução do vídeo. Verifique se a câmera está acessível.');
        }
      }

      // Limpar o timeout se a conexão for bem-sucedida
      clearTimeout(timeoutId);
    } catch (err) {
      console.error('Erro no streaming:', err);
      setStreamStatus('erro');
      setError('Ocorreu um erro inesperado. Por favor, tente novamente.');
    }
  };

  const logout = () => {
    if (auth && 'logout' in auth) {
      auth.logout();
      router.push('/login');
    }
  };

  const handleNewCameraChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewCamera(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddCamera = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSavingCamera(true);
    setSaveError(null);

    try {
      // Validar campos obrigatórios
      if (!newCamera.nome || !newCamera.ip || !newCamera.port) {
        setSaveError('Nome, endereço IP e porta são obrigatórios');
        setSavingCamera(false);
        return;
      }

      // Verificar se usuário e senha são obrigatórios (exceto para HTTP)
      if (newCamera.protocol !== 'http' && (!newCamera.username || !newCamera.password)) {
        setSaveError('Usuário e senha são obrigatórios para este protocolo');
        setSavingCamera(false);
        return;
      }

      // Criar a nova câmera
      const camera = await dbService.createCamera({
        nome: newCamera.nome || '',
        ip: newCamera.ip,
        username: newCamera.username || '',
        password: newCamera.password || '',
        port: newCamera.port,
        protocol: newCamera.protocol || 'rtsp',
        status: 'inactive',
        descricao: '',
        createdBy: user?.id,
        updatedAt: new Date().toISOString()
      });

      // Adicionar a nova câmera à lista e convertê-la para o formato CameraConfig
      const cameraConfig: CameraConfig = {
        id: camera.id,
        nome: camera.nome,
        ip: camera.ip,
        username: camera.username,
        password: camera.password,
        port: camera.port,
        protocol: camera.protocol as StreamProtocol
      };

      setAvailableCameras(prev => [...prev, cameraConfig]);

      // Selecionar a nova câmera
      setCameraConfig(cameraConfig);
      setSelectedCameraId(camera.id);
      setSelectedProtocol(cameraConfig.protocol || 'rtsp');

      // Fechar o modal
      setShowAddCameraModal(false);

      // Limpar o formulário
      setNewCamera({
        nome: '',
        ip: '',
        username: '',
        password: '',
        port: '554',
        protocol: 'rtsp',
        url_path: '',
        connection_options: ''
      });

      // Registrar o log
      if (user) {
        await dbService.createLog({
          userId: user.id,
          cameraId: camera.id,
          action: 'CAMERA_CREATE',
          category: 'camera',
          severity: 'info',
          details: `Câmera ${camera.nome} criada com sucesso`,
          ip: window.location.hostname
        });
      }
    } catch (error) {
      console.error('Erro ao cadastrar câmera:', error);
      setSaveError('Erro ao cadastrar câmera. Por favor, tente novamente.');
    } finally {
      setSavingCamera(false);
    }
  };

  const toggleDirectConnection = async () => {
    // Desconectar a conexão atual
    await disconnectStream();

    // Alternar entre modos de conexão
    setIsRemoteAccess(!isRemoteAccess);

    // Iniciar a nova conexão
    setShowStream(true);
    await initStream();
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-background">
        <div className="text-center">
          <div className="mb-4 h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-gray-500">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!auth.isAuthenticated) return null;

  return (
    <AppLayout>
      <div className="w-full">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Streaming de Câmeras</h1>
          <p className="text-gray-600">Visualize e gerencie suas câmeras de segurança remotamente.</p>
        </div>

        <div className="grid gap-6 md:grid-cols-3 lg:grid-cols-4">
          <div className="md:col-span-2 lg:col-span-3">
            {isLoading ? (
              <div className="flex items-center justify-center h-64 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="text-center">
                  <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  <p className="mt-2 text-gray-600">Carregando...</p>
                </div>
              </div>
            ) : (
              <>
                <form onSubmit={handleConfigSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 mb-5">
                  <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-4">
                    <div className="lg:col-span-2">
                      <label htmlFor="camera" className="form-label">Câmera</label>
                      <div className="relative">
                        <select
                          id="camera"
                          className="form-input form-select"
                          value={selectedCameraId || ''}
                          onChange={handleCameraChange}
                          disabled={showStream}
                        >
                          <option value="" disabled>Selecione uma câmera</option>
                          {availableCameras.map(camera => (
                            <option key={camera.id} value={camera.id}>
                              {camera.nome || `Câmera (${camera.ip})`}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="protocol" className="form-label">Protocolo</label>
                      <div className="relative">
                        <select
                          id="protocol"
                          className="form-input form-select"
                          value={selectedProtocol}
                          onChange={(e) => setSelectedProtocol(e.target.value as StreamProtocol)}
                          disabled={showStream}
                        >
                          <optgroup label="Protocolos Web">
                            <option value="webrtc">WebRTC</option>
                            <option value="mjpeg">MJPEG</option>
                            <option value="flv">FLV</option>
                            <option value="hls">HLS</option>
                          </optgroup>
                          <optgroup label="Protocolos Padrão">
                            <option value="rtsp">RTSP</option>
                            <option value="tcp">TCP</option>
                            <option value="udp">UDP</option>
                            <option value="http">HTTP</option>
                          </optgroup>
                          <optgroup label="Proprietários">
                            <option value="reolink">Reolink P2P</option>
                            <option value="onvif">ONVIF</option>
                          </optgroup>
                        </select>
                      </div>
                    </div>

                    <div className="flex items-end">
                      <div className="w-full">
                        {!showStream ? (
                          <button
                            type="submit"
                            className="btn bg-blue-500 hover:bg-blue-600 text-white w-full flex items-center justify-center space-x-2"
                            style={{ color: 'white', backgroundColor: '#3b82f6' }}
                            disabled={!selectedCameraId}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">Iniciar Stream</span>
                          </button>
                        ) : (
                          <button
                            type="button"
                            className="btn bg-error hover:bg-error/90 text-white w-full flex items-center justify-center space-x-2"
                            style={{ color: 'white', backgroundColor: '#ef4444' }}
                            onClick={disconnectStream}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">{streamStatus === 'conectando' ? 'Cancelar' : 'Desconectar'}</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </form>
              </>
            )}

            <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-900 shadow-md" style={{ minHeight: '480px' }}>
              {showStream ? (
                <>
                  {streamStatus === 'conectando' && !loadingTimeout && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10 backdrop-blur-sm">
                      <div className="text-center">
                        <div className="inline-block h-10 w-10 animate-spin rounded-full border-4 border-white border-t-transparent"></div>
                        <p className="mt-3 text-white font-medium">Conectando à câmera...</p>
                      </div>
                    </div>
                  )}

                  {selectedProtocol === 'reolink' && !isRemoteAccess ? (
                    <iframe
                      src={`https://p2p.reolink.com/live.html?uid=${cameraConfig.username}&password=${cameraConfig.password}`}
                      className="w-full h-full border-0"
                      allow="camera;microphone;fullscreen;autoplay"
                      sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                      referrerPolicy="no-referrer"
                      onError={() => {
                        setStreamStatus('erro');
                        setError('Erro na conexão com a câmera. Tente novamente.');
                      }}
                    />
                  ) : selectedProtocol === 'webrtc' ? (
                    <iframe
                      src={getStreamUrl(selectedProtocol)}
                      className="w-full h-full border-0"
                      allow="camera;microphone;fullscreen;autoplay"
                      sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                      referrerPolicy="no-referrer"
                      onError={() => {
                        setStreamStatus('erro');
                        setError('Erro na conexão com a câmera. Tente usar outro protocolo.');
                      }}
                    />
                  ) : selectedProtocol === 'flv' ? (
                    <VideoPlayer
                      url={getStreamUrl(selectedProtocol)}
                      protocol={selectedProtocol}
                      onError={(errorMessage) => {
                        setStreamStatus('erro');
                        setError(errorMessage);
                      }}
                    />
                  ) : selectedProtocol === 'http' ? (
                    <>
                      {/* Tratamento especial para DroidCam */}
                      {cameraConfig.port === '4747' ? (
                        <div className="relative w-full h-full bg-black overflow-hidden">
                          {/* Para DroidCam, usamos uma abordagem dupla: img para MJPEG e iframe como fallback */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <img
                              ref={(el) => {
                                if (el) {
                                  // @ts-ignore
                                  window.droidCamImg = el;
                                }
                              }}
                              src={`http://${cameraConfig.ip}:${cameraConfig.port}/video`}
                              className="w-full h-full object-contain"
                              alt="DroidCam stream"
                              style={{
                                backgroundColor: 'black'
                              }}
                              onLoad={() => {
                                setStreamStatus('conectado');
                                console.log('DroidCam stream carregado com sucesso via img');
                              }}
                              onError={(e) => {
                                console.error('Erro ao carregar DroidCam stream via img:', e);
                                // Não mostramos erro aqui, pois tentaremos o iframe como fallback

                                // Escondemos a imagem para mostrar o iframe
                                if (e.currentTarget) {
                                  e.currentTarget.style.display = 'none';
                                }

                                // Mostramos o iframe como fallback
                                const iframeEl = document.getElementById('droidcam-iframe');
                                if (iframeEl) {
                                  iframeEl.style.display = 'block';
                                }
                              }}
                            />

                            {/* Iframe como fallback, inicialmente escondido */}
                            <iframe
                              id="droidcam-iframe"
                              ref={(el) => {
                                if (el) {
                                  // @ts-ignore
                                  window.httpIframe = el;
                                  // Inicialmente escondido
                                  el.style.display = 'none';
                                }
                              }}
                              src={getStreamUrl('http')}
                              className="w-full h-full border-0 scale-100 transform-gpu"
                              style={{
                                objectFit: 'contain',
                                backgroundColor: 'black'
                              }}
                              allow="camera;microphone;fullscreen;autoplay"
                              referrerPolicy="no-referrer"
                              onLoad={() => {
                                setStreamStatus('conectado');
                                console.log('DroidCam stream carregado com sucesso via iframe');
                              }}
                              onError={(e) => {
                                console.error('Erro ao carregar DroidCam stream via iframe:', e);
                                // Se ambos falharem, mostramos o erro
                                const imgEl = document.querySelector('[ref="droidCamImg"]');
                                if (!imgEl || (imgEl as HTMLElement).style.display === 'none') {
                                  setStreamStatus('erro');
                                  setError('Erro na conexão com o DroidCam. Verifique se o aplicativo está em execução e se o IP e porta estão corretos.');
                                }
                              }}
                            />
                          </div>

                          {/* Botões de controle para o DroidCam */}
                          <div className="absolute top-2 right-2 flex space-x-2 z-20">
                            {/* Botão para abrir em nova janela */}
                            <button
                              className="bg-white/80 hover:bg-white p-2 rounded-full shadow-md"
                              onClick={() => {
                                const url = getStreamUrl('http');
                                window.open(url, '_blank', 'noopener,noreferrer');
                              }}
                              title="Abrir em nova janela"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                            </button>

                            {/* Botão para mostrar URL (debug) */}
                            <button
                              className="bg-yellow-400/80 hover:bg-yellow-400 p-2 rounded-full shadow-md"
                              onClick={() => {
                                const url = getStreamUrl('http');
                                alert(`URL do DroidCam: ${url}`);
                              }}
                              title="Mostrar URL (debug)"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>

                            {/* Botão de atualização para o iframe */}
                            <button
                              className="bg-white/80 hover:bg-white p-2 rounded-full shadow-md"
                              onClick={() => {
                                // Atualiza a imagem primeiro (método principal)
                                // @ts-ignore
                                if (window.droidCamImg) {
                                  // @ts-ignore
                                  const img = window.droidCamImg;

                                  // Gera um timestamp para evitar cache
                                  const timestamp = new Date().getTime();
                                  const newSrc = `http://${cameraConfig.ip}:${cameraConfig.port}/video?t=${timestamp}`;
                                  console.log('Atualizando DroidCam img com URL:', newSrc);

                                  // Efeito visual de atualização
                                  img.style.opacity = '0.5';

                                  // Força recarregar a imagem
                                  img.src = newSrc;

                                  setTimeout(() => {
                                    img.style.opacity = '1';
                                  }, 300);
                                }

                                // Também atualiza o iframe (fallback)
                                // @ts-ignore
                                if (window.httpIframe) {
                                  // @ts-ignore
                                  const iframe = window.httpIframe;

                                  // Obtém a URL completa com timestamp atualizado
                                  const newSrc = getStreamUrl('http');
                                  console.log('Atualizando DroidCam iframe com URL:', newSrc);

                                  // Atualiza o iframe
                                  iframe.src = '';
                                  setTimeout(() => {
                                    iframe.src = newSrc;
                                  }, 100);
                                }
                              }}
                              title="Atualizar stream"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            </button>

                            {/* Botão para tela cheia */}
                            <button
                              className="bg-white/80 hover:bg-white p-2 rounded-full shadow-md"
                              onClick={() => {
                                // @ts-ignore
                                if (window.httpIframe) {
                                  // @ts-ignore
                                  const iframe = window.httpIframe;
                                  if (iframe.requestFullscreen) {
                                    iframe.requestFullscreen();
                                  }
                                }
                              }}
                              title="Tela cheia"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                              </svg>
                            </button>
                          </div>

                          {/* Status de conexão */}
                          <div className="absolute top-2 left-2 bg-green-500/80 text-white px-2 py-1 rounded text-xs z-20">
                            <span>Conectado</span>
                          </div>

                          {/* Botão para testar URL direta */}
                          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30">
                            <button
                              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded shadow-lg flex items-center space-x-2"
                              onClick={() => {
                                const url = `http://${cameraConfig.ip}:${cameraConfig.port}/video`;
                                window.open(url, '_blank', 'noopener,noreferrer');
                              }}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                              <span>Testar URL Direta</span>
                            </button>
                          </div>

                          {/* Mensagem de ajuda para DroidCam */}
                          <div className="absolute bottom-20 left-0 right-0 bg-black/70 text-white p-2 text-center text-xs z-20">
                            <p>Se o stream não aparecer, verifique se o aplicativo DroidCam está em execução no seu dispositivo.</p>
                          </div>
                        </div>
                      ) : (
                        /* Para outras câmeras HTTP */
                        <div className="relative w-full h-full bg-black overflow-hidden">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <iframe
                              ref={(el) => {
                                if (el) {
                                  // @ts-ignore
                                  window.httpIframe = el;
                                }
                              }}
                              src={getStreamUrl('http')}
                              className="w-full h-full border-0 scale-100 transform-gpu"
                              style={{
                                objectFit: 'contain',
                                backgroundColor: 'black'
                              }}
                              allow="camera;microphone;fullscreen;autoplay"
                              referrerPolicy="no-referrer"
                              onLoad={() => {
                                setStreamStatus('conectado');
                                console.log('HTTP stream carregado com sucesso');
                              }}
                              onError={(e) => {
                                console.error('Erro ao carregar HTTP stream:', e);
                                setStreamStatus('erro');
                                setError('Erro na conexão com a câmera HTTP. Tente usar outro protocolo ou verifique a URL.');
                              }}
                            />
                          </div>

                          {/* Status de conexão */}
                          <div className="absolute top-2 left-2 bg-green-500/80 text-white px-2 py-1 rounded text-xs z-20">
                            <span>Conectado</span>
                          </div>

                          {/* Botões de controle para o iframe */}
                          <div className="absolute top-2 right-2 flex space-x-2 z-20">
                            {/* Botão para abrir em nova janela */}
                            <button
                              className="bg-white/80 hover:bg-white p-2 rounded-full shadow-md"
                              onClick={() => {
                                const url = getStreamUrl('http');
                                window.open(url, '_blank', 'noopener,noreferrer');
                              }}
                              title="Abrir em nova janela"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                            </button>

                            {/* Botão de atualização para o iframe */}
                            <button
                              className="bg-white/80 hover:bg-white p-2 rounded-full shadow-md"
                              onClick={() => {
                                // @ts-ignore
                                if (window.httpIframe) {
                                  // @ts-ignore
                                  const iframe = window.httpIframe;

                                  // Obtém a URL completa com timestamp atualizado
                                  const newSrc = getStreamUrl('http');
                                  console.log('Atualizando HTTP stream com URL:', newSrc);

                                  // Efeito visual de atualização
                                  iframe.style.opacity = '0.5';
                                  iframe.src = '';

                                  setTimeout(() => {
                                    iframe.src = newSrc;
                                    setTimeout(() => {
                                      iframe.style.opacity = '1';
                                    }, 300);
                                  }, 100);
                                }
                              }}
                              title="Atualizar stream"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            </button>

                            {/* Botão para tela cheia */}
                            <button
                              className="bg-white/80 hover:bg-white p-2 rounded-full shadow-md"
                              onClick={() => {
                                // @ts-ignore
                                if (window.httpIframe) {
                                  // @ts-ignore
                                  const iframe = window.httpIframe;
                                  if (iframe.requestFullscreen) {
                                    iframe.requestFullscreen();
                                  }
                                }
                              }}
                              title="Tela cheia"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  ) : selectedProtocol === 'rtsp' || selectedProtocol === 'tcp' || selectedProtocol === 'udp' ? (
                    <div className="w-full h-full flex items-center justify-center bg-black text-white flex-col p-6">
                      <p className="mb-4 text-lg">Stream {selectedProtocol.toUpperCase()} disponível em:</p>
                      <div className="w-full max-w-lg bg-gray-800 p-3 rounded-md overflow-x-auto">
                        <code className="text-sm">{getStreamUrl(selectedProtocol)}</code>
                      </div>
                      <p className="mt-4 text-sm text-gray-300">Use VLC ou outro player compatível com {selectedProtocol.toUpperCase()}</p>
                      {(selectedProtocol === 'tcp' || selectedProtocol === 'udp') && (
                        <div className="mt-4 text-xs bg-blue-900/50 p-3 rounded-md max-w-lg">
                          <p className="font-medium mb-2 text-blue-200">Dicas para {selectedProtocol.toUpperCase()}:</p>
                          <ul className="list-disc list-inside space-y-1 text-gray-300">
                            <li>Para VLC: Media → Abrir Fluxo de Rede → Cole a URL</li>
                            <li>Verifique se a porta {cameraConfig.port} está aberta no firewall</li>
                            <li>Para {selectedProtocol === 'tcp' ? 'TCP' : 'UDP'}, adicione opções como "transport={selectedProtocol}" nas configurações da câmera</li>
                          </ul>
                        </div>
                      )}
                      <button
                        className="mt-5 btn bg-info hover:bg-info/90 text-white flex items-center space-x-2"
                        onClick={() => {
                          const url = getStreamUrl(selectedProtocol);
                          navigator.clipboard.writeText(url);
                          alert('URL copiada para a área de transferência!');
                        }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                          <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                        </svg>
                        <span>Copiar URL</span>
                      </button>
                    </div>
                  ) : selectedProtocol === 'mjpeg' ? (
                    <img
                      ref={imgRef}
                      src={getStreamUrl('mjpeg')}
                      className="w-full h-full object-contain"
                      alt="Camera stream"
                      onError={() => {
                        setStreamStatus('erro');
                        setError('Erro ao carregar stream MJPEG. Tente outro protocolo.');
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-white">
                      <p>Protocolo não suportado diretamente no navegador</p>
                    </div>
                  )}

                  {error && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/70 backdrop-blur-sm z-20">
                      <div className="rounded-lg bg-white p-6 text-center max-w-md shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-error mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-medium mb-2">Problema de conexão</h3>
                        <p className="mb-4 text-error">{error}</p>

                        {connectivityStatus && (
                          <div className="mb-4 text-left text-sm">
                            <p className="mb-2 font-medium">Status da conexão:</p>
                            <ul className="space-y-2">
                              <li className="flex items-center">
                                <span className={`inline-block w-3 h-3 rounded-full mr-2 ${connectivityStatus.internet ? 'bg-success' : 'bg-error'}`}></span>
                                Internet: {connectivityStatus.internet ? 'Conectado' : 'Desconectado'}
                              </li>
                              {selectedProtocol !== 'http' && (
                                <li className="flex items-center">
                                  <span className={`inline-block w-3 h-3 rounded-full mr-2 ${connectivityStatus.server ? 'bg-success' : 'bg-error'}`}></span>
                                  Servidor Reolink: {connectivityStatus.server ? 'Disponível' : 'Indisponível'}
                                </li>
                              )}
                              <li className="flex items-center">
                                <span className={`inline-block w-3 h-3 rounded-full mr-2 ${connectivityStatus.local ? 'bg-success' : 'bg-error'}`}></span>
                                Câmera local: {connectivityStatus.local ? 'Acessível' : 'Inacessível'}
                              </li>
                            </ul>
                          </div>
                        )}

                        <div className="flex flex-wrap gap-2 justify-center">
                          <button
                            className="btn bg-primary text-white font-medium"
                            style={{ color: 'white', backgroundColor: '#3b82f6' }}
                            onClick={async () => {
                              setError(null);
                              setStreamStatus('conectando');
                              await initStream();
                            }}
                          >
                            Tentar Novamente
                          </button>

                          {selectedProtocol !== 'http' && connectivityStatus && !connectivityStatus.server && connectivityStatus.internet && (
                            <>
                              {connectivityStatus.local ? (
                                <button
                                  className="btn bg-warning text-gray-800 font-medium"
                                  style={{ color: '#1f2937', backgroundColor: '#fbbf24' }}
                                  onClick={toggleDirectConnection}
                                >
                                  Usar Conexão Direta
                                </button>
                              ) : (
                                <button
                                  className="btn bg-info text-white font-medium"
                                  style={{ color: 'white', backgroundColor: '#0ea5e9' }}
                                  onClick={() => router.push('/cameras')}
                                >
                                  Gerenciar Câmeras
                                </button>
                              )}
                            </>
                          )}

                          {selectedProtocol === 'http' && !connectivityStatus.local && (
                            <>
                              {/* Verificação especial para DroidCam */}
                              {cameraConfig.port === '4747' ? (
                                <button
                                  className="btn bg-green-600 text-white font-medium"
                                  style={{ color: 'white', backgroundColor: '#16a34a' }}
                                  onClick={() => {
                                    const url = getStreamUrl('http');
                                    window.open(url, '_blank', 'noopener,noreferrer');
                                  }}
                                >
                                  Abrir DroidCam em Nova Janela
                                </button>
                              ) : (
                                <button
                                  className="btn bg-info text-white font-medium"
                                  style={{ color: 'white', backgroundColor: '#0ea5e9' }}
                                  onClick={() => router.push('/cameras')}
                                >
                                  Gerenciar Câmeras
                                </button>
                              )}
                            </>
                          )}

                          <button
                            className="btn bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium"
                            style={{ color: '#1f2937', backgroundColor: '#e5e7eb' }}
                            onClick={disconnectStream}
                          >
                            Cancelar
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="flex h-full items-center justify-center p-8">
                  <div className="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-800">Nenhum streaming ativo</h3>
                    <p className="mt-2 text-gray-600">Selecione uma câmera e clique em Iniciar Stream</p>
                  </div>
                </div>
              )}
            </div>

            {showStream && !fullscreen && streamStatus === 'conectado' && (
              <div className="mt-4">
                <div className="rounded-lg bg-white p-4 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium text-gray-800">Informações da câmera</h3>
                    <div className="badge badge-success">
                      <div className="mr-1.5 h-2 w-2 rounded-full bg-success"></div>
                      Conectado
                    </div>
                  </div>
                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
                    <div>
                      <span className="text-gray-500 text-sm">Nome:</span>
                      <div className="font-medium">{cameraConfig.nome || 'N/A'}</div>
                    </div>
                    <div>
                      <span className="text-gray-500 text-sm">IP:</span>
                      <div className="font-medium">{cameraConfig.ip}</div>
                    </div>
                    <div>
                      <span className="text-gray-500 text-sm">Protocolo:</span>
                      <div className="font-medium">{selectedProtocol.toUpperCase()}</div>
                    </div>
                    <div>
                      <span className="text-gray-500 text-sm">Modo de Acesso:</span>
                      <div className="font-medium">
                        {isRemoteAccess
                          ? 'Remoto (Internet)'
                          : selectedProtocol === 'reolink'
                            ? 'P2P Reolink'
                            : selectedProtocol === 'http'
                              ? 'HTTP Direto'
                              : 'Conexão Local'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 sticky top-20">
              <h3 className="font-medium text-gray-800 mb-4">Configuração de Câmeras</h3>

              <div className="space-y-4">
                <p className="text-sm text-gray-600">Para configurar câmeras TCP/UDP, siga estas etapas:</p>

                <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                  <li>Adicione uma nova câmera clicando em "Adicionar Câmera"</li>
                  <li>Selecione o protocolo TCP ou UDP</li>
                  <li>Preencha o endereço IP, porta e credenciais da câmera</li>
                  <li>Configure opções adicionais se necessário</li>
                </ol>

                <div className="pt-2">
                  <button
                    onClick={() => setShowAddCameraModal(true)}
                    className="btn bg-blue-500 hover:bg-blue-600 text-white w-full flex items-center justify-center space-x-2"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span>Adicionar Câmera</span>
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg shadow-sm border border-blue-100 p-5 mt-5">
              <h3 className="font-medium text-gray-800 mb-4">Ajuda Rápida</h3>

              <div className="space-y-3 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-blue-700">WebRTC</h4>
                  <p>Melhor para navegadores modernos. Baixa latência, mas requer suporte da câmera.</p>
                </div>

                <div>
                  <h4 className="font-medium text-blue-700">RTSP</h4>
                  <p>Protocolo padrão para câmeras IP. Requer aplicativo externo como VLC.</p>
                </div>

                <div>
                  <h4 className="font-medium text-blue-700">MJPEG</h4>
                  <p>Compatível com todos navegadores, mas maior consumo de banda.</p>
                </div>

                <div>
                  <h4 className="font-medium text-blue-700">Reolink P2P</h4>
                  <p>Para câmeras Reolink com acesso remoto via nuvem.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal para adicionar nova câmera */}
      {showAddCameraModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm">
          <div className="w-full max-w-lg rounded-lg bg-white p-6 shadow-lg animate-fade-in">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-800">Adicionar Nova Câmera</h2>
              <button
                type="button"
                className="rounded-full p-1.5 text-gray-500 hover:bg-gray-100 transition-colors"
                onClick={() => setShowAddCameraModal(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {saveError && (
              <div className="mb-4 rounded-md bg-error/10 p-3 text-error">
                <p className="text-sm">{saveError}</p>
              </div>
            )}

            <form onSubmit={handleAddCamera} className="space-y-4">
              <div>
                <label htmlFor="nome" className="form-label">
                  Nome da Câmera
                </label>
                <input
                  type="text"
                  id="nome"
                  name="nome"
                  className="form-input"
                  placeholder="Ex: Câmera Entrada Principal"
                  value={newCamera.nome}
                  onChange={handleNewCameraChange}
                  disabled={savingCamera}
                  required
                />
              </div>

              <div>
                <label htmlFor="ip" className="form-label">
                  Endereço IP
                </label>
                <input
                  type="text"
                  id="ip"
                  name="ip"
                  className="form-input"
                  placeholder="Ex: *************"
                  value={newCamera.ip}
                  onChange={handleNewCameraChange}
                  disabled={savingCamera}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="username" className="form-label">
                    Usuário {newCamera.protocol !== 'http' && <span className="text-red-500">*</span>}
                    {newCamera.protocol === 'http' && <span className="text-xs text-gray-500 ml-1">(opcional)</span>}
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    className="form-input"
                    placeholder="Ex: admin"
                    value={newCamera.username}
                    onChange={handleNewCameraChange}
                    disabled={savingCamera}
                    required={newCamera.protocol !== 'http'}
                  />
                </div>

                <div>
                  <label htmlFor="password" className="form-label">
                    Senha {newCamera.protocol !== 'http' && <span className="text-red-500">*</span>}
                    {newCamera.protocol === 'http' && <span className="text-xs text-gray-500 ml-1">(opcional)</span>}
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    className="form-input"
                    placeholder="••••••••"
                    value={newCamera.password}
                    onChange={handleNewCameraChange}
                    disabled={savingCamera}
                    required={newCamera.protocol !== 'http'}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="port" className="form-label">
                    Porta
                  </label>
                  <input
                    type="text"
                    id="port"
                    name="port"
                    className="form-input"
                    placeholder="Ex: 554"
                    value={newCamera.port}
                    onChange={handleNewCameraChange}
                    disabled={savingCamera}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="protocol" className="form-label">
                    Protocolo
                  </label>
                  <select
                    id="protocol"
                    name="protocol"
                    className="form-input form-select"
                    value={newCamera.protocol}
                    onChange={handleNewCameraChange}
                    disabled={savingCamera}
                  >
                    <optgroup label="Protocolos Comuns">
                      <option value="rtsp">RTSP</option>
                      <option value="tcp">TCP</option>
                      <option value="udp">UDP</option>
                      <option value="http">HTTP</option>
                    </optgroup>
                    <optgroup label="Streaming Web">
                      <option value="webrtc">WebRTC</option>
                      <option value="flv">FLV</option>
                      <option value="mjpeg">MJPEG</option>
                      <option value="hls">HLS</option>
                    </optgroup>
                    <optgroup label="Proprietários">
                      <option value="reolink">Reolink P2P</option>
                      <option value="onvif">ONVIF</option>
                    </optgroup>
                  </select>
                  {newCamera.protocol === 'http' && (
                    <p className="mt-1 text-xs text-blue-600">
                      Para câmeras DroidCam e outras sem autenticação, deixe os campos de usuário e senha vazios.
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label htmlFor="url_path" className="form-label">
                  Caminho URL <span className="text-xs text-gray-500">(opcional)</span>
                </label>
                <input
                  type="text"
                  id="url_path"
                  name="url_path"
                  className="form-input"
                  placeholder="Ex: /video/stream"
                  value={newCamera.url_path || ''}
                  onChange={handleNewCameraChange}
                  disabled={savingCamera}
                />
                <p className="mt-1 text-xs text-gray-500">Para câmeras TCP/UDP, especifique o caminho da URL se necessário</p>
              </div>

              <div>
                <label htmlFor="connection_options" className="form-label">
                  Opções de Conexão <span className="text-xs text-gray-500">(opcional)</span>
                </label>
                <input
                  type="text"
                  id="connection_options"
                  name="connection_options"
                  className="form-input"
                  placeholder="Ex: transport=tcp&timeout=30"
                  value={newCamera.connection_options || ''}
                  onChange={handleNewCameraChange}
                  disabled={savingCamera}
                />
                <p className="mt-1 text-xs text-gray-500">Parâmetros adicionais para a conexão, separados por & (ex: transport=tcp)</p>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  className="btn bg-gray-200 hover:bg-gray-300 text-gray-800"
                  onClick={() => setShowAddCameraModal(false)}
                  disabled={savingCamera}
                >
                  Cancelar
                </button>

                <button
                  type="submit"
                  className="btn bg-blue-500 hover:bg-blue-600 text-white relative"
                  disabled={savingCamera}
                >
                  {savingCamera ? (
                    <>
                      <span className="opacity-0">Salvar</span>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      </div>
                    </>
                  ) : (
                    'Salvar'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </AppLayout>
  );
};

export default Streaming;
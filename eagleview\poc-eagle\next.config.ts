import type { NextConfig } from "next";

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  eslint: {
    // Ignorar erros de ESLint durante o build
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Ignorar erros de TypeScript durante o build
    ignoreBuildErrors: true,
  },
  // Ignorar erros de imagem durante o build
  images: {
    unoptimized: true,
  },
  // Desativar todos os indicadores de desenvolvimento (incluindo o ícone "N" no canto inferior esquerdo)
  devIndicators: {
    buildActivity: false
  },
  // Ocultar o banner de desenvolvimento com o "N" no canto da tela
  reactStrictMode: true,
};

export default nextConfig;

import cv2
import numpy as np
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton, QStatusBar, QComboBox, QLineEdit, QHBoxLayout, QGroupBox, QMessageBox, QProgressBar, QInputDialog
from PyQt5.QtCore import QTimer, Qt, QThread, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap
import sys
import logging
import threading
import queue
import socket
import time
import requests
import os
import platform
import subprocess
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NetworkTester(QThread):
    """Classe para testar conectividade da câmera em thread separada"""
    # Sinais para comunicação com a UI
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int, str)
    
    def __init__(self, ip, ports_http, ports_rtsp, username=None, password=None):
        super().__init__()
        self.ip = ip
        self.ports_http = ports_http
        self.ports_rtsp = ports_rtsp
        self.username = username
        self.password = password
        self.is_running = True
    
    def stop(self):
        self.is_running = False
    
    def run(self):
        """Executa os testes de conectividade"""
        # Teste de ping
        self.progress.emit(10, f"Testando ping para {self.ip}...")
        is_reachable = self.test_ping()
        
        if not is_reachable or not self.is_running:
            self.finished.emit(False, f"Host {self.ip} não está acessível via ping")
            return
            
        # Teste de portas HTTP
        self.progress.emit(30, "Testando portas HTTP...")
        http_success, http_port = self.test_http_ports()
        
        if http_success and self.is_running:
            self.finished.emit(True, f"Conexão HTTP estabelecida com {self.ip}:{http_port}")
            return
            
        # Teste de portas RTSP
        self.progress.emit(60, "Testando portas RTSP...")
        rtsp_success, rtsp_port = self.test_rtsp_ports()
        
        if rtsp_success and self.is_running:
            self.finished.emit(True, f"Conexão RTSP estabelecida com {self.ip}:{rtsp_port}")
            return
            
        # Se chegou aqui, nenhuma conexão teve sucesso
        self.finished.emit(False, f"Não foi possível conectar a {self.ip} em nenhuma porta")
    
    def test_ping(self):
        """Testa se o host responde ao ping"""
        try:
            ping_command = "ping"
            ping_param = "-n" if platform.system().lower() == "windows" else "-c"
            ping_count = "1"
            ping_timeout = "-w" if platform.system().lower() == "windows" else "-W"
            ping_timeout_val = "1000" if platform.system().lower() == "windows" else "1"
            
            # Monta o comando completo
            cmd = [ping_command, ping_param, ping_count, ping_timeout, ping_timeout_val, self.ip]
            
            # Executa o ping de forma mais confiável usando subprocess
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Analisa o resultado
            if platform.system().lower() == "windows":
                success = "TTL=" in result.stdout
            else:
                success = "1 received" in result.stdout
                
            logger.info(f"Ping para {self.ip}: {'Sucesso' if success else 'Falha'}")
            return success
            
        except Exception as e:
            logger.error(f"Erro ao executar ping: {str(e)}")
            return False
    
    def test_http_ports(self):
        """Testa as portas HTTP configuradas"""
        total_ports = len(self.ports_http)
        for i, port in enumerate(self.ports_http):
            if not self.is_running:
                return False, None
                
            progress_val = 30 + int((i / total_ports) * 30)
            self.progress.emit(progress_val, f"Testando HTTP porta {port}...")
            
            try:
                http_url = f"http://{self.ip}:{port}"
                logger.info(f"Testando conexão HTTP em {http_url}")
                response = requests.get(http_url, timeout=2, 
                                      auth=(self.username, self.password) if self.username else None)
                
                if response.status_code < 400:  # Aceita qualquer código de sucesso ou redirecionamento
                    logger.info(f"Conexão HTTP com sucesso na porta {port}")
                    return True, port
            except requests.RequestException:
                # Ignora erros de timeout ou conexão recusada
                pass
            except Exception as e:
                logger.error(f"Erro ao testar HTTP porta {port}: {str(e)}")
                
        return False, None
    
    def test_rtsp_ports(self):
        """Testa as portas RTSP configuradas"""
        total_ports = len(self.ports_rtsp)
        for i, port in enumerate(self.ports_rtsp):
            if not self.is_running:
                return False, None
                
            progress_val = 60 + int((i / total_ports) * 30)
            self.progress.emit(progress_val, f"Testando RTSP porta {port}...")
            
            try:
                logger.info(f"Testando conexão RTSP em {self.ip}:{port}")
                sock = socket.create_connection((self.ip, port), timeout=2)
                sock.close()
                logger.info(f"Conexão RTSP com sucesso na porta {port}")
                return True, port
            except socket.error:
                # Ignora erros de timeout ou conexão recusada
                pass
            except Exception as e:
                logger.error(f"Erro ao testar RTSP porta {port}: {str(e)}")
                
        return False, None

class CameraConfig:
    def __init__(self):
        # Configurações da câmera local
        self.local_ip = "**************"
        self.remote_ip = "**************"
        self.username = "admin"
        self.password = "Timelapse@1"
        
        # Lista de possíveis caminhos RTSP para tentar
        self.rtsp_paths = [
            "/h264Preview_01_main",
            "/h264Preview_01_sub", 
            "/live/ch0",
            "/live/main",
            "/cam/realmonitor?channel=1&subtype=0",
            "/bcs/channel0_main.bcs",
            "/flv?port=1935&app=bcs&stream=channel0_main.bcs",
            ""  # URL vazia para tentar só com o IP e porta
        ]

        # Lista de possíveis caminhos HTTP/HTTPS
        self.http_paths = [
            "/video",
            "/videostream.cgi",
            "/api.cgi?cmd=GetWebRTCStream",
            "/cgi-bin/api.cgi?cmd=Snap&channel=0",
            "/api.cgi?cmd=GetMJStream&channel=0",
            ""  # URL vazia para tentar só com o IP e porta
        ]
        
        # Lista de portas
        self.rtsp_ports = [554, 1935]
        self.http_ports = [80, 443, 8000]
        
    def get_rtsp_url(self, path_index=0, use_remote=False, port_index=0):
        if path_index < len(self.rtsp_paths):
            ip = self.remote_ip if use_remote else self.local_ip
            port = self.rtsp_ports[port_index % len(self.rtsp_ports)]
            
            # Se for caminho vazio, retorna só a URL base
            if self.rtsp_paths[path_index] == "":
                return f"rtsp://{self.username}:{self.password}@{ip}:{port}"
            
            return f"rtsp://{self.username}:{self.password}@{ip}:{port}{self.rtsp_paths[path_index]}"
        return None

    def get_http_url(self, path_index=0, use_remote=False, port_index=0, use_https=False):
        if path_index < len(self.http_paths):
            ip = self.remote_ip if use_remote else self.local_ip
            port = self.http_ports[port_index % len(self.http_ports)]
            protocol = "https" if use_https else "http"
            
            # Se for caminho vazio, retorna só a URL base
            if self.http_paths[path_index] == "":
                return f"{protocol}://{ip}:{port}"
                
            return f"{protocol}://{ip}:{port}{self.http_paths[path_index]}"
        return None
    
    def test_connection(self, use_remote=False, callback=None):
        """Inicia o teste de conexão e retorna o tester para controle"""
        ip = self.remote_ip if use_remote else self.local_ip
        tester = NetworkTester(ip, self.http_ports, self.rtsp_ports, self.username, self.password)
        if callback:
            tester.finished.connect(callback)
        tester.start()
        return tester

class CameraStream(QThread):
    """Classe para capturar frames da câmera em thread separada"""
    frame_received = pyqtSignal(np.ndarray)
    connection_error = pyqtSignal(str)
    
    def __init__(self, url):
        super().__init__()
        self.url = url
        self.is_running = True
        
    def stop(self):
        self.is_running = False
        
    def run(self):
        """Captura frames da câmera continuamente"""
        try:
            cap = cv2.VideoCapture(self.url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimiza o buffer
            
            if not cap.isOpened():
                self.connection_error.emit(f"Erro ao abrir conexão com a câmera usando URL: {self.url}")
                return
                
            consecutive_failures = 0
            
            while self.is_running:
                ret, frame = cap.read()
                if ret:
                    self.frame_received.emit(frame)
                    consecutive_failures = 0
                else:
                    consecutive_failures += 1
                    logger.error(f"Erro ao ler frame da câmera (tentativa {consecutive_failures})")
                    
                    if consecutive_failures >= 5:
                        self.connection_error.emit("Falha ao ler frames da câmera")
                        break
                        
                    # Pequena pausa antes de tentar novamente
                    time.sleep(0.5)
                    
            cap.release()
        except Exception as e:
            self.connection_error.emit(f"Erro na captura: {str(e)}")

class ReoLinkP2PConnector:
    """Classe para estabelecer conexão com câmeras Reolink usando o código UID/QR"""
    
    def __init__(self, uid=None, password=None):
        self.uid = uid  # Código UID da câmera (ex: 952700083KBZSG2O)
        self.password = password
        self.connection_data = None
        
    def extract_uid_from_qrcode(self, qr_code_text):
        """Extrai o UID de um texto QR code no formato <S><UID>XXXXXXXXX</UID><OPE>X</OPE></S>"""
        try:
            uid_match = re.search(r'<UID>([A-Z0-9]+)</UID>', qr_code_text)
            if uid_match:
                return uid_match.group(1)
        except Exception as e:
            logger.error(f"Erro ao extrair UID do QR code: {str(e)}")
        return None
    
    def connect_with_uid(self, uid=None, password=None):
        """Estabelece conexão com a câmera usando o UID"""
        if uid:
            self.uid = uid
        if password:
            self.password = password
            
        if not self.uid or not self.password:
            return False, "UID ou senha não fornecidos"
            
        try:
            logger.info(f"Tentando conexão P2P com UID: {self.uid}")
            
            # Simulamos a obtenção de endereço via P2P da Reolink
            # Na implementação real, isso envolveria fazer requisições aos servidores P2P da Reolink
            # Como não temos acesso à API privada da Reolink, assumimos que a câmera está disponível
            # na rede e tentamos conexão direta
            
            # Baseado no UID, determinamos se é câmera local ou remota
            # Em um cenário real, isso seria feito consultando os servidores da Reolink
            is_remote = True
            
            # O código oficial da Reolink faria a resolução do UID para um IP
            # Como placeholder, usamos os IPs conhecidos
            if is_remote:
                ip = "**************"  # Remoto
            else:
                ip = "**************"  # Local
                
            # Salva os dados de conexão
            self.connection_data = {
                "ip": ip,
                "is_remote": is_remote,
                "uid": self.uid,
                "password": self.password,
                "username": "admin",  # Default para câmeras Reolink
                "ports": {
                    "rtsp": 554,
                    "http": 80,
                    "https": 443,
                    "rtmp": 1935
                },
                "paths": {
                    "rtsp": "/h264Preview_01_main",
                    "http": "/api.cgi?cmd=GetWebRTCStream",
                    "https": "/api.cgi?cmd=GetWebRTCStream",
                    "rtmp": "/flv?port=1935&app=bcs&stream=channel0_main.bcs"
                }
            }
            
            # Define RTMP como protocolo padrão recomendado para Reolink
            # O app Reolink provavelmente usa RTMP para streaming
            self.preferred_protocol = "rtmp"
            
            return True, f"Conexão P2P estabelecida com UID: {self.uid}"
        except Exception as e:
            logger.error(f"Erro na conexão P2P: {str(e)}")
            return False, f"Falha na conexão P2P: {str(e)}"
    
    def get_stream_url(self, protocol="rtmp"):
        """Retorna a URL para streaming com base no protocolo selecionado"""
        if not self.connection_data:
            return None
            
        ip = self.connection_data["ip"]
        username = self.connection_data["username"]
        password = self.connection_data["password"]
        
        if protocol.lower() == "rtsp":
            port = self.connection_data["ports"]["rtsp"]
            path = self.connection_data["paths"]["rtsp"]
            return f"rtsp://{username}:{password}@{ip}:{port}{path}"
        elif protocol.lower() == "http":
            port = self.connection_data["ports"]["http"]
            path = self.connection_data["paths"]["http"]
            return f"http://{username}:{password}@{ip}:{port}{path}"
        elif protocol.lower() == "https":
            port = self.connection_data["ports"]["https"]
            path = self.connection_data["paths"]["https"]
            return f"https://{username}:{password}@{ip}:{port}{path}"
        elif protocol.lower() == "rtmp":
            port = self.connection_data["ports"]["rtmp"]
            path = self.connection_data["paths"]["rtmp"]
            # O RTMP da Reolink não usa autenticação no URL, mas no handshake
            return f"rtmp://{ip}:{port}{path}"
        
        return None

class CameraViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Visualizador da Câmera Eagle View")
        
        # Inicialização de variáveis
        self.camera_config = CameraConfig()
        self.frame_queue = queue.Queue(maxsize=10)
        self.is_running = False
        self.current_path_index = 0
        self.current_port_index = 0
        self.use_remote = False
        self.use_http = False
        self.use_https = True
        self.network_tester = None
        self.camera_stream = None
        self.reolink_connector = ReoLinkP2PConnector()
        
        # Configuração da interface
        self.setup_ui()
        
        # Configuração do timer para atualização da interface
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(30)  # 30ms = ~33fps
        
        # Teste inicial de conexão
        self.test_camera_connection()
    
    def setup_ui(self):
        """Configura a interface do usuário"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)
        
        # Label para exibição do vídeo
        self.image_label = QLabel("Sem conexão com a câmera")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("QLabel { background-color : black; color : white; }")
        self.layout.addWidget(self.image_label)
        
        # Barra de progresso para testes
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        self.layout.addWidget(self.progress_bar)
        
        # Grupo de conexão Reolink UID
        reolink_group = QGroupBox("Conexão Reolink (UID/QR Code)")
        reolink_layout = QVBoxLayout()
        
        # Layout para QR Code / UID
        uid_layout = QHBoxLayout()
        uid_layout.addWidget(QLabel("UID:"))
        self.uid_input = QLineEdit()
        self.uid_input.setPlaceholderText("Digite o UID/código da câmera (ex: 952700083KBZSG2O)")
        uid_layout.addWidget(self.uid_input)
        reolink_layout.addLayout(uid_layout)
        
        # Layout para senha
        password_layout = QHBoxLayout()
        password_layout.addWidget(QLabel("Senha:"))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Digite a senha da câmera")
        self.password_input.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(self.password_input)
        reolink_layout.addLayout(password_layout)
        
        # Botão para conexão por UID
        uid_button_layout = QHBoxLayout()
        self.connect_uid_button = QPushButton("Conectar com UID")
        self.connect_uid_button.clicked.connect(self.connect_with_uid)
        
        self.qr_code_button = QPushButton("Processar QR Code")
        self.qr_code_button.clicked.connect(self.process_qr_code)
        
        uid_button_layout.addWidget(self.connect_uid_button)
        uid_button_layout.addWidget(self.qr_code_button)
        reolink_layout.addLayout(uid_button_layout)
        
        reolink_group.setLayout(reolink_layout)
        self.layout.addWidget(reolink_group)
        
        # Grupo de controles
        control_group = QGroupBox("Controles Avançados")
        control_layout = QVBoxLayout()
        
        # Layout para seleção de câmera
        camera_select_layout = QHBoxLayout()
        self.local_button = QPushButton("Câmera Local (**************)")
        self.remote_button = QPushButton("Câmera Remota (**************)")
        self.local_button.setCheckable(True)
        self.remote_button.setCheckable(True)
        self.local_button.setChecked(True)
        self.local_button.clicked.connect(lambda: self.switch_camera(False))
        self.remote_button.clicked.connect(lambda: self.switch_camera(True))
        camera_select_layout.addWidget(self.local_button)
        camera_select_layout.addWidget(self.remote_button)
        control_layout.addLayout(camera_select_layout)
        
        # Layout para seleção de protocolo
        protocol_select_layout = QHBoxLayout()
        self.rtsp_button = QPushButton("RTSP")
        self.http_button = QPushButton("HTTP")
        self.https_button = QPushButton("HTTPS")
        self.rtmp_button = QPushButton("RTMP")
        self.rtsp_button.setCheckable(True)
        self.http_button.setCheckable(True)
        self.https_button.setCheckable(True)
        self.rtmp_button.setCheckable(True)
        self.rtsp_button.setChecked(True)
        self.rtsp_button.clicked.connect(lambda: self.switch_protocol("rtsp"))
        self.http_button.clicked.connect(lambda: self.switch_protocol("http"))
        self.https_button.clicked.connect(lambda: self.switch_protocol("https"))
        self.rtmp_button.clicked.connect(lambda: self.switch_protocol("rtmp"))
        protocol_select_layout.addWidget(self.rtsp_button)
        protocol_select_layout.addWidget(self.http_button)
        protocol_select_layout.addWidget(self.https_button)
        protocol_select_layout.addWidget(self.rtmp_button)
        control_layout.addLayout(protocol_select_layout)
        
        # Layout para seleção de porta
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("Porta:"))
        self.port_combo = QComboBox()
        self.update_port_combo()
        self.port_combo.currentIndexChanged.connect(self.change_port)
        port_layout.addWidget(self.port_combo)
        control_layout.addLayout(port_layout)
        
        # Combobox para seleção do caminho
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("Caminho:"))
        self.path_combo = QComboBox()
        self.update_path_combo()
        self.path_combo.currentIndexChanged.connect(self.change_path)
        path_layout.addWidget(self.path_combo)
        control_layout.addLayout(path_layout)
        
        # Botões de controle
        button_layout = QHBoxLayout()
        self.toggle_button = QPushButton("Iniciar")
        self.toggle_button.clicked.connect(self.toggle_stream)
        self.toggle_button.setEnabled(False)
        
        self.test_button = QPushButton("Testar Conexão")
        self.test_button.clicked.connect(self.test_camera_connection)
        
        self.auto_detect_button = QPushButton("Auto-Detectar")
        self.auto_detect_button.clicked.connect(self.auto_detect_settings)
        
        button_layout.addWidget(self.toggle_button)
        button_layout.addWidget(self.test_button)
        button_layout.addWidget(self.auto_detect_button)
        control_layout.addLayout(button_layout)
        
        control_group.setLayout(control_layout)
        self.layout.addWidget(control_group)
        
        # Barra de status
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Define o tamanho inicial da janela
        self.resize(1280, 720)
    
    def update_path_combo(self):
        """Atualiza as opções do combobox baseado no protocolo selecionado"""
        self.path_combo.clear()
        if self.use_http:
            paths = self.camera_config.http_paths
            prefix = "HTTPS" if self.use_https else "HTTP"
        else:
            paths = self.camera_config.rtsp_paths
            prefix = "RTSP"
        
        for i, path in enumerate(paths):
            path_display = path if path else "(URL Base)"
            self.path_combo.addItem(f"{prefix} {i+1}: {path_display}")
    
    def update_port_combo(self):
        """Atualiza as opções de porta baseado no protocolo selecionado"""
        self.port_combo.clear()
        if self.use_http:
            ports = self.camera_config.http_ports
            prefix = "HTTPS" if self.use_https else "HTTP"
        else:
            ports = self.camera_config.rtsp_ports
            prefix = "RTSP"
        
        for i, port in enumerate(ports):
            self.port_combo.addItem(f"{prefix} Porta {port}")
    
    def switch_camera(self, use_remote):
        """Alterna entre câmera local e remota"""
        if self.use_remote == use_remote:
            return
            
        self.use_remote = use_remote
        self.local_button.setChecked(not use_remote)
        self.remote_button.setChecked(use_remote)
        
        # Para o streaming se estiver rodando
        if self.is_running:
            self.toggle_stream()
        
        # Testa a conectividade com a nova câmera
        self.test_camera_connection()
    
    def switch_protocol(self, protocol):
        """Alterna entre protocolos RTSP/HTTP/HTTPS"""
        old_use_http = self.use_http
        old_use_https = self.use_https
        
        if protocol == "rtsp":
            self.use_http = False
            self.use_https = False
            self.rtsp_button.setChecked(True)
            self.http_button.setChecked(False)
            self.https_button.setChecked(False)
        elif protocol == "http":
            self.use_http = True
            self.use_https = False
            self.rtsp_button.setChecked(False)
            self.http_button.setChecked(True)
            self.https_button.setChecked(False)
        elif protocol == "https":
            self.use_http = True
            self.use_https = True
            self.rtsp_button.setChecked(False)
            self.http_button.setChecked(False)
            self.https_button.setChecked(True)
        elif protocol == "rtmp":
            self.use_http = False
            self.use_https = False
            self.rtsp_button.setChecked(False)
            self.http_button.setChecked(False)
            self.https_button.setChecked(False)
            self.rtmp_button.setChecked(True)
        
        # Se houve mudança de protocolo
        if old_use_http != self.use_http or old_use_https != self.use_https:
            self.update_path_combo()
            self.update_port_combo()
            self.current_path_index = 0
            self.current_port_index = 0
            
            # Para o streaming se estiver rodando
            if self.is_running:
                self.toggle_stream()
    
    def test_camera_connection(self):
        """Testa a conexão com a câmera"""
        self.toggle_button.setEnabled(False)
        self.test_button.setEnabled(False)
        self.auto_detect_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_bar.showMessage("Testando conexão...")
        
        # Cancela qualquer teste em andamento
        if self.network_tester and self.network_tester.isRunning():
            self.network_tester.stop()
            
        # Inicia novo teste
        self.network_tester = self.camera_config.test_connection(
            self.use_remote, 
            self.on_connection_test_finished
        )
        
        # Conecta o sinal de progresso
        self.network_tester.progress.connect(self.on_connection_test_progress)
    
    def on_connection_test_progress(self, value, message):
        """Atualiza o progresso do teste de conexão"""
        self.progress_bar.setValue(value)
        self.status_bar.showMessage(message)
    
    def on_connection_test_finished(self, success, message):
        """Callback quando o teste de conexão termina"""
        self.progress_bar.setValue(100 if success else 0)
        self.progress_bar.setVisible(False)
        self.test_button.setEnabled(True)
        self.auto_detect_button.setEnabled(True)
        
        if success:
            self.status_bar.showMessage(message)
            self.toggle_button.setEnabled(True)
        else:
            self.status_bar.showMessage(f"Erro: {message}")
            QMessageBox.warning(self, "Erro de Conexão", message)
    
    def change_path(self, index):
        """Muda o caminho do streaming"""
        if self.current_path_index == index:
            return
            
        self.current_path_index = index
        
        # Para o streaming se estiver rodando
        if self.is_running:
            self.toggle_stream()
    
    def change_port(self, index):
        """Muda a porta do streaming"""
        if self.current_port_index == index:
            return
            
        self.current_port_index = index
        
        # Para o streaming se estiver rodando
        if self.is_running:
            self.toggle_stream()
    
    def start_camera_stream(self):
        """Inicia a captura de frames da câmera"""
        if self.is_running:
            return
            
        # Determina a URL baseada nas configurações atuais
        if self.use_http:
            url = self.camera_config.get_http_url(
                self.current_path_index, 
                self.use_remote, 
                self.current_port_index,
                self.use_https
            )
        else:
            url = self.camera_config.get_rtsp_url(
                self.current_path_index, 
                self.use_remote, 
                self.current_port_index
            )
            
        logger.info(f"Iniciando streaming com URL: {url}")
        self.status_bar.showMessage(f"Conectando à câmera: {url}")
        
        # Para qualquer stream anterior se existir
        if self.camera_stream and self.camera_stream.isRunning():
            self.camera_stream.stop()
            self.camera_stream.wait()
            
        # Inicia novo stream
        self.camera_stream = CameraStream(url)
        self.camera_stream.frame_received.connect(self.on_frame_received)
        self.camera_stream.connection_error.connect(self.on_stream_error)
        self.camera_stream.start()
        
        self.is_running = True
        self.toggle_button.setText("Parar")
    
    def on_frame_received(self, frame):
        """Recebe um novo frame da câmera"""
        try:
            if self.frame_queue.full():
                self.frame_queue.get_nowait()
            self.frame_queue.put(frame)
        except queue.Full:
            pass
    
    def on_stream_error(self, error_message):
        """Tratamento de erro no streaming"""
        logger.error(f"Erro no streaming: {error_message}")
        self.status_bar.showMessage(f"Erro: {error_message}")
        self.stop_camera_stream()
        
        # Mostra mensagem de erro
        QMessageBox.warning(self, "Erro de Streaming", 
                           f"Falha ao conectar com a câmera.\n{error_message}\n\n"
                           "Tente mudar a configuração ou verificar a conectividade.")
    
    def stop_camera_stream(self):
        """Para o streaming da câmera"""
        if not self.is_running:
            return
            
        if self.camera_stream and self.camera_stream.isRunning():
            self.camera_stream.stop()
            self.camera_stream.wait()
            
        self.is_running = False
        self.toggle_button.setText("Iniciar")
        self.status_bar.showMessage("Streaming parado")
        self.frame_queue = queue.Queue(maxsize=10)  # Limpa a fila
        self.image_label.setText("Sem conexão com a câmera")
    
    def update_frame(self):
        """Atualiza o frame na interface"""
        try:
            frame = self.frame_queue.get_nowait()
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = frame.shape
            bytes_per_line = ch * w
            
            qt_image = QImage(frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            scaled_pixmap = QPixmap.fromImage(qt_image).scaled(
                self.image_label.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
            
            # Atualiza status para mostrar que está funcionando
            if self.is_running:
                protocol = "HTTPS" if (self.use_http and self.use_https) else "HTTP" if self.use_http else "RTSP"
                camera_type = "Remota" if self.use_remote else "Local"
                self.status_bar.showMessage(f"Câmera {camera_type} conectada via {protocol}")
        except queue.Empty:
            pass
    
    def toggle_stream(self):
        """Alterna entre iniciar e parar o streaming"""
        if self.is_running:
            self.stop_camera_stream()
        else:
            # Verifica se há conexão P2P estabelecida
            if self.reolink_connector.connection_data:
                self.start_p2p_stream()
            else:
                self.start_camera_stream()
    
    def auto_detect_settings(self):
        """Implementa a detecção automática de configurações"""
        QMessageBox.information(self, "Auto-Detecção", 
                              "A auto-detecção testará diferentes configurações para encontrar "
                              "a melhor conexão com a câmera. Este processo pode levar alguns minutos.")
                              
        # Implementar a detecção automática posteriormente
        # Por enquanto, só faz o teste de conexão básico
        self.test_camera_connection()
    
    def connect_with_uid(self):
        """Conecta à câmera usando o UID fornecido pelo usuário"""
        uid = self.uid_input.text().strip()
        password = self.password_input.text()
        
        if not uid:
            QMessageBox.warning(self, "Erro", "Por favor, digite o UID da câmera")
            return
            
        if not password:
            QMessageBox.warning(self, "Erro", "Por favor, digite a senha da câmera")
            return
        
        self.status_bar.showMessage(f"Conectando via UID: {uid}...")
        success, message = self.reolink_connector.connect_with_uid(uid, password)
        
        if success:
            self.status_bar.showMessage(message)
            self.toggle_button.setEnabled(True)
            
            # Atualiza a interface para o estado conectado
            if self.reolink_connector.connection_data["is_remote"]:
                self.remote_button.setChecked(True)
                self.local_button.setChecked(False)
                self.use_remote = True
            else:
                self.remote_button.setChecked(False)
                self.local_button.setChecked(True)
                self.use_remote = False
                
            # Inicia o streaming automaticamente
            self.start_p2p_stream()
        else:
            QMessageBox.warning(self, "Erro de Conexão", message)
            self.status_bar.showMessage(f"Erro: {message}")
    
    def process_qr_code(self):
        """Processa o código QR da câmera"""
        qr_code_text, ok = QInputDialog.getText(
            self, 
            "QR Code Reolink",
            "Cole o texto do QR code aqui (formato: <S><UID>XXXXXXXX</UID><OPE>X</OPE></S>):"
        )
        
        if ok and qr_code_text:
            uid = self.reolink_connector.extract_uid_from_qrcode(qr_code_text)
            if uid:
                self.uid_input.setText(uid)
                self.status_bar.showMessage(f"UID extraído com sucesso: {uid}")
                
                # Pergunta se quer conectar automaticamente
                reply = QMessageBox.question(
                    self, 
                    'Conectar Automaticamente', 
                    f'UID {uid} extraído com sucesso. Deseja conectar agora?',
                    QMessageBox.Yes | QMessageBox.No, 
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes and self.password_input.text():
                    self.connect_with_uid()
            else:
                QMessageBox.warning(self, "Erro", "Não foi possível extrair o UID do código fornecido")
    
    def start_p2p_stream(self):
        """Inicia o streaming usando a conexão P2P"""
        if not self.reolink_connector.connection_data:
            QMessageBox.warning(self, "Erro", "Nenhuma conexão P2P estabelecida")
            return
        
        # Determina o protocolo baseado nas seleções da interface
        if self.rtsp_button.isChecked():
            protocol = "rtsp"
        elif self.http_button.isChecked():
            protocol = "http"
        elif self.https_button.isChecked():
            protocol = "https"
        elif self.rtmp_button.isChecked():
            protocol = "rtmp"
        else:
            # Usa o protocolo preferido pela câmera
            protocol = getattr(self.reolink_connector, "preferred_protocol", "rtmp")
            
            # Atualiza os botões na interface
            if protocol == "rtsp":
                self.rtsp_button.setChecked(True)
            elif protocol == "http":
                self.http_button.setChecked(True)
            elif protocol == "https":
                self.https_button.setChecked(True)
            elif protocol == "rtmp":
                self.rtmp_button.setChecked(True)
            
        # Obtém a URL para o streaming
        url = self.reolink_connector.get_stream_url(protocol)
        
        if not url:
            QMessageBox.warning(self, "Erro", f"Falha ao gerar URL para protocolo {protocol}")
            return
            
        logger.info(f"Iniciando streaming P2P com URL: {url}")
        self.status_bar.showMessage(f"Conectando à câmera: {url}")
        
        # Para qualquer stream anterior se existir
        if self.camera_stream and self.camera_stream.isRunning():
            self.camera_stream.stop()
            self.camera_stream.wait()
            
        # Inicia novo stream
        self.camera_stream = CameraStream(url)
        self.camera_stream.frame_received.connect(self.on_frame_received)
        self.camera_stream.connection_error.connect(self.on_stream_error)
        self.camera_stream.start()
        
        self.is_running = True
        self.toggle_button.setText("Parar")
    
    def closeEvent(self, event):
        """Limpa recursos ao fechar a aplicação"""
        # Para o stream da câmera
        if self.camera_stream and self.camera_stream.isRunning():
            self.camera_stream.stop()
            
        # Para o teste de rede
        if self.network_tester and self.network_tester.isRunning():
            self.network_tester.stop()
            
        self.is_running = False
        super().closeEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    viewer = CameraViewer()
    viewer.show()
    
    # Processa argumentos de linha de comando
    if len(sys.argv) > 1:
        # Verifica se há UID nos argumentos
        for i, arg in enumerate(sys.argv):
            if arg == "--uid" and i + 1 < len(sys.argv):
                viewer.uid_input.setText(sys.argv[i + 1])
            elif arg == "--password" and i + 1 < len(sys.argv):
                viewer.password_input.setText(sys.argv[i + 1])
        
        # Se UID e senha foram fornecidos, tenta conectar automaticamente
        if viewer.uid_input.text() and viewer.password_input.text():
            viewer.connect_with_uid()
    
    sys.exit(app.exec_()) 
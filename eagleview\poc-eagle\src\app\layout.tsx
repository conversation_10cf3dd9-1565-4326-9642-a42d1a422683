'use client';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { createContext, useState, useEffect } from 'react';
import { dbService } from '@/services/db.service';
import Sidebar from '@/components/Sidebar';
import { usePathname } from 'next/navigation';

export interface AuthContextType {
  isAuthenticated: boolean;
  user: {
    id: number;
    nome: string;
    email: string;
    role: string;
  } | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
}

export const AuthContext = createContext<AuthContextType | null>(null);

const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<AuthContextType['user']>(null);

  useEffect(() => {
    // Verifica se há um usuário na sessão
    const savedUser = sessionStorage.getItem('user');
    if (savedUser) {
      const userData = JSON.parse(savedUser);
      setUser(userData);
      setIsAuthenticated(true);
    }
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      // Busca o usuário pelo email
      const user = await dbService.getUserByEmail(email);

      if (user && user.senha === password) {
        const userData = {
          id: user.id,
          nome: user.nome,
          email: user.email,
          role: user.role
        };

        setUser(userData);
        setIsAuthenticated(true);
        sessionStorage.setItem('user', JSON.stringify(userData));

        // Registra o log de login
        await dbService.createLog({
          userId: user.id,
          action: 'LOGIN',
          details: 'Login realizado com sucesso',
          ip: window.location.hostname
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro ao fazer login:', error);
      return false;
    }
  };

  const logout = () => {
    if (user) {
      // Registra o log de logout
      dbService.createLog({
        userId: user.id,
        action: 'LOGOUT',
        details: 'Logout realizado com sucesso',
        ip: window.location.hostname
      });
    }

    setUser(null);
    setIsAuthenticated(false);
    sessionStorage.removeItem('user');
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}

'use client';
import { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../layout';
import type { AuthContextType } from '../layout';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import { dbService } from '@/services/db.service';
import { Button } from '@/app/components/Button';
import { PlusIcon, UserIcon, PencilIcon, Power } from 'lucide-react';

interface User {
  id: number;
  nome: string;
  email: string;
  role: string;
  status: string;
  lastLogin: string | null;
  createdAt: string;
}

const UserCard = ({ user }: { user: User }) => {
  const statusColors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    locked: 'bg-red-100 text-red-800',
    pending: 'bg-yellow-100 text-yellow-800'
  };

  const roleColors = {
    admin: 'bg-purple-100 text-purple-800',
    user: 'bg-blue-100 text-blue-800',
    viewer: 'bg-teal-100 text-teal-800'
  };

  const statusColor = statusColors[user.status as keyof typeof statusColors] || statusColors.inactive;
  const roleColor = roleColors[user.role as keyof typeof roleColors] || roleColors.user;
  
  const lastLogin = user.lastLogin ? new Date(user.lastLogin).toLocaleString('pt-BR') : 'Nunca';
  const createdAt = new Date(user.createdAt).toLocaleString('pt-BR');

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="p-5">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{user.nome}</h3>
            <p className="text-sm text-gray-500">{user.email}</p>
          </div>
          <div className="flex space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColor}`}>
              {user.status === 'active' ? 'Ativo' : 
               user.status === 'inactive' ? 'Inativo' : 
               user.status === 'locked' ? 'Bloqueado' : 'Pendente'}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${roleColor}`}>
              {user.role === 'admin' ? 'Administrador' : 
               user.role === 'user' ? 'Usuário' : 'Visualizador'}
            </span>
          </div>
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          <p><span className="font-medium">Último login:</span> {lastLogin}</p>
          <p><span className="font-medium">Criado em:</span> {createdAt}</p>
        </div>
      </div>
      
      <div className="px-5 py-3 bg-gray-50 flex justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          leftIcon={<PencilIcon className="h-4 w-4" />}
          className="border-gray-300 bg-white text-gray-700"
        >
          Editar
        </Button>
        <Button
          variant="danger"
          size="sm"
          leftIcon={<Power className="h-4 w-4" />}
          className="bg-red-600 text-white"
        >
          Desativar
        </Button>
      </div>
    </div>
  );
};

const UsersPage = () => {
  const auth = useContext(AuthContext) as AuthContextType;
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newUser, setNewUser] = useState({
    nome: '',
    email: '',
    senha: '',
    confirmSenha: '',
    role: 'user',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!auth.isAuthenticated) {
      router.push('/login');
      return;
    }

    // Verificar se o usuário é admin
    if (auth.user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    const loadUsers = async () => {
      try {
        setIsLoading(true);
        const usersData = await dbService.getUsers();
        setUsers(usersData);
      } catch (error) {
        console.error('Erro ao carregar usuários:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, [auth.isAuthenticated, auth.user?.role, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewUser(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError('');

    if (newUser.senha !== newUser.confirmSenha) {
      setError('As senhas não coincidem');
      setIsSaving(false);
      return;
    }

    try {
      // Verificar se o email já existe
      const existingUser = await dbService.getUserByEmail(newUser.email);
      if (existingUser) {
        setError('Este email já está em uso');
        setIsSaving(false);
        return;
      }

      const user = await dbService.createUser({
        nome: newUser.nome,
        email: newUser.email,
        senha: newUser.senha,
        role: newUser.role,
        status: 'active',
        lastLogin: null,
        updatedAt: new Date().toISOString(),
        preferences: {
          language: 'pt-BR',
          theme: 'light',
          notifications: true
        }
      });

      setUsers(prev => [...prev, user]);
      setShowAddModal(false);
      setNewUser({
        nome: '',
        email: '',
        senha: '',
        confirmSenha: '',
        role: 'user',
      });
    } catch (error) {
      console.error('Erro ao adicionar usuário:', error);
      setError('Erro ao adicionar usuário. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  if (!auth.isAuthenticated || auth.user?.role !== 'admin') return null;

  return (
    <AppLayout>
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Gerenciamento de Usuários</h1>
          <Button
            onClick={() => setShowAddModal(true)}
            leftIcon={<PlusIcon className="h-5 w-5" />}
            className="bg-blue-600 text-white"
          >
            Adicionar Usuário
          </Button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : users.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Nenhum usuário encontrado</h2>
            <p className="text-gray-600 mb-4">Adicione seu primeiro usuário para começar.</p>
            <Button
              onClick={() => setShowAddModal(true)}
              leftIcon={<PlusIcon className="h-5 w-5" />}
              fullWidth
              className="bg-blue-600 text-white"
            >
              Adicionar Usuário
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {users.map(user => (
              <UserCard key={user.id} user={user} />
            ))}
          </div>
        )}
        
        {/* Modal de Adicionar Usuário */}
        {showAddModal && (
          <div className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Adicionar Novo Usuário</h2>
                  <button 
                    onClick={() => setShowAddModal(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                {error && (
                  <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
                    {error}
                  </div>
                )}
                
                <form onSubmit={handleAddUser}>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="nome" className="block text-sm font-medium text-gray-700 mb-1">
                        Nome Completo *
                      </label>
                      <input
                        type="text"
                        id="nome"
                        name="nome"
                        value={newUser.nome}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={newUser.email}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Função *
                      </label>
                      <select
                        id="role"
                        name="role"
                        value={newUser.role}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      >
                        <option value="admin">Administrador</option>
                        <option value="user">Usuário</option>
                        <option value="viewer">Visualizador</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="senha" className="block text-sm font-medium text-gray-700 mb-1">
                        Senha *
                      </label>
                      <input
                        type="password"
                        id="senha"
                        name="senha"
                        value={newUser.senha}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                        minLength={6}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="confirmSenha" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirmar Senha *
                      </label>
                      <input
                        type="password"
                        id="confirmSenha"
                        name="confirmSenha"
                        value={newUser.confirmSenha}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                        minLength={6}
                      />
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-end space-x-3">
                    <Button
                      type="button"
                      onClick={() => setShowAddModal(false)}
                      variant="outline"
                      disabled={isSaving}
                      className="border-gray-300 bg-white text-gray-700"
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      isLoading={isSaving}
                      className="bg-blue-600 text-white"
                    >
                      {isSaving ? 'Salvando...' : 'Adicionar Usuário'}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default UsersPage;

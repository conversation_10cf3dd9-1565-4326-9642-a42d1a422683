{% extends 'base.html' %}

{% block title %}{{ directory.name }} - Navegação do Servidor{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{% url 'server_directory_list' %}">Diretórios</a></li>
<li class="breadcrumb-item"><a href="{% url 'server_directory_browse' directory.id %}">{{ directory.name }}</a></li>
{% for item in breadcrumb %}
<li class="breadcrumb-item">
    <a href="{% url 'server_directory_browse_path' directory.id item.path %}">{{ item.name }}</a>
</li>
{% endfor %}
<li class="breadcrumb-item active">{{ current_path|default:"Raiz" }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="mb-2 d-flex align-items-center">
                    <i class="fas fa-folder-open me-3 text-primary"></i>
                    {{ directory.name }}
                </h1>
                <p class="text-muted mb-0">{{ directory.path }}{% if current_path %}/{{ current_path }}{% endif %}</p>
            </div>
            <div class="d-flex gap-2">
                {% if parent_path is not None %}
                    {% if parent_path %}
                        <a href="{% url 'server_directory_browse_path' directory.id parent_path %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                    {% else %}
                        <a href="{% url 'server_directory_browse' directory.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                    {% endif %}
                {% endif %}
                <a href="{% url 'server_directory_list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>Todos os Diretórios
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Subdiretórios -->
{% if subdirectories %}
<div class="row mb-4">
    <div class="col-12">
        <h5 class="mb-3">
            <i class="fas fa-folder me-2 text-warning"></i>
            Subdiretórios ({{ subdirectories|length }})
        </h5>
        <div class="row">
            {% for subdir in subdirectories %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                <a href="{% url 'server_directory_browse_path' directory.id subdir.path %}" 
                   class="card subdirectory-card text-decoration-none">
                    <div class="card-body text-center">
                        <i class="fas fa-folder fa-2x text-warning mb-2"></i>
                        <h6 class="card-title mb-0">{{ subdir.name }}</h6>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Imagens -->
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-3">
            <h5 class="mb-0">
                <i class="fas fa-images me-2 text-info"></i>
                Imagens ({{ images|length }})
            </h5>
            {% if can_create_timelapse %}
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="selectAll()">
                    <i class="fas fa-check-square me-2"></i>Selecionar Todas
                </button>
                <button class="btn btn-outline-secondary" onclick="deselectAll()">
                    <i class="fas fa-square me-2"></i>Desmarcar Todas
                </button>
                <button class="btn btn-success" onclick="showCreateTimelapseModal()" id="create-btn" disabled>
                    <i class="fas fa-video me-2"></i>Criar Timelapse
                </button>
            </div>
            {% endif %}
        </div>
        
        {% if images %}
        <div class="row" id="images-grid">
            {% for image in images %}
            <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                <div class="card image-card" onclick="toggleImageSelection(this, '{{ image.name }}')">
                    <div class="image-container">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbTwvdGV4dD48L3N2Zz4=" 
                             alt="{{ image.name }}" class="card-img-top">
                        <div class="image-overlay">
                            <div class="selection-checkbox">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <h6 class="card-title small mb-1 text-truncate" title="{{ image.name }}">{{ image.name }}</h6>
                        <small class="text-muted">{{ image.size|filesizeformat }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-images fa-4x text-muted mb-3"></i>
            <h5 class="text-muted">Nenhuma imagem encontrada</h5>
            <p class="text-muted">Este diretório não contém imagens válidas</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal para Criar Timelapse -->
<div class="modal fade" id="createTimelapseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-video me-2"></i>
                    Criar Timelapse
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="timelapse-form">
                    <div class="mb-3">
                        <label for="timelapse-title" class="form-label">Título do Timelapse</label>
                        <input type="text" class="form-control" id="timelapse-title" required>
                    </div>
                    <div class="mb-3">
                        <label for="timelapse-fps" class="form-label">FPS (Frames por Segundo)</label>
                        <select class="form-select" id="timelapse-fps">
                            <option value="12">12 FPS</option>
                            <option value="24" selected>24 FPS</option>
                            <option value="30">30 FPS</option>
                            <option value="60">60 FPS</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Imagens Selecionadas</label>
                        <div id="selected-images-info" class="form-text">
                            <span id="selected-count">0</span> imagens selecionadas
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="createTimelapse()" id="modal-create-btn">
                    <i class="fas fa-video me-2"></i>
                    Criar Timelapse
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.subdirectory-card {
    transition: transform 0.2s ease;
}

.subdirectory-card:hover {
    transform: translateY(-2px);
    text-decoration: none !important;
}

.image-card {
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
}

.image-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.image-card.selected {
    border: 2px solid #0d6efd;
    transform: translateY(-2px);
}

.image-container {
    position: relative;
    overflow: hidden;
}

.image-container img {
    height: 120px;
    object-fit: cover;
    width: 100%;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(13, 110, 253, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.image-card.selected .image-overlay {
    opacity: 1;
}

.selection-checkbox {
    background: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0d6efd;
    font-size: 16px;
}
</style>

<script>
let selectedImages = new Set();

function toggleImageSelection(card, imageName) {
    if (selectedImages.has(imageName)) {
        selectedImages.delete(imageName);
        card.classList.remove('selected');
    } else {
        selectedImages.add(imageName);
        card.classList.add('selected');
    }
    updateCreateButton();
    updateSelectedCount();
}

function selectAll() {
    const imageCards = document.querySelectorAll('.image-card');
    selectedImages.clear();
    imageCards.forEach(card => {
        const imageName = card.getAttribute('onclick').match(/'([^']+)'/)[1];
        selectedImages.add(imageName);
        card.classList.add('selected');
    });
    updateCreateButton();
    updateSelectedCount();
}

function deselectAll() {
    selectedImages.clear();
    document.querySelectorAll('.image-card').forEach(card => {
        card.classList.remove('selected');
    });
    updateCreateButton();
    updateSelectedCount();
}

function updateCreateButton() {
    const createBtn = document.getElementById('create-btn');
    createBtn.disabled = selectedImages.size < 2;
}

function updateSelectedCount() {
    const countSpan = document.getElementById('selected-count');
    if (countSpan) {
        countSpan.textContent = selectedImages.size;
    }
}

function showCreateTimelapseModal() {
    if (selectedImages.size < 2) {
        alert('Selecione pelo menos 2 imagens para criar um timelapse.');
        return;
    }
    updateSelectedCount();
    new bootstrap.Modal(document.getElementById('createTimelapseModal')).show();
}

function createTimelapse() {
    const title = document.getElementById('timelapse-title').value.trim();
    const fps = parseInt(document.getElementById('timelapse-fps').value);
    
    if (!title) {
        alert('Digite um título para o timelapse.');
        return;
    }
    
    if (selectedImages.size < 2) {
        alert('Selecione pelo menos 2 imagens.');
        return;
    }
    
    const createBtn = document.getElementById('modal-create-btn');
    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Criando...';
    
    fetch('{% url "create_timelapse_from_directory" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            directory_id: {{ directory.id }},
            subdirectory: '{{ current_path|default:"" }}',
            title: title,
            selected_images: Array.from(selectedImages),
            fps: fps
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Timelapse criado com sucesso!');
            window.location.href = '{% url "timelapse_list" %}';
        } else {
            alert('Erro: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao criar timelapse.');
    })
    .finally(() => {
        createBtn.disabled = false;
        createBtn.innerHTML = '<i class="fas fa-video me-2"></i>Criar Timelapse';
    });
}
</script>
{% endblock %} 
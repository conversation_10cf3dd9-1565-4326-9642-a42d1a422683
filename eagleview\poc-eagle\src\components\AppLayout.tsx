'use client';
import { useState, useContext, useEffect } from 'react';
import { AuthContext } from '@/app/layout';
import type { AuthContextType } from '@/app/layout';
import { useRouter } from 'next/navigation';
import Sidebar from './Sidebar';

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout = ({ children }: AppLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const auth = useContext(AuthContext) as AuthContextType;
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Redirect to login if not authenticated
    if (!auth.isAuthenticated) {
      router.push('/login');
    }
  }, [auth.isAuthenticated, router]);

  if (!mounted || !auth.isAuthenticated) return null;

  return (
    <div className="flex h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        user={auth.user}
      />

      <div className="flex flex-col flex-1 w-full overflow-hidden">
        {/* Header melhorado */}
        <header className="w-full bg-white/80 backdrop-blur-sm shadow-sm sticky top-0 z-20 border-b border-gray-200/50">
          <div className="px-6 py-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="btn btn-ghost btn-sm lg:hidden focus-ring"
                aria-label="Abrir menu"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent hidden md:block">
                  Eagle View
                </h1>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Indicador de status */}
              <div className="hidden sm:flex items-center gap-2 px-3 py-2 bg-emerald-50 border border-emerald-200 rounded-full">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-emerald-700">Online</span>
              </div>

              {/* User menu */}
              <div className="flex items-center gap-3 px-3 py-2 bg-gray-50 border border-gray-200 rounded-full hover:bg-gray-100 transition-colors">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-white">
                    {auth.user?.nome?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-700 hidden sm:block">
                  {auth.user?.nome}
                </span>
              </div>

              <button
                onClick={auth.logout}
                className="btn btn-outline btn-sm focus-ring hover-lift"
                title="Sair do sistema"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span className="hidden sm:inline">Sair</span>
              </button>
            </div>
          </div>
        </header>

        {/* Main content melhorado */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="min-h-full">
            <div className="container mx-auto py-8 animate-fade-in">
              {children}
            </div>
          </div>
        </main>

        {/* Footer melhorado */}
        <footer className="w-full py-4 px-6 border-t border-gray-200/50 bg-white/50 backdrop-blur-sm">
          <div className="container mx-auto">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <p className="text-xs text-gray-500">
                © 2025 Eagle View Camera System. Todos os direitos reservados.
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-400">
                <span>v1.0.0</span>
                <span>•</span>
                <span>Status: Operacional</span>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default AppLayout;

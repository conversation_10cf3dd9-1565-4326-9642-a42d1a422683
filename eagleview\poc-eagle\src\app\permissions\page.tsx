'use client';
import { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../layout';
import type { AuthContextType } from '../layout';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import { dbService } from '@/services/db.service';

interface User {
  id: number;
  nome: string;
  email: string;
  role: string;
  status: string;
}

interface Camera {
  id: number;
  nome: string;
}

interface Permission {
  userId: number;
  cameraId: number;
  canView: boolean;
  canControl: boolean;
  canEdit: boolean;
}

const PermissionsPage = () => {
  const auth = useContext(AuthContext) as AuthContextType;
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [cameras, setCameras] = useState<Camera[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  useEffect(() => {
    if (!auth.isAuthenticated) {
      router.push('/login');
      return;
    }

    // Verificar se o usuário é admin
    if (auth.user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);
        const usersData = await dbService.getUsers();
        const camerasData = await dbService.getCameras();
        
        // Filtrar apenas usuários ativos que não são administradores
        const filteredUsers = usersData.filter(user => 
          user.status === 'active' && user.role !== 'admin'
        );
        
        setUsers(filteredUsers);
        setCameras(camerasData);
        
        // Criar permissões padrão (simuladas)
        // Em um sistema real, isso viria do banco de dados
        const defaultPermissions: Permission[] = [];
        
        filteredUsers.forEach(user => {
          camerasData.forEach(camera => {
            defaultPermissions.push({
              userId: user.id,
              cameraId: camera.id,
              canView: true,
              canControl: user.role === 'user',
              canEdit: false
            });
          });
        });
        
        setPermissions(defaultPermissions);
        
        if (filteredUsers.length > 0) {
          setSelectedUser(filteredUsers[0].id);
        }
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [auth.isAuthenticated, auth.user?.role, router]);

  const handleUserChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedUser(parseInt(e.target.value));
  };

  const handlePermissionChange = (cameraId: number, field: keyof Omit<Permission, 'userId' | 'cameraId'>, value: boolean) => {
    setPermissions(prev => 
      prev.map(perm => 
        perm.userId === selectedUser && perm.cameraId === cameraId
          ? { ...perm, [field]: value }
          : perm
      )
    );
  };

  const handleSavePermissions = async () => {
    if (!selectedUser) return;
    
    setIsSaving(true);
    setSaveMessage('');
    
    try {
      // Simular salvamento de permissões
      // Em um sistema real, isso seria enviado para o backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveMessage('Permissões salvas com sucesso!');
      
      // Limpar a mensagem após 3 segundos
      setTimeout(() => {
        setSaveMessage('');
      }, 3000);
    } catch (error) {
      console.error('Erro ao salvar permissões:', error);
      setSaveMessage('Erro ao salvar permissões. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  if (!auth.isAuthenticated || auth.user?.role !== 'admin') return null;

  return (
    <AppLayout>
      <div className="container mx-auto">
        <h1 className="text-2xl font-bold mb-6">Gerenciamento de Permissões</h1>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : users.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Nenhum usuário disponível</h2>
            <p className="text-gray-600 mb-4">Adicione usuários não-administradores para gerenciar permissões.</p>
            <button
              onClick={() => router.push('/users')}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Gerenciar Usuários
            </button>
          </div>
        ) : cameras.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Nenhuma câmera disponível</h2>
            <p className="text-gray-600 mb-4">Adicione câmeras para gerenciar permissões.</p>
            <button
              onClick={() => router.push('/cameras')}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Gerenciar Câmeras
            </button>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="w-full md:w-1/3">
                  <label htmlFor="user" className="block text-sm font-medium text-gray-700 mb-1">
                    Selecione um Usuário
                  </label>
                  <select
                    id="user"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={selectedUser || ''}
                    onChange={handleUserChange}
                  >
                    {users.map(user => (
                      <option key={user.id} value={user.id}>
                        {user.nome} ({user.email}) - {user.role === 'user' ? 'Usuário' : 'Visualizador'}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="flex items-center">
                  {saveMessage && (
                    <div className={`mr-4 text-sm ${saveMessage.includes('sucesso') ? 'text-green-600' : 'text-red-600'}`}>
                      {saveMessage}
                    </div>
                  )}
                  <button
                    onClick={handleSavePermissions}
                    disabled={isSaving || !selectedUser}
                    className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50"
                  >
                    {isSaving ? 'Salvando...' : 'Salvar Permissões'}
                  </button>
                </div>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Câmera
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Visualizar
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Controlar
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Editar
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {cameras.map(camera => {
                    const permission = permissions.find(
                      p => p.userId === selectedUser && p.cameraId === camera.id
                    );
                    
                    return (
                      <tr key={camera.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{camera.nome}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              checked={permission?.canView || false}
                              onChange={e => handlePermissionChange(camera.id, 'canView', e.target.checked)}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              checked={permission?.canControl || false}
                              onChange={e => handlePermissionChange(camera.id, 'canControl', e.target.checked)}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              checked={permission?.canEdit || false}
                              onChange={e => handlePermissionChange(camera.id, 'canEdit', e.target.checked)}
                            />
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
            
            <div className="p-4 bg-gray-50 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                <p><strong>Visualizar:</strong> Permite ao usuário ver o streaming da câmera</p>
                <p><strong>Controlar:</strong> Permite ao usuário controlar a câmera (PTZ, configurações)</p>
                <p><strong>Editar:</strong> Permite ao usuário editar as configurações da câmera</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default PermissionsPage;

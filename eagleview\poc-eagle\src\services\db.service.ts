import fs from 'fs/promises';
import path from 'path';

const DB_PATH = path.join(process.cwd(), 'src', 'db.json');

interface User {
  id: number;
  nome: string;
  email: string;
  senha: string;
  role: string;
  status: string;
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
  preferences: {
    language: string;
    theme: string;
    notifications: boolean;
  };
}

interface Camera {
  id: number;
  nome: string;
  descricao?: string;
  ip: string;
  username: string;
  password: string;
  port: string;
  protocol: string;
  status: string;
  location?: string;
  model?: string;
  firmware?: string;
  settings?: {
    resolution: string;
    fps: number;
    quality: string;
    audio: boolean;
    motion_detection: boolean;
  };
  alerts?: {
    motion: boolean;
    offline: boolean;
    error: boolean;
  };
  maintenance?: {
    lastCheck: string;
    nextCheck: string;
    status: string;
  };
  statistics?: {
    uptime: number;
    lastDowntime: string | null;
    totalConnections: number;
    avgBitrate: string;
  };
  createdBy?: number;
  createdAt: string;
  updatedAt?: string;
  lastConnection: string;
}

interface Log {
  id: number;
  userId: number;
  cameraId?: number;
  action: string;
  category?: string;
  severity?: string;
  details: string;
  metadata?: Record<string, any>;
  timestamp: string;
  ip: string;
}

interface Settings {
  system: {
    defaultProtocol: string;
    reconnectAttempts: number;
    reconnectInterval: number;
    streamQuality: string;
    maxCameras?: number;
    maintenance?: {
      backupEnabled: boolean;
      backupInterval: string;
      retentionDays: number;
    };
  };
  security?: {
    passwordPolicy?: {
      minLength: number;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      requireUppercase: boolean;
    };
    session?: {
      timeout: number;
      maxAttempts: number;
      lockDuration: number;
    };
  };
  notifications?: {
    email?: {
      enabled: boolean;
      server: string;
      port: number;
      username: string;
    };
    telegram?: {
      enabled: boolean;
      botToken: string;
      chatId: string;
    };
  };
  storage?: {
    type: string;
    path: string;
    maxSize: string;
    autoCleanup: boolean;
    retentionDays: number;
  };
}

interface Recording {
  id: number;
  cameraId: number;
  filename: string;
  startTime: string;
  endTime: string;
  duration: number;
  size: string;
  type: string;
  status: string;
  path: string;
  metadata?: {
    resolution: string;
    fps: number;
    codec: string;
  };
  createdAt: string;
}

interface Alert {
  id: number;
  cameraId: number;
  type: string;
  severity: string;
  status: string;
  message: string;
  metadata?: Record<string, any>;
  timestamp: string;
  acknowledgedBy: number | null;
  acknowledgedAt: string | null;
}

export interface DB {
  users: User[];
  cameras: Camera[];
  logs: Log[];
  settings: Settings;
  recordings?: Recording[];
  alerts?: Alert[];
}

class DatabaseService {
  private static instance: DatabaseService;
  private baseUrl: string;

  private constructor() {
    this.baseUrl = '/api/db';
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  async getUsers(): Promise<User[]> {
    const response = await fetch(`${this.baseUrl}?action=getUsers`);
    return response.json();
  }

  async getUserById(id: number): Promise<User | null> {
    const response = await fetch(`${this.baseUrl}?action=getUserById&id=${id}`);
    return response.json();
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const response = await fetch(`${this.baseUrl}?action=getUserByEmail&email=${email}`);
    return response.json();
  }

  async createUser(user: Omit<User, 'id' | 'createdAt'>): Promise<User> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'createUser', data: user })
    });
    return response.json();
  }

  async getCameras(): Promise<Camera[]> {
    const response = await fetch(`${this.baseUrl}?action=getCameras`);
    return response.json();
  }

  async getCameraById(id: number): Promise<Camera | null> {
    const response = await fetch(`${this.baseUrl}?action=getCameraById&id=${id}`);
    return response.json();
  }

  async createCamera(camera: Omit<Camera, 'id' | 'createdAt' | 'lastConnection'>): Promise<Camera> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'createCamera', data: camera })
    });
    return response.json();
  }

  async updateCameraStatus(id: number, status: string): Promise<Camera | null> {
    const response = await fetch(this.baseUrl, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'updateCameraStatus', id, data: { status } })
    });
    return response.json();
  }

  async updateCamera(id: number, cameraData: Partial<Camera>): Promise<Camera | null> {
    const response = await fetch(this.baseUrl, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'updateCamera',
        id,
        data: {
          ...cameraData,
          updatedAt: new Date().toISOString()
        }
      })
    });
    return response.json();
  }

  async deleteCamera(id: number): Promise<{ success: boolean }> {
    const response = await fetch(this.baseUrl, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'deleteCamera', id })
    });
    return response.json();
  }

  async createLog(log: Omit<Log, 'id' | 'timestamp'>): Promise<Log> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'createLog', data: log })
    });
    return response.json();
  }

  async getLogs(limit: number = 100): Promise<Log[]> {
    const response = await fetch(`${this.baseUrl}?action=getLogs&limit=${limit}`);
    return response.json();
  }

  async getSettings(): Promise<Settings> {
    const response = await fetch(`${this.baseUrl}?action=getSettings`);
    return response.json();
  }

  async updateSettings(settings: Partial<Settings>): Promise<Settings> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'updateSettings', data: settings })
    });
    return response.json();
  }

  async updateUserPassword(id: number, newPassword: string): Promise<User | null> {
    const response = await fetch(this.baseUrl, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'updateUserPassword', id, data: { senha: newPassword } })
    });
    return response.json();
  }
}

export const dbService = DatabaseService.getInstance();
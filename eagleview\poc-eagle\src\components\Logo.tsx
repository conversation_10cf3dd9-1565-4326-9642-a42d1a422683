import Image from 'next/image';
import { useState, useEffect } from 'react';

interface LogoProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}

const Logo = ({
  className = '',
  showText = true,
  size = 'md',
  animated = true
}: LogoProps) => {
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    setLoaded(true);
  }, []);

  // Define o tamanho do logo com base no prop size
  const sizeMap = {
    sm: { icon: 28, height: 28 },
    md: { icon: 36, height: 36 },
    lg: { icon: 48, height: 48 }
  };

  const { icon, height } = sizeMap[size];

  const animationClass = animated && loaded ? 'animate-fade-in' : '';

  return (
    <div className={`flex items-center ${className} ${animationClass}`}>
      <div className="relative">
        <Image
          src="/logo_eagle_view_simbolo_color_icone.png"
          alt="Eagle View Logo"
          width={icon}
          height={height}
          priority
          className={`${animated ? 'transition-transform duration-300 hover:scale-110' : ''} drop-shadow-md`}
          onLoad={() => setLoaded(true)}
        />
        {animated && (
          <div className="absolute -bottom-1 -right-1 h-2 w-2 rounded-full bg-green-500 animate-pulse shadow-sm" />
        )}
      </div>

      {showText && (
        <div className={`ml-3 font-bold ${size === 'lg' ? 'text-2xl' : size === 'md' ? 'text-xl' : 'text-base'} ${className.includes('text-white') ? 'text-white' : 'text-blue-600'}`}>
          <span>Eagle</span>
          <span className={className.includes('text-white') ? 'text-blue-200' : 'text-blue-800'}>View</span>
        </div>
      )}
    </div>
  );
};

export default Logo;
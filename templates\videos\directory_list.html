{% extends 'base.html' %}

{% block title %}Diretórios do Servidor - EagleView{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item active">Diretórios do Servidor</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between mb-5">
            <div>
                <h1 class="mb-2 d-flex align-items-center">
                    <i class="fas fa-folder-open me-3 text-primary"></i>
                    Diretórios do Servidor
                </h1>
                <p class="text-muted mb-0">Navegue pelos diretórios de imagens disponíveis no servidor</p>
            </div>
            {% if user.is_superuser %}
            <div>
                <a href="{% url 'admin:videos_serverdirectory_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Adicionar Di<PERSON>ó<PERSON>
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    {% if directories %}
        {% for directory in directories %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card directory-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-start mb-3">
                        <div class="directory-icon me-3">
                            <i class="fas fa-folder fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-1">{{ directory.name }}</h5>
                            <small class="text-muted">{{ directory.path }}</small>
                        </div>
                    </div>
                    
                    {% if directory.description %}
                    <p class="card-text text-muted small mb-3">{{ directory.description }}</p>
                    {% endif %}
                    
                    <div class="directory-info mb-3">
                        <div class="row g-2 text-center">
                            <div class="col-6">
                                <div class="info-item">
                                    <i class="fas fa-clock text-info"></i>
                                    <small class="d-block text-muted">Criado em</small>
                                    <small class="fw-bold">{{ directory.created_at|date:"d/m/Y" }}</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="info-item">
                                    <i class="fas fa-users text-success"></i>
                                    <small class="d-block text-muted">Grupos</small>
                                    <small class="fw-bold">
                                        {% if directory.allowed_groups.count > 0 %}
                                            {{ directory.allowed_groups.count }}
                                        {% else %}
                                            Todos
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <a href="{% url 'server_directory_browse' directory.id %}" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>
                            Navegar
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-4x text-muted mb-4"></i>
                <h4 class="text-muted mb-3">Nenhum diretório configurado</h4>
                <p class="text-muted mb-4">Configure diretórios do servidor para navegar pelas imagens</p>
                {% if user.is_superuser %}
                <a href="{% url 'admin:videos_serverdirectory_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Configurar Primeiro Diretório
                </a>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>

<style>
.directory-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.directory-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.directory-icon {
    min-width: 50px;
}

.info-item {
    padding: 8px;
    border-radius: 4px;
    background: #f8f9fa;
}

.info-item i {
    font-size: 18px;
    margin-bottom: 4px;
}
</style>
{% endblock %} 
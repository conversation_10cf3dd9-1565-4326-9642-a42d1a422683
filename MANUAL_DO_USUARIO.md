# Manual do Usuário - EagleView

## 📋 Índice
1. [<PERSON><PERSON><PERSON>](#visão-geral)
2. [Tipos de Usuários](#tipos-de-usuários)
3. [Acesso ao <PERSON>](#acesso-ao-sistema)
4. [Interface Principal](#interface-principal)
5. [Funcionalidades do Administrador](#funcionalidades-do-administrador)
6. [Funcionalidades do Usuário](#funcionalidades-do-usuário)
7. [Fluxos de Trabalho](#fluxos-de-trabalho)
8. [Solução de Problemas](#solução-de-problemas)

---

## 🎯 Visão Geral

O **EagleView** é uma plataforma profissional para criação e visualização de timelapses. O sistema permite que administradores criem timelapses a partir de imagens armazenadas em servidores e compartilhem esses vídeos com usuários específicos através de grupos.

### Principais Funcionalidades:
- ✅ Criação de timelapses a partir de imagens do servidor
- ✅ Sistema de grupos para controle de acesso
- ✅ Interface intuitiva e responsiva
- ✅ Processamento automático de vídeos
- ✅ Navegação em diretórios de servidor
- ✅ Controle granular de permissões

---

## 👥 Tipos de Usuários

### 🔧 Administrador
**Responsabilidades:**
- Criar e editar timelapses
- Configurar diretórios de servidor
- Gerenciar usuários e grupos
- Definir permissões de acesso
- Monitorar processamento de vídeos

**Identificação:** Usuários com status `is_staff` ou `is_superuser` ativado

### 👤 Usuário
**Responsabilidades:**
- Visualizar timelapses delegados
- Acessar vídeos do seu grupo
- Navegar pela interface de visualização

**Limitações:** Não pode criar, editar ou deletar timelapses

---

## 🚪 Acesso ao Sistema

### URL de Acesso
```
http://127.0.0.1:8000/
```

### Tela de Login
1. Acesse a URL do sistema
2. Insira suas credenciais:
   - **Usuário:** Seu nome de usuário
   - **Senha:** Sua senha
3. Clique em "Entrar"

### Credenciais Padrão do Administrador
- **Usuário:** douglas
- **Senha:** 18031999

---

## 🖥️ Interface Principal

### Barra Lateral (Sidebar)
A navegação principal fica na barra lateral esquerda:

#### Para Todos os Usuários:
- **🏠 Painel:** Dashboard principal
- **📁 Projetos:** Lista de timelapses disponíveis

#### Apenas para Administradores:
- **▶️ Gerar Timelapse:** Criar novos timelapses
- **🗂️ Diretórios:** Configurar diretórios do servidor
- **⚙️ Admin:** Painel administrativo Django

### Área Principal
- **Breadcrumb:** Navegação hierárquica
- **Conteúdo:** Área principal com funcionalidades
- **Mensagens:** Notificações do sistema

---

## 🔧 Funcionalidades do Administrador

### 1. Dashboard Administrativo
**Acesso:** Painel → Dashboard

**Funcionalidades:**
- Visão geral do sistema
- Estatísticas de timelapses
- Acesso rápido a funcionalidades principais
- Botão "Novo Projeto" para criar timelapses

### 2. Gerenciamento de Timelapses

#### 2.1 Criar Novo Timelapse
**Acesso:** Gerar Timelapse → Novo Projeto

**Passos:**
1. **Selecionar Diretório:**
   - Escolha o diretório configurado
   - Navegue pelos subdiretórios se necessário

2. **Configurar Parâmetros:**
   - **Título:** Nome do timelapse
   - **Empresa/Grupo:** Selecione o grupo proprietário
   - **FPS:** Frames por segundo (padrão: 24)
   - **Grupos com Acesso:** Defina quem pode visualizar

3. **Selecionar Imagens:**
   - Visualize miniaturas das imagens
   - Marque/desmarque imagens específicas
   - Use "Selecionar Todas" para facilitar

4. **Processar Vídeo:**
   - Clique em "Gerar Timelapse"
   - Aguarde o processamento
   - Receba notificação de conclusão

#### 2.2 Gerenciar Timelapses Existentes
**Acesso:** Projetos → Lista de Timelapses

**Ações Disponíveis:**
- **👁️ Visualizar:** Assistir ao timelapse
- **✏️ Editar:** Modificar configurações
- **🗑️ Deletar:** Remover timelapse
- **👥 Permissões:** Gerenciar acesso por grupos

### 3. Configuração de Diretórios

#### 3.1 Adicionar Novo Diretório
**Acesso:** Diretórios → Novo Diretório

**Campos Obrigatórios:**
- **Nome:** Identificação do diretório
- **Caminho:** Caminho absoluto no servidor
- **Descrição:** Informações adicionais
- **Grupos Permitidos:** Quais grupos podem acessar

#### 3.2 Gerenciar Diretórios Existentes
**Funcionalidades:**
- **Editar:** Modificar configurações
- **Ativar/Desativar:** Controlar disponibilidade
- **Testar Acesso:** Verificar conectividade
- **Visualizar Conteúdo:** Navegar pelos arquivos

### 4. Administração de Usuários
**Acesso:** Admin → Painel Administrativo

**Funcionalidades:**
- **Criar Usuários:** Adicionar novos usuários
- **Gerenciar Grupos:** Organizar usuários por empresa/projeto
- **Definir Permissões:** Controlar acesso a funcionalidades
- **Ativar/Desativar:** Gerenciar status dos usuários

---

## 👤 Funcionalidades do Usuário

### 1. Dashboard do Usuário
**Acesso:** Painel → Dashboard

**Visualização:**
- Timelapses disponíveis para seu grupo
- Últimos vídeos adicionados
- Estatísticas pessoais de visualização

### 2. Visualização de Timelapses

#### 2.1 Lista de Projetos
**Acesso:** Projetos → Lista

**Informações Exibidas:**
- **Título:** Nome do timelapse
- **Empresa:** Grupo proprietário
- **Duração:** Tempo do vídeo
- **Data de Criação:** Quando foi criado
- **Status:** Disponível/Processando

#### 2.2 Reprodução de Vídeos
**Funcionalidades:**
- **Player HTML5:** Controles padrão de vídeo
- **Tela Cheia:** Visualização expandida
- **Controle de Volume:** Ajuste de áudio
- **Velocidade:** Controle de reprodução
- **Download:** Baixar vídeo (se permitido)

### 3. Filtros e Busca
**Opções Disponíveis:**
- **Por Grupo:** Filtrar por empresa/projeto
- **Por Data:** Ordenar cronologicamente
- **Por Título:** Busca textual
- **Por Status:** Apenas disponíveis

---

## 🔄 Fluxos de Trabalho

### Fluxo Completo - Administrador

```mermaid
graph TD
    A[Login como Admin] --> B[Configurar Diretório]
    B --> C[Criar Grupos de Usuários]
    C --> D[Navegar Diretório]
    D --> E[Selecionar Imagens]
    E --> F[Configurar Timelapse]
    F --> G[Definir Permissões]
    G --> H[Processar Vídeo]
    H --> I[Notificar Usuários]
```

### Fluxo Simplificado - Usuário

```mermaid
graph TD
    A[Login como Usuário] --> B[Acessar Dashboard]
    B --> C[Ver Lista de Projetos]
    C --> D[Selecionar Timelapse]
    D --> E[Assistir Vídeo]
    E --> F[Compartilhar/Download]
```

---

## 🛠️ Solução de Problemas

### Problemas Comuns

#### 1. Não Consigo Ver Timelapses
**Possíveis Causas:**
- Usuário não está no grupo correto
- Timelapse ainda está processando
- Permissões não foram configuradas

**Soluções:**
- Contate o administrador para verificar grupos
- Aguarde conclusão do processamento
- Solicite acesso ao administrador

#### 2. Erro ao Processar Timelapse
**Possíveis Causas:**
- Diretório inacessível
- Imagens corrompidas
- Falta de espaço em disco

**Soluções:**
- Verificar conectividade com servidor
- Validar integridade das imagens
- Liberar espaço no servidor

#### 3. Login Não Funciona
**Possíveis Causas:**
- Credenciais incorretas
- Usuário desativado
- Problemas de sessão

**Soluções:**
- Verificar usuário e senha
- Contatar administrador
- Limpar cache do navegador

### Contato para Suporte
- **Administrador do Sistema:** douglas
- **Email:** [configurar email de suporte]
- **Documentação Técnica:** `/docs/`

---

## 📱 Compatibilidade

### Navegadores Suportados:
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### Dispositivos:
- ✅ Desktop/Laptop
- ✅ Tablet
- ✅ Smartphone (visualização)

---

## 🔄 Atualizações

**Versão Atual:** 1.0.0
**Última Atualização:** Julho 2025

Para sugestões e melhorias, entre em contato com a equipe de desenvolvimento.

---

## 📚 Exemplos Práticos

### Exemplo 1: Criando Primeiro Timelapse (Administrador)

**Cenário:** Criar timelapse de uma obra em construção

**Passo a Passo:**
1. **Preparação:**
   ```
   Login → Admin → Diretórios → Novo Diretório
   Nome: "Obra Shopping Center"
   Caminho: "/servidor/cameras/obra_shopping/"
   Grupos: "Construtora ABC"
   ```

2. **Criação do Grupo:**
   ```
   Admin → Grupos → Adicionar
   Nome: "Construtora ABC"
   Usuários: joão, maria, pedro
   ```

3. **Geração do Timelapse:**
   ```
   Gerar Timelapse → Selecionar Diretório: "Obra Shopping Center"
   Título: "Construção Shopping - Semana 1"
   FPS: 30
   Grupos com Acesso: "Construtora ABC"
   ```

### Exemplo 2: Visualizando Timelapses (Usuário)

**Cenário:** Usuário "joão" quer ver progresso da obra

**Passo a Passo:**
1. **Acesso:**
   ```
   Login com credenciais → Dashboard
   ```

2. **Navegação:**
   ```
   Projetos → Filtrar por "Construtora ABC"
   Selecionar: "Construção Shopping - Semana 1"
   ```

3. **Visualização:**
   ```
   Player de vídeo → Controles disponíveis
   Tela cheia → Compartilhar → Download (se permitido)
   ```

---

## ⚙️ Configurações Avançadas

### Configuração de Servidor de Imagens

#### Tipos de Conexão Suportados:
- **SSH:** Conexão segura com chave ou senha
- **FTP:** Protocolo básico de transferência
- **SFTP:** FTP seguro sobre SSH

#### Exemplo de Configuração SSH:
```
Nome: "Servidor Principal"
Tipo: SSH
Host: *************
Porta: 22
Usuário: eagleview
Senha: [deixar vazio se usar chave]
Chave SSH: /home/<USER>/.ssh/id_rsa
Pasta Remota: /var/cameras/timelapses/
```

### Otimização de Performance

#### Configurações Recomendadas:
- **FPS Padrão:** 24-30 para fluidez
- **Resolução:** Manter original das imagens
- **Formato:** MP4 para compatibilidade
- **Compressão:** H.264 para qualidade/tamanho

#### Limites do Sistema:
- **Máximo de Imagens:** 10.000 por timelapse
- **Tamanho Máximo:** 2GB por vídeo final
- **Formatos Suportados:** JPG, PNG, BMP, TIFF
- **Processamento Simultâneo:** 3 timelapses

---

## 🔐 Segurança e Permissões

### Níveis de Acesso

#### Super Administrador:
- Acesso total ao sistema
- Gerenciamento de usuários
- Configurações de servidor
- Logs e monitoramento

#### Administrador de Grupo:
- Gerenciar timelapses do grupo
- Adicionar usuários ao grupo
- Configurar permissões específicas

#### Usuário Final:
- Visualizar timelapses permitidos
- Download (se habilitado)
- Compartilhamento básico

### Boas Práticas de Segurança:
1. **Senhas Fortes:** Mínimo 8 caracteres
2. **Grupos Organizados:** Separar por projeto/empresa
3. **Revisão Periódica:** Verificar acessos mensalmente
4. **Backup Regular:** Manter cópias dos vídeos
5. **Logs de Auditoria:** Monitorar atividades

---

## 📊 Monitoramento e Relatórios

### Dashboard de Administração

#### Métricas Disponíveis:
- **Total de Timelapses:** Criados por período
- **Usuários Ativos:** Últimos 30 dias
- **Espaço Utilizado:** Armazenamento de vídeos
- **Processamentos:** Sucessos/falhas
- **Acessos:** Por usuário e grupo

#### Relatórios Exportáveis:
- **CSV:** Dados tabulares
- **PDF:** Relatórios formatados
- **JSON:** Integração com sistemas

### Logs do Sistema

#### Eventos Registrados:
- Login/logout de usuários
- Criação de timelapses
- Alterações de permissões
- Erros de processamento
- Acessos a vídeos

---

## 🚀 Dicas de Uso

### Para Administradores:
1. **Organize por Projetos:** Crie grupos específicos
2. **Teste Conexões:** Verifique servidores regularmente
3. **Monitore Espaço:** Acompanhe uso de armazenamento
4. **Documente Processos:** Mantenha procedimentos atualizados
5. **Treine Usuários:** Forneça orientações básicas

### Para Usuários:
1. **Favoritos:** Marque timelapses importantes
2. **Qualidade de Rede:** Use conexão estável para vídeos
3. **Navegador Atualizado:** Mantenha browser em dia
4. **Feedback:** Reporte problemas ao administrador
5. **Compartilhamento:** Use links diretos quando possível

---

## 🔧 Manutenção

### Tarefas Regulares:

#### Diárias:
- Verificar processamentos pendentes
- Monitorar logs de erro
- Validar backups automáticos

#### Semanais:
- Revisar uso de espaço
- Atualizar grupos de usuários
- Testar conexões de servidor

#### Mensais:
- Auditoria de permissões
- Limpeza de arquivos temporários
- Relatório de uso do sistema

### Comandos Úteis:
```bash
# Verificar status do servidor
python manage.py check

# Limpar arquivos temporários
python manage.py cleanup_temp_files

# Gerar relatório de uso
python manage.py usage_report

# Backup do banco de dados
python manage.py dumpdata > backup.json
```

---

## 📞 Suporte Técnico

### Canais de Atendimento:
- **Email:** <EMAIL>
- **Telefone:** (11) 9999-9999
- **Chat:** Disponível no sistema
- **Documentação:** `/docs/technical/`

### Horário de Atendimento:
- **Segunda a Sexta:** 8h às 18h
- **Sábado:** 8h às 12h
- **Emergências:** 24h (apenas críticas)

### Informações para Suporte:
Ao entrar em contato, tenha em mãos:
- Versão do sistema
- Navegador utilizado
- Descrição detalhada do problema
- Prints de tela (se aplicável)
- Logs de erro (se disponíveis)

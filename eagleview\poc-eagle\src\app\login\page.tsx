'use client';
import { useContext, useState, useRef } from 'react';
import { AuthContext } from '../layout';
import type { AuthContextType } from '../layout';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Logo from '@/components/Logo';
import { dbService } from '@/services/db.service';

const Login = () => {
  const auth = useContext(AuthContext);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetPassword, setResetPassword] = useState('');
  const [resetConfirmPassword, setResetConfirmPassword] = useState('');
  const [resetStep, setResetStep] = useState<'email' | 'password'>('email');
  const [resetSuccess, setResetSuccess] = useState(false);
  const [resetError, setResetError] = useState('');
  const [isResetting, setIsResetting] = useState(false);

  if (!auth) {
    throw new Error('AuthContext must be used within an AuthProvider');
  }

  const { login } = auth as AuthContextType;

  const fillDemoCredentials = () => {
    setEmail('<EMAIL>');
    setPassword('teste123');
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Simula um pequeno atraso para mostrar o estado de carregamento
      await new Promise(resolve => setTimeout(resolve, 800));

      const success = await login(email, password);

      if (success) {
        router.push('/streaming');
      } else {
        setError('Credenciais inválidas. Tente novamente.');
      }
    } catch (err) {
      setError('Ocorreu um erro durante o login. Tente novamente.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    setShowForgotPassword(true);
    setResetStep('email');
    setResetEmail('');
    setResetPassword('');
    setResetConfirmPassword('');
    setResetSuccess(false);
    setResetError('');
  };

  const validateEmail = (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const validatePassword = (password: string): boolean => {
    // Pelo menos 8 caracteres, uma letra maiúscula, um número e um caractere especial
    return password.length >= 8 &&
      /[A-Z]/.test(password) &&
      /[0-9]/.test(password) &&
      /[^A-Za-z0-9]/.test(password);
  };

  const handleVerifyEmail = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateEmail(resetEmail)) {
      setResetError('Por favor, insira um email válido.');
      return;
    }

    setIsResetting(true);
    setResetError('');

    try {
      // Simula um pequeno atraso para mostrar o estado de carregamento
      await new Promise(resolve => setTimeout(resolve, 800));

      // Verifica se o email existe no banco de dados
      const user = await dbService.getUserByEmail(resetEmail);

      if (user) {
        setResetStep('password');
      } else {
        setResetError('Email não encontrado. Verifique e tente novamente.');
      }
    } catch (err) {
      console.error('Erro ao verificar email:', err);
      setResetError('Ocorreu um erro ao verificar o email. Tente novamente.');
    } finally {
      setIsResetting(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validatePassword(resetPassword)) {
      setResetError('A senha deve ter pelo menos 8 caracteres, uma letra maiúscula, um número e um caractere especial.');
      return;
    }

    if (resetPassword !== resetConfirmPassword) {
      setResetError('As senhas não coincidem.');
      return;
    }

    setIsResetting(true);
    setResetError('');

    try {
      // Simula um pequeno atraso para mostrar o estado de carregamento
      await new Promise(resolve => setTimeout(resolve, 800));

      // Atualiza a senha no banco de dados
      const user = await dbService.getUserByEmail(resetEmail);

      if (user) {
        await dbService.updateUserPassword(user.id, resetPassword);
        setResetSuccess(true);

        // Registra a alteração de senha
        await dbService.createLog({
          userId: user.id,
          action: 'PASSWORD_RESET',
          category: 'security',
          severity: 'info',
          details: 'Senha alterada com sucesso através da funcionalidade "Esqueceu a senha"',
          ip: window.location.hostname
        });

        // Limpa os campos
        setResetPassword('');
        setResetConfirmPassword('');
      } else {
        setResetError('Usuário não encontrado. Tente novamente.');
      }
    } catch (err) {
      console.error('Erro ao redefinir senha:', err);
      setResetError('Ocorreu um erro ao redefinir a senha. Tente novamente.');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background decorativo */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-purple-400/10 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">

            {/* Lado Esquerdo - Form de Login */}
            <div className="order-2 lg:order-1">
              {!showForgotPassword ? (
                <div className="card card-elevated p-8 lg:p-10 animate-slide-in backdrop-blur-sm bg-white/95">
                  {/* Header */}
                  <div className="text-center mb-8">
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-2">
                      Bem-vindo de volta
                    </h1>
                    <p className="text-gray-600">
                      Faça login para acessar o Eagle View Camera System
                    </p>
                  </div>

                  {error && (
                    <div className="alert alert-error mb-6 animate-slide-in">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{error}</span>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Demo credentials */}
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-blue-900 mb-1">Credenciais de Demonstração</h4>
                          <p className="text-sm text-blue-700 mb-2">
                            <strong>Email:</strong> <EMAIL><br />
                            <strong>Senha:</strong> teste123
                          </p>
                          <button
                            type="button"
                            onClick={fillDemoCredentials}
                            className="btn btn-sm btn-outline text-blue-600 border-blue-300 hover:bg-blue-50"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Preencher automaticamente
                          </button>
                        </div>
                      </div>
                    </div>
                    {/* Email field */}
                    <div className="form-group">
                      <label htmlFor="email" className="form-label">
                        <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                        Endereço de Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        className="form-input focus-ring"
                        placeholder="<EMAIL>"
                        autoComplete="email"
                        autoFocus
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                      />
                    </div>

                    {/* Password field */}
                    <div className="form-group">
                      <div className="flex items-center justify-between mb-2">
                        <label htmlFor="password" className="form-label mb-0">
                          <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                          Senha
                        </label>
                        <button
                          type="button"
                          onClick={handleForgotPassword}
                          className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
                        >
                          Esqueceu a senha?
                        </button>
                      </div>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        className="form-input focus-ring"
                        placeholder="••••••••"
                        autoComplete="current-password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                      />
                    </div>

                    {/* Submit button */}
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="btn btn-primary btn-lg w-full hover-lift"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                          <span>Entrando...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                          </svg>
                          <span>Entrar no Sistema</span>
                        </div>
                      )}
                    </button>
                  </form>

                  {/* Footer */}
                  <div className="mt-8 pt-6 border-t border-gray-100 text-center">
                    <p className="text-gray-600 mb-4">
                      Não tem uma conta?
                    </p>
                    <Link
                      href="/cadastro"
                      className="btn btn-outline btn-md w-full group"
                    >
                      <svg className="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                      </svg>
                      <span>Criar Nova Conta</span>
                    </Link>
                  </div>
              </div>
            ) : (
              <div className="card p-8 shadow-lg rounded-xl flex flex-col w-full">
                <div className="mb-6 flex justify-center">
                  <Logo size="md" />
                </div>

                <h2 className="mb-6 text-center text-xl font-bold text-foreground">
                  {resetSuccess
                    ? "Senha Redefinida!"
                    : resetStep === 'email'
                      ? "Esqueceu sua senha?"
                      : "Criar Nova Senha"
                  }
                </h2>

                {resetError && (
                  <div className="mb-4 rounded bg-error/10 p-3 text-error">
                    <p className="text-sm">{resetError}</p>
                  </div>
                )}

                {resetSuccess ? (
                  <div className="text-center flex-grow flex flex-col justify-center">
                    <div className="mb-4 p-4 bg-success/10 rounded-lg text-success flex flex-col items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <p className="font-medium">Sua senha foi alterada com sucesso!</p>
                      <p className="text-sm mt-2">Você já pode fazer login com sua nova senha.</p>
                    </div>

                    <button
                      type="button"
                      onClick={() => setShowForgotPassword(false)}
                      className="btn btn-primary w-full text-white mt-4"
                    >
                      Voltar para o Login
                    </button>
                  </div>
                ) : resetStep === 'email' ? (
                  <form onSubmit={handleVerifyEmail} className="space-y-5 flex-grow flex flex-col">
                    <p className="text-sm text-gray-600 mb-4">
                      Digite seu endereço de email para redefinir sua senha.
                    </p>

                    <div>
                      <label htmlFor="reset-email" className="form-label">
                        Email
                      </label>
                      <input
                        type="email"
                        id="reset-email"
                        value={resetEmail}
                        onChange={(e) => setResetEmail(e.target.value)}
                        required
                        className="form-input"
                        placeholder="<EMAIL>"
                        autoFocus
                      />
                    </div>

                    <div className="flex space-x-3 mt-auto pt-4">
                      <button
                        type="button"
                        onClick={() => setShowForgotPassword(false)}
                        className="btn btn-secondary flex-1"
                        disabled={isResetting}
                      >
                        Cancelar
                      </button>

                      <button
                        type="submit"
                        className="btn btn-primary flex-1 text-white"
                        disabled={isResetting}
                      >
                        {isResetting ? (
                          <div className="flex items-center justify-center">
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                            <span>Verificando...</span>
                          </div>
                        ) : (
                          "Continuar"
                        )}
                      </button>
                    </div>
                  </form>
                ) : (
                  <form onSubmit={handleResetPassword} className="space-y-5 flex-grow flex flex-col">
                    <p className="text-sm text-gray-600 mb-4">
                      Crie uma nova senha para sua conta ({resetEmail}).
                    </p>

                    <div>
                      <label htmlFor="reset-password" className="form-label">
                        Nova Senha
                      </label>
                      <input
                        type="password"
                        id="reset-password"
                        value={resetPassword}
                        onChange={(e) => setResetPassword(e.target.value)}
                        required
                        className="form-input"
                        placeholder="••••••••"
                        autoFocus
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        A senha deve ter pelo menos 8 caracteres, uma letra maiúscula, um número e um caractere especial.
                      </p>
                    </div>

                    <div>
                      <label htmlFor="reset-confirm-password" className="form-label">
                        Confirmar Nova Senha
                      </label>
                      <input
                        type="password"
                        id="reset-confirm-password"
                        value={resetConfirmPassword}
                        onChange={(e) => setResetConfirmPassword(e.target.value)}
                        required
                        className="form-input"
                        placeholder="••••••••"
                      />
                    </div>

                    <div className="flex space-x-3 mt-auto pt-4">
                      <button
                        type="button"
                        onClick={() => setResetStep('email')}
                        className="btn btn-secondary flex-1"
                        disabled={isResetting}
                      >
                        Voltar
                      </button>

                      <button
                        type="submit"
                        className="btn btn-primary flex-1 text-white"
                        disabled={isResetting}
                      >
                        {isResetting ? (
                          <div className="flex items-center justify-center">
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                            <span>Alterando...</span>
                          </div>
                        ) : (
                          "Redefinir Senha"
                        )}
                      </button>
                    </div>
                  </form>
                )}

              </div>
            )}
          </div>

            {/* Lado Direito - Apresentação */}
            <div className="order-1 lg:order-2">
              <div className="relative">
                {/* Background decorativo */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-3xl"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-3xl"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-2xl -translate-y-8 translate-x-8"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full blur-xl translate-y-4 -translate-x-4"></div>

                <div className="relative p-8 lg:p-12 text-white animate-slide-in-right">
                  {/* Header */}
                  <div className="mb-12">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30">
                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h2 className="text-2xl lg:text-3xl font-bold">Eagle View</h2>
                        <p className="text-blue-100 text-sm">Camera System</p>
                      </div>
                    </div>
                    <p className="text-blue-100 text-lg leading-relaxed">
                      Sistema profissional de monitoramento e vigilância com tecnologia avançada
                    </p>
                  </div>

                  {/* Features */}
                  <div className="space-y-8">
                    <div className="flex items-start gap-4 group">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold mb-2">Gestão de Usuários</h3>
                        <p className="text-blue-100 leading-relaxed">
                          Controle completo de acesso com diferentes níveis de permissão
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4 group">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold mb-2">Múltiplos Protocolos</h3>
                        <p className="text-blue-100 leading-relaxed">
                          Suporte a RTSP, HTTP, WebRTC, ONVIF e muito mais
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4 group">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30 group-hover:scale-110 transition-transform">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold mb-2">Monitoramento 24/7</h3>
                        <p className="text-blue-100 leading-relaxed">
                          Vigilância contínua com alertas em tempo real
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="mt-12 grid grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white mb-1">99.9%</div>
                      <div className="text-xs text-blue-200 uppercase tracking-wide">Uptime</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white mb-1">24/7</div>
                      <div className="text-xs text-blue-200 uppercase tracking-wide">Suporte</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white mb-1">10+</div>
                      <div className="text-xs text-blue-200 uppercase tracking-wide">Protocolos</div>
                    </div>
                  </div>

                  {/* Bottom badge */}
                  <div className="mt-12 p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div>
                        <p className="font-semibold text-white">Sistema Seguro</p>
                        <p className="text-xs text-blue-200">Criptografia de ponta a ponta</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-6 text-center">
        <p className="text-sm text-gray-500">
          © 2025 Eagle View Camera System. Todos os direitos reservados.
        </p>
      </div>
    </div>
  );
};

export default Login;
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - EagleView</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% load static %}
    <style>
        :root {
            --bg-primary: #0A0B0D;
            --bg-secondary: #11141A;
            --surface-secondary: #1C2128;
            --surface-tertiary: #262C36;
            --highlight-orange: #FF6A00;
            --highlight-blue: #0066CC;
            --text-primary: #F8FAFC;
            --text-secondary: #94A3B8;
            --border-primary: #334155;
            --border-secondary: #1E293B;
            --success: #10B981;
            --danger: #EF4444;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --radius-lg: 16px;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(255, 106, 0, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(0, 102, 204, 0.03) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .login-container {
            background: rgba(28, 33, 40, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            padding: 48px;
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--highlight-orange), var(--highlight-blue));
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .logo {
            height: 60px;
            width: auto;
            margin-bottom: 16px;
        }
        
        .app-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-white);
            margin: 0;
        }
        
        .login-title {
            text-align: center;
            margin-bottom: 32px;
            color: var(--text-secondary);
            font-size: 1rem;
        }
        
        .form-control {
            background: rgba(38, 44, 54, 0.6);
            border: 2px solid var(--border-primary);
            color: var(--text-primary);
            border-radius: 12px;
            padding: 14px 18px;
            font-size: 1rem;
            transition: var(--transition-smooth);
        }
        
        .form-control:focus {
            background: rgba(38, 44, 54, 0.8);
            border-color: var(--highlight-orange);
            color: var(--text-primary);
            box-shadow: 0 0 0 0.25rem rgba(255, 106, 0, 0.15);
            transform: translateY(-1px);
        }
        
        .form-control::placeholder {
            color: var(--text-secondary);
        }
        
        .form-label {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--highlight-orange), #FF7F1A);
            border: none;
            color: var(--text-primary);
            font-weight: 600;
            border-radius: 12px;
            padding: 14px 24px;
            width: 100%;
            font-size: 1rem;
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-smooth);
        }
        
        .btn-primary:hover::before {
            left: 100%;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #FF7F1A, #d14900);
            color: var(--text-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .alert {
            border-radius: 6px;
            border: none;
            margin-bottom: 24px;
        }
        
        .alert-danger {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border-left: 4px solid #dc3545;
        }
        
        .form-check-input {
            background-color: var(--bg-dark);
            border-color: var(--border-color);
        }
        
        .form-check-input:checked {
            background-color: var(--highlight-orange);
            border-color: var(--highlight-orange);
        }
        
        .form-check-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 32px;
            color: var(--text-secondary);
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-container">
            <img src="{% static 'images/logo.png' %}" alt="EagleView" class="logo">
            <h1 class="app-title">EagleView</h1>
        </div>
        
        <h2 class="login-title">Faça login em sua conta</h2>
        
        {% if form.errors %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Usuário ou senha incorretos.
            </div>
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            <div class="mb-3">
                <label for="{{ form.username.id_for_label }}" class="form-label">
                    <i class="fas fa-user me-2"></i>Usuário
                </label>
                <input type="text" 
                       class="form-control" 
                       id="{{ form.username.id_for_label }}"
                       name="{{ form.username.html_name }}"
                       placeholder="Digite seu usuário"
                       required>
            </div>
            
            <div class="mb-3">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock me-2"></i>Senha
                </label>
                <input type="password" 
                       class="form-control" 
                       id="{{ form.password.id_for_label }}"
                       name="{{ form.password.html_name }}"
                       placeholder="Digite sua senha"
                       required>
            </div>
            
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember">
                <label class="form-check-label" for="remember">
                    Lembrar de mim
                </label>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt me-2"></i>Entrar
            </button>
        </form>
        
        <div class="footer-text">
            <i class="fas fa-video me-1"></i>
            Plataforma de Geração de Timelapses
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
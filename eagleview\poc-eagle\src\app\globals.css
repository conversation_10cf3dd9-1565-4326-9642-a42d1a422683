@import "tailwindcss";

:root {
  /* Design System - Eagle View */

  /* <PERSON><PERSON> principais - Paleta moderna e profissional */
  --primary: #2563eb; /* Azul principal mais sólido */
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-900: #1e3a8a;

  /* Cores secundárias */
  --secondary: #64748b;
  --secondary-light: #94a3b8;
  --secondary-dark: #475569;

  /* Cores de gradiente para fundos modernos */
  --gradient-from: #f8fafc; /* Mais sutil */
  --gradient-to: #f1f5f9;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  /* Cores de fundo - Sistema hierárquico */
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --surface: #ffffff;
  --surface-elevated: #ffffff;
  --foreground: #0f172a;
  --foreground-secondary: #334155;
  --foreground-tertiary: #64748b;

  /* Cores de destaque - Mais vibrantes e modernas */
  --accent: #f59e0b;
  --accent-hover: #d97706;
  --accent-light: #fbbf24;

  /* Cores de estado - Sistema consistente */
  --success: #059669;
  --success-light: #10b981;
  --success-bg: #ecfdf5;
  --warning: #d97706;
  --warning-light: #f59e0b;
  --warning-bg: #fffbeb;
  --error: #dc2626;
  --error-light: #ef4444;
  --error-bg: #fef2f2;
  --info: #0284c7;
  --info-light: #0ea5e9;
  --info-bg: #f0f9ff;

  /* Cores neutras - Escala refinada */
  --gray-25: #fcfcfd;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --gray-950: #020617;

  /* Bordas e sombras - Sistema moderno */
  --border-color: var(--gray-200);
  --border-color-light: var(--gray-100);
  --border-color-strong: var(--gray-300);

  /* Raios de borda - Sistema consistente */
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;
  --radius-full: 9999px;

  /* Sombras - Sistema em camadas */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  /* Sombras coloridas */
  --shadow-primary: 0 10px 15px -3px rgba(37, 99, 235, 0.1), 0 4px 6px -4px rgba(37, 99, 235, 0.1);
  --shadow-success: 0 10px 15px -3px rgba(5, 150, 105, 0.1), 0 4px 6px -4px rgba(5, 150, 105, 0.1);
  --shadow-warning: 0 10px 15px -3px rgba(217, 119, 6, 0.1), 0 4px 6px -4px rgba(217, 119, 6, 0.1);
  --shadow-error: 0 10px 15px -3px rgba(220, 38, 38, 0.1), 0 4px 6px -4px rgba(220, 38, 38, 0.1);

  /* Transições - Sistema fluido */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* Espaçamento - Sistema 8pt grid */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem; /* 2px */
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-1_5: 0.375rem; /* 6px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-2_5: 0.625rem; /* 10px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-3_5: 0.875rem; /* 14px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-7: 1.75rem;    /* 28px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-9: 2.25rem;    /* 36px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */

  /* Tipografia - Sistema hierárquico */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */

  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Reset e configurações globais */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* Layout principal */
#__next {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ocultar indicadores de desenvolvimento */
#__next-build-watcher,
button[data-next-build-indicator] {
  display: none !important;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-full);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* ===== COMPONENTES BASE ===== */

/* Cards - Sistema modular */
.card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color-light);
  transition: all var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color);
}

.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-interactive {
  cursor: pointer;
  transition: all var(--transition);
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Container - Sistema responsivo */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }

/* Botões - Sistema completo */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  border-radius: var(--radius);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Tamanhos de botão */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
}

.btn-sm {
  padding: var(--spacing-1_5) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.btn-md {
  padding: var(--spacing-2_5) var(--spacing-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

/* Variantes de botão */
.btn-primary {
  background: var(--primary);
  color: white;
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  background: var(--primary-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--gray-200);
  border-color: var(--border-color-strong);
}

.btn-success {
  background: var(--success);
  color: white;
  box-shadow: var(--shadow-success);
}

.btn-success:hover {
  background: var(--success-light);
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning);
  color: white;
  box-shadow: var(--shadow-warning);
}

.btn-warning:hover {
  background: var(--warning-light);
  transform: translateY(-1px);
}

.btn-error {
  background: var(--error);
  color: white;
  box-shadow: var(--shadow-error);
}

.btn-error:hover {
  background: var(--error-light);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border-color-strong);
  color: var(--foreground);
}

.btn-outline:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-ghost {
  background: transparent;
  color: var(--foreground-secondary);
}

.btn-ghost:hover {
  background: var(--gray-100);
  color: var(--foreground);
}

/* ===== FORMULÁRIOS MELHORADOS ===== */

.form-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  background: var(--surface);
  color: var(--foreground);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: all var(--transition-fast);
  appearance: none;
  outline: none;
}

.form-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  background: var(--surface-elevated);
}

.form-input:disabled {
  background: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-input.error {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-input.success {
  border-color: var(--success);
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: var(--spacing-10);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--foreground);
  line-height: var(--line-height-tight);
}

.form-label.required::after {
  content: " *";
  color: var(--error);
}

.form-group {
  margin-bottom: var(--spacing-6);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-message {
  margin-top: var(--spacing-2);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-tight);
}

.form-message.error {
  color: var(--error);
}

.form-message.success {
  color: var(--success);
}

.form-message.warning {
  color: var(--warning);
}

/* ===== BADGES MELHORADOS ===== */

.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  white-space: nowrap;
  border: 1px solid transparent;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-700);
  border-color: var(--primary-200);
}

.badge-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-200);
}

.badge-success {
  background: var(--success-bg);
  color: var(--success);
  border-color: rgba(5, 150, 105, 0.2);
}

.badge-warning {
  background: var(--warning-bg);
  color: var(--warning);
  border-color: rgba(217, 119, 6, 0.2);
}

.badge-error {
  background: var(--error-bg);
  color: var(--error);
  border-color: rgba(220, 38, 38, 0.2);
}

.badge-info {
  background: var(--info-bg);
  color: var(--info);
  border-color: rgba(2, 132, 199, 0.2);
}

/* Badge com ponto de status */
.badge-dot::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* ===== ALERTAS ===== */

.alert {
  padding: var(--spacing-4);
  border-radius: var(--radius);
  border: 1px solid;
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.alert-success {
  background: var(--success-bg);
  border-color: rgba(5, 150, 105, 0.3);
  color: var(--success);
}

.alert-warning {
  background: var(--warning-bg);
  border-color: rgba(217, 119, 6, 0.3);
  color: var(--warning);
}

.alert-error {
  background: var(--error-bg);
  border-color: rgba(220, 38, 38, 0.3);
  color: var(--error);
}

.alert-info {
  background: var(--info-bg);
  border-color: rgba(2, 132, 199, 0.3);
  color: var(--info);
}

/* Gradientes - seguindo o estilo da página de login */
.bg-gradient-brand {
  background-image: linear-gradient(to bottom right, var(--gradient-from), var(--gradient-to));
}

/* Layout específico */
.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 18rem;
  z-index: 40;
}

.content-with-sidebar {
  margin-left: 18rem;
  width: calc(100% - 18rem);
}

/* ===== ANIMAÇÕES MELHORADAS ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Classes de animação */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-in-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-in-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-in-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* ===== UTILITÁRIOS ===== */

/* Estados de loading */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--gray-300);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Estados visuais */
.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Truncate text */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Hover effects */
.hover-lift {
  transition: transform var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Focus styles */
.focus-ring {
  transition: box-shadow var(--transition-fast);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .btn-lg {
    padding: var(--spacing-2_5) var(--spacing-4);
    font-size: var(--font-size-sm);
  }

  .card {
    border-radius: var(--radius);
  }

  .sidebar-fixed {
    width: 16rem;
  }

  .content-with-sidebar {
    margin-left: 16rem;
    width: calc(100% - 16rem);
  }
}

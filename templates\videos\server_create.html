{% extends 'base.html' %}
{% load static %}

{% block title %}Nova Configuração de Servidor - EagleView{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: var(--surface-secondary);
        border: 1px solid var(--border-primary);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-section h5 {
        color: var(--highlight-orange);
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--border-primary);
    }
    
    .form-control, .form-select {
        background: var(--surface-tertiary);
        border: 1px solid var(--border-primary);
        color: var(--text-primary);
    }
    
    .form-control:focus, .form-select:focus {
        background: var(--surface-tertiary);
        border-color: var(--highlight-orange);
        box-shadow: 0 0 0 0.2rem rgba(255, 106, 0, 0.25);
        color: var(--text-primary);
    }
    
    .form-label {
        color: var(--text-primary);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .form-text {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }
    
    .connection-preview {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-primary);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .test-connection-area {
        background: var(--surface-tertiary);
        border: 2px dashed var(--border-primary);
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        margin-top: 1rem;
    }
    
    .auth-method-toggle {
        display: flex;
        background: var(--surface-tertiary);
        border-radius: 8px;
        padding: 0.25rem;
        margin-bottom: 1rem;
    }
    
    .auth-method-toggle button {
        flex: 1;
        background: transparent;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        color: var(--text-secondary);
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .auth-method-toggle button.active {
        background: var(--highlight-orange);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <!-- Cabeçalho Moderno -->
            <div class="d-flex align-items-center mb-5">
                <a href="{% url 'server_settings' %}" class="btn btn-outline-secondary rounded-circle me-4" style="width: 48px; height: 48px;">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="flex-grow-1">
                    <h1 class="mb-2 d-flex align-items-center">
                        <i class="fas fa-server me-3 text-primary float"></i>
                        Nova Configuração de Servidor
                    </h1>
                    <p class="text-muted mb-0">
                        Configure uma conexão SSH/FTP para importar imagens automaticamente dos seus servidores
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="fas fa-question-circle me-2"></i>
                        Ajuda
                    </button>
                </div>
            </div>

            <form method="post" id="serverForm">
                {% csrf_token %}
                
                <!-- Informações Básicas -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>Informações Básicas</h5>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nome da Configuração</label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="Ex: Servidor de Produção">
                                <div class="form-text">Nome descritivo para identificar esta configuração</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="connection_type" class="form-label">Tipo de Conexão</label>
                                <select class="form-select" id="connection_type" name="connection_type" required>
                                    <option value="ssh">SSH</option>
                                    <option value="sftp">SFTP</option>
                                    <option value="ftp">FTP</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configurações de Conexão -->
                <div class="form-section">
                    <h5><i class="fas fa-network-wired me-2"></i>Configurações de Conexão</h5>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="host" class="form-label">Servidor</label>
                                <input type="text" class="form-control" id="host" name="host" required
                                       placeholder="Ex: ************* ou servidor.exemplo.com">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="port" class="form-label">Porta</label>
                                <input type="number" class="form-control" id="port" name="port" value="22" min="1" max="65535">
                                <div class="form-text">SSH: 22, FTP: 21</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Usuário</label>
                        <input type="text" class="form-control" id="username" name="username" required
                               placeholder="Nome de usuário para conexão">
                    </div>
                </div>

                <!-- Autenticação -->
                <div class="form-section">
                    <h5><i class="fas fa-key me-2"></i>Autenticação</h5>
                    
                    <div class="auth-method-toggle" id="authToggle">
                        <button type="button" class="active" data-method="password">
                            <i class="fas fa-lock me-2"></i>Senha
                        </button>
                        <button type="button" data-method="key">
                            <i class="fas fa-key me-2"></i>Chave SSH
                        </button>
                    </div>

                    <div id="passwordAuth">
                        <div class="mb-3">
                            <label for="password" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Senha do usuário">
                            <div class="form-text">Deixe em branco se usar chave SSH</div>
                        </div>
                    </div>

                    <div id="keyAuth" style="display: none;">
                        <div class="mb-3">
                            <label for="private_key_path" class="form-label">Caminho da Chave SSH</label>
                            <input type="text" class="form-control" id="private_key_path" name="private_key_path"
                                   placeholder="Ex: /home/<USER>/.ssh/id_rsa">
                            <div class="form-text">Caminho completo para o arquivo de chave privada SSH</div>
                        </div>
                    </div>
                </div>

                <!-- Configurações Remotas -->
                <div class="form-section">
                    <h5><i class="fas fa-folder me-2"></i>Configurações Remotas</h5>
                    
                    <div class="mb-3">
                        <label for="remote_path" class="form-label">Pasta das Imagens</label>
                        <input type="text" class="form-control" id="remote_path" name="remote_path" required
                               placeholder="Ex: /home/<USER>/images ou /var/www/uploads">
                        <div class="form-text">Caminho completo da pasta que contém as imagens no servidor</div>
                    </div>

                    <!-- Preview da Conexão -->
                    <div class="connection-preview">
                        <h6 class="mb-2">Preview da Conexão:</h6>
                        <code id="connectionString">ssh usuario@servidor:porta</code>
                    </div>
                </div>

                <!-- Teste de Conexão -->
                <div class="form-section">
                    <h5><i class="fas fa-plug me-2"></i>Teste de Conexão</h5>
                    
                    <div class="test-connection-area">
                        <p class="text-muted mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Recomendamos testar a conexão antes de salvar
                        </p>
                        <button type="button" class="btn btn-outline-success" id="testBtn">
                            <i class="fas fa-plug me-2"></i>
                            Testar Conexão
                        </button>
                        <div id="testResult" class="mt-3" style="display: none;"></div>
                    </div>
                </div>

                <!-- Botões de Ação -->
                <div class="d-flex gap-3 justify-content-end">
                    <a href="{% url 'server_settings' %}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Salvar Configuração
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('serverForm');
    const authToggle = document.getElementById('authToggle');
    const passwordAuth = document.getElementById('passwordAuth');
    const keyAuth = document.getElementById('keyAuth');
    const testBtn = document.getElementById('testBtn');
    const testResult = document.getElementById('testResult');
    
    // Toggle de autenticação
    authToggle.addEventListener('click', function(e) {
        if (e.target.closest('button')) {
            const method = e.target.closest('button').dataset.method;
            
            // Atualizar botões ativos
            authToggle.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
            e.target.closest('button').classList.add('active');
            
            // Mostrar/ocultar campos
            if (method === 'password') {
                passwordAuth.style.display = 'block';
                keyAuth.style.display = 'none';
                document.getElementById('password').required = true;
                document.getElementById('private_key_path').required = false;
            } else {
                passwordAuth.style.display = 'none';
                keyAuth.style.display = 'block';
                document.getElementById('password').required = false;
                document.getElementById('private_key_path').required = true;
            }
        }
    });
    
    // Atualizar preview da conexão
    function updateConnectionPreview() {
        const type = document.getElementById('connection_type').value;
        const username = document.getElementById('username').value || 'usuario';
        const host = document.getElementById('host').value || 'servidor';
        const port = document.getElementById('port').value || '22';
        
        let connectionString = '';
        if (type === 'ssh') {
            connectionString = `ssh ${username}@${host}:${port}`;
        } else if (type === 'sftp') {
            connectionString = `sftp ${username}@${host}:${port}`;
        } else {
            connectionString = `ftp ${username}@${host}:${port}`;
        }
        
        document.getElementById('connectionString').textContent = connectionString;
    }
    
    // Atualizar porta padrão baseada no tipo
    document.getElementById('connection_type').addEventListener('change', function() {
        const type = this.value;
        const portField = document.getElementById('port');
        
        if (type === 'ftp') {
            portField.value = '21';
        } else {
            portField.value = '22';
        }
        
        updateConnectionPreview();
    });
    
    // Atualizar preview quando campos mudam
    ['username', 'host', 'port'].forEach(field => {
        document.getElementById(field).addEventListener('input', updateConnectionPreview);
    });
    
    // Inicializar preview
    updateConnectionPreview();
    
    // Teste de conexão
    testBtn.addEventListener('click', function() {
        const formData = new FormData(form);
        
        // Mostrar loading
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando...';
        testBtn.disabled = true;
        testResult.style.display = 'none';
        
        // Simular teste (implementar depois)
        setTimeout(() => {
            testResult.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Funcionalidade de teste será implementada após salvar a configuração.
                </div>
            `;
            testResult.style.display = 'block';
            
            testBtn.innerHTML = '<i class="fas fa-plug me-2"></i>Testar Conexão';
            testBtn.disabled = false;
        }, 2000);
    });
});
</script>
{% endblock %} 
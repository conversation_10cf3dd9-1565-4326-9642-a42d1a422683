{"name": "poc-eagle", "version": "0.1.0", "private": true, "scripts": {"dev": "set NEXT_DISABLE_INDICATOR=true && next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/hls.js": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "hls.js": "^1.6.2", "lucide-react": "^0.488.0", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4", "typescript": "^5"}}
# Generated by Django 5.2.3 on 2025-06-24 23:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Timelapse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=120, verbose_name="<PERSON><PERSON><PERSON><PERSON>")),
                (
                    "video",
                    models.FileField(upload_to="timelapses/", verbose_name="Vídeo"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Criado em"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Criado por",
                    ),
                ),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="auth.group",
                        verbose_name="Empresa",
                    ),
                ),
            ],
            options={
                "verbose_name": "Timelapse",
                "verbose_name_plural": "Timelapses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProcessingJob",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pendente"),
                            ("processing", "Processando"),
                            ("completed", "Concluído"),
                            ("failed", "Falhou"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True)),
                (
                    "timelapse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="jobs",
                        to="videos.timelapse",
                    ),
                ),
            ],
            options={
                "verbose_name": "Job de Processamento",
                "verbose_name_plural": "Jobs de Processamento",
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="FrameImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image", models.ImageField(upload_to="frames/")),
                ("filename", models.CharField(max_length=255)),
                ("order", models.PositiveIntegerField(default=0)),
                ("selected", models.BooleanField(default=False)),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                (
                    "timelapse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="frames",
                        to="videos.timelapse",
                    ),
                ),
            ],
            options={
                "verbose_name": "Frame",
                "verbose_name_plural": "Frames",
                "ordering": ["order", "filename"],
            },
        ),
    ]

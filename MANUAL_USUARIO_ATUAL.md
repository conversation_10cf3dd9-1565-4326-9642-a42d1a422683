# Manual do Usuário - EagleView (Versão Atual)

## 📋 Índice
1. [<PERSON><PERSON><PERSON>](#visão-geral)
2. [Acesso ao Si<PERSON>](#acesso-ao-sistema)
3. [Tipos de Usuários](#tipos-de-usuários)
4. [Interface Principal](#interface-principal)
5. [Funcionalidades Disponíveis](#funcionalidades-disponíveis)
6. [Limitações Conhecidas](#limitações-conhecidas)

---

## 🎯 Visão Geral

O **EagleView** é uma plataforma para criação e visualização de timelapses. O sistema permite que administradores criem timelapses a partir de imagens e compartilhem com usuários através de grupos.

### ✅ Funcionalidades Implementadas:
- Criação de timelapses a partir de imagens
- Sistema básico de grupos
- Interface responsiva
- Processamento automático de vídeos
- Navegação em diretórios locais

---

## 🚪 Acesso ao Sistema

### URL de Acesso
```
http://127.0.0.1:8000/
```

### Credenciais Padrão
- **Administrador:** douglas / 18031999

### Processo de Login
1. Acesse a URL do sistema
2. Insira usuário e senha
3. Clique em "Entrar"
4. Será redirecionado para o Dashboard

---

## 👥 Tipos de Usuários

### 🔧 Administrador (is_staff/is_superuser)
**Pode acessar:**
- ✅ Dashboard administrativo
- ✅ Criar/editar timelapses
- ✅ Configurar diretórios do servidor
- ✅ Painel admin Django (/admin/)
- ✅ Todas as funcionalidades do sistema

### 👤 Usuário Regular
**Pode acessar:**
- ✅ Dashboard básico
- ✅ Visualizar timelapses do seu grupo
- ✅ Reproduzir vídeos
- ✅ Fazer download (se permitido)

**Não pode:**
- ❌ Criar timelapses
- ❌ Configurar sistema
- ❌ Acessar admin

---

## 🖥️ Interface Principal

### Barra Lateral (Todos os Usuários)
- **🏠 Painel:** Dashboard principal
- **📁 Projetos:** Lista de timelapses

### Barra Lateral (Apenas Administradores)
- **▶️ Gerar Timelapse:** Criar novos timelapses
- **🗂️ Diretórios:** Configurar diretórios (/diretorios/)
- **⚙️ Configurações:** Configurações de servidor (/configuracoes/)
- **👨‍💼 Admin:** Painel Django (/admin/)

### Área Principal
- Breadcrumb de navegação
- Conteúdo principal
- Mensagens do sistema

---

## 🔧 Funcionalidades Disponíveis

### 1. Dashboard
**URL:** `/`

**Para Administradores:**
- Lista de timelapses recentes
- Botão "Novo Projeto"
- Estatísticas básicas

**Para Usuários:**
- Timelapses disponíveis para seu grupo
- Acesso rápido aos vídeos

### 2. Lista de Projetos
**URL:** `/projetos/`

**Funcionalidades:**
- ✅ Visualização em cards
- ✅ Preview de vídeos (hover)
- ✅ Busca por título/criador
- ✅ Status do processamento
- ✅ Download de vídeos
- ✅ Exclusão (apenas admin)

**Filtros Disponíveis:**
- Busca textual (título + criador)

### 3. Detalhes do Timelapse
**URL:** `/projetos/{id}/`

**Informações Exibidas:**
- Player de vídeo HTML5
- Título e descrição
- Data de criação
- Criador
- Número de frames
- Controles de reprodução

### 4. Criação de Timelapses (Admin)
**URL:** `/editor/`

**Processo:**
1. **Selecionar Diretório:** Escolher diretório configurado
2. **Navegar Pastas:** Explorar subdiretórios
3. **Selecionar Imagens:** Marcar imagens desejadas
4. **Configurar:**
   - Título do projeto
   - FPS (frames por segundo)
   - Grupo proprietário
5. **Processar:** Gerar vídeo automaticamente

### 5. Configuração de Diretórios (Admin)
**URL:** `/diretorios/`

**Funcionalidades:**
- ✅ Listar diretórios configurados
- ✅ Navegar em subdiretórios
- ✅ Visualizar imagens disponíveis
- ✅ Criar timelapses diretamente

### 6. Configurações de Servidor (Admin)
**URL:** `/configuracoes/`

**Funcionalidades:**
- ✅ Gerenciar conexões de servidor
- ✅ Configurar SSH/FTP/SFTP
- ✅ Testar conectividade
- ⚠️ Interface básica (em desenvolvimento)

### 7. Painel Administrativo Django
**URL:** `/admin/`

**Funcionalidades:**
- ✅ Gerenciar usuários e grupos
- ✅ Configurar timelapses
- ✅ Visualizar jobs de processamento
- ✅ Configurar diretórios e conexões
- ✅ Preview de imagens e vídeos

---

## ⚠️ Limitações Conhecidas

### 1. **Sistema de Grupos Básico**
- Apenas Django Groups padrão
- Sem roles intermediários
- Sem interface de gestão de grupos na aplicação

### 2. **Dashboard Simples**
- Sem estatísticas avançadas
- Sem métricas de uso
- Sem gráficos ou relatórios

### 3. **Filtros Limitados**
- Apenas busca textual
- Sem filtros por data, status ou grupo
- Sem ordenação avançada

### 4. **Funcionalidades de Servidor Parciais**
- Modelos criados mas interface incompleta
- Algumas conexões podem não funcionar
- Sem importação automática

### 5. **Sem Sistema de Logs**
- Sem auditoria de ações
- Sem logs de acesso
- Sem monitoramento de erros

### 6. **Sem Relatórios**
- Sem exportação CSV/PDF
- Sem comandos de manutenção
- Sem limpeza automática

---

## 🔄 Fluxos de Trabalho Atuais

### Administrador - Criar Timelapse
```
1. Login → Dashboard
2. "Gerar Timelapse" → Selecionar Diretório
3. Navegar → Selecionar Imagens
4. Configurar Título/FPS → Processar
5. Aguardar → Vídeo Pronto
```

### Usuário - Assistir Timelapse
```
1. Login → Dashboard
2. "Projetos" → Selecionar Vídeo
3. Reproduzir → Download (se permitido)
```

---

## 🛠️ Solução de Problemas

### Problemas Comuns

#### 1. "Não vejo timelapses"
- Verifique se está no grupo correto
- Contate administrador para verificar permissões

#### 2. "Erro ao processar vídeo"
- Verifique se há pelo menos 2 imagens
- Confirme se diretório está acessível
- Aguarde conclusão de outros processamentos

#### 3. "Não consigo criar timelapses"
- Verifique se é administrador (is_staff)
- Configure diretórios primeiro
- Certifique-se que há imagens no diretório

---

## 📱 Compatibilidade

### Navegadores Testados:
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Edge 90+

### Dispositivos:
- ✅ Desktop/Laptop
- ⚠️ Tablet (funcional mas não otimizado)
- ⚠️ Smartphone (apenas visualização)

---

## 🔄 Próximas Atualizações

### Em Desenvolvimento:
- Dashboard com estatísticas
- Filtros avançados
- Sistema de grupos melhorado
- Interface de configuração de servidor completa

### Planejado:
- Sistema de logs
- Relatórios e exportação
- Funcionalidades de compartilhamento
- Comandos de manutenção

---

## 📞 Suporte

**Administrador:** douglas
**Versão:** 1.0.0 (Atual)
**Última Atualização:** Julho 2025

Para problemas técnicos, contate o administrador do sistema.

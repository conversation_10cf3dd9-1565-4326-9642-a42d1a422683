.camerapreview {
    float: left;
    margin: 5px;
    transition: opacity 0.3s, transform 0.3s;
    opacity: 1;
    transform: translateY(0);
}

.cameralivewall {
    float: left;
    margin: 5px;
    min-height: 240px;
    min-width: 320px;
}

.hiddencam {
    opacity: 0;
    transform: translateY(20px);
}

.hiddencam[style*="display: none;"] {
    transform: translateY(0);
}

.searchform {
    float: right;
    margin-right: 0;
    margin-left: 20px;
    position: relative;
    display: flex;
    align-items: center;
}

.searchform input {
    border-radius: 10px;
    border: 1px solid #ccc;
    padding: 8px 8px;
    font-size: 12px;
    outline: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 300px;
}

.searchform input:focus {
    border-color: #3b82f6;
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.searchform .clear-btn {
    position: absolute;
    right: 10px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: #aaa;
    display: none;
    /* clear button is hidden by default */
    align-items: center;
    justify-content: center;
    height: 100%;
    transition: color 0.3s ease;
}

.searchform .clear-btn:hover {
    color: #000;
}

.cameraslist {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    list-style: none;
    margin: 0;
}

.cameraslist .camera-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 10px;
    width: 100%;
    text-decoration: none;
    color: #777777;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cameraslist .camera-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}


.cameraslist .camera-item img {
    object-fit: cover;
    width: 124px;
    height: 75px;
    margin-right: 16px;
}

.cameraslist .camera-item .camera-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    font-size: 14px;
}

/* mobile devs cameras lists */
@media (max-width: 768px) {
    .cameraslist .camera-item {
        flex-direction: column;
        text-align: center;
        padding: 14px;
    }

    .cameraslist .camera-item img {
        margin-right: 0;
        margin-bottom: 8px;
    }
}

/* archive and records player styling */
.archplayercontainer {
    width: 60%;
}

.recplayercontainer {
    width: 70%;
}

.liveplayercontainer {
    width: 70%;
}

.rectimeline {
    width: 67%;
}

@media (max-width: 768px) {
    .archplayercontainer {
        width: 100%;
    }

    .recplayercontainer {
        width: 100%;
    }

    .liveplayercontainer {
        width: 100%;
    }

    .rectimeline {
        width: 100%;
    }

}

.copyright a {
    color: #3b82f6;
}

.recordings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
    width: 100%;
    box-sizing: border-box;
}

.recording-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    background: #f8fafc;
    border-left: 4px solid #3b82f6;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: background 0.3s;
    width: 100%;
}

.recording-item:hover {
    background: #eff6ff;
}

.recording-info {
    flex: 1 1 60%;
    min-width: 250px;
}

.recording-info p {
    margin: 0.3rem 0;
    font-size: 0.95rem;
    color: #333;
}

.recording-info strong {
    color: #555;
    margin-right: 0.3rem;
}

.record-actions {
    display: flex;
    flex: 1 1 30%;
    justify-content: flex-end;
    align-items: center;
    gap: 0.8rem;
    min-width: 120px;
    margin-top: 0.5rem;
}

.record-actions a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 4px;
    border-radius: 6px;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.record-actions a:hover {
    background-color: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 4px rgba(59, 130, 246, 0.2);
}

.record-actions a img {
    width: 16px;
    height: 16px;
    display: block;
}

@media (max-width: 600px) {
    .recording-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .record-actions {
        justify-content: flex-start;
        margin-top: 1rem;
    }
}
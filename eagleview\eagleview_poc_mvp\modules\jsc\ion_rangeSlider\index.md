![ion.rangeSlider](_tmp/logo-ion-range-slider.png)

> English description | <a href="readme.ru.md">Описание на русском</a>

Ion.RangeSlider. Is an easy, flexible and responsive range slider with tons of options.

***

* Version: 2.3.1 | *Version 3.x is under development now*
* <a href="http://ionden.com/a/plugins/ion.rangeSlider/en.html">Project page and demos</a>
* <a href="http://ionden.com/a/plugins/ion.rangeSlider/ion.rangeSlider-2.3.1.zip">Download ZIP</a>

[![](https://pledgie.com/campaigns/25694.png?skin_name=chrome)](https://pledgie.com/campaigns/25694)

## Description
* Ion.RangeSlider — cool, comfortable, responsive and easily customizable range slider
* Supports events and public methods, has flexible settings, can be completely altered with CSS
* Cross-browser: Google Chrome, Mozilla Firefox 3.6+, Opera 12+, Safari 5+, Internet Explorer 8+
* Ion.RangeSlider supports touch-devices (iPhone, iPad, Nexus, etc.).
* Ion.RangeSlider freely distributed under terms of <a href="http://ionden.com/a/plugins/licence.html" target="_blank">MIT licence</a>.
* With this plugin you will be able to build beautiful range sliders, like this:

![ion.rangeSlider](http://ionden.com/a/plugins/ion.rangeSlider/static/img/ion-range-slider.png)

## Key features
* Skin support. (5 skins included and PSD for skin creation)
* Any number of sliders at one page without conflicts and big performance problems
* Two slider types single (1 slider) and double (2 sliders)
* Support of negative and fractional values
* Ability to set custom step and snap grid to step
* Support of custom values diapason
* Customisable grid of values
* Ability to disable UI elements (min and max, current value, grid)
* Postfixes and prefixes for your numbers ($20, 20 &euro; etc.)
* Additional postfix for maximum value (eg. $0 — $100<b>+</b>)
* Ability to prettify large numbers (eg. 10000000 -> 10 000 000 or 10.000.000)
* Slider writes its value right into input value field. This makes it easy to use in any html form
* Any slider value can be set through input data-attribute (eg. data-min="10")
* Slider supports disable param. You can set it true to make slider inactive
* Slider supports external methods (update, reset and remove) to control it after creation
* For advanced users slider has callbacks (onStart, onChange, onFinish, onUpdate). Slider pastes all its params to callback first argument as object
* Slider supports date and time


## Demos

* <a href="http://ionden.com/a/plugins/ion.rangeSlider/demo.html" class="switch__item">Basic demo</a>
* <a href="http://ionden.com/a/plugins/ion.rangeSlider/demo_advanced.html" class="switch__item">Advanced demo</a>
* <a href="http://ionden.com/a/plugins/ion.rangeSlider/demo_interactions.html" class="switch__item">Interactions demo</a>


## GitHub

* <a href="https://github.com/IonDen/ion.rangeSlider">GitHub page</a>


### <a href="history.md">Update history</a>

***

Support the plugin:

[Support the plugin on Patreon](https://www.patreon.com/IonDen)

#!/usr/bin/env node

/**
 * Script para limpar todas as câmeras e adicionar câmeras reais funcionais
 * Baseado nos endpoints fornecidos pelo usuário
 */

const fs = require('fs');
const path = require('path');

// Câmeras reais funcionais baseadas nos endpoints fornecidos
const realCameras = [
  {
    id: 3,
    nome: "Times Square NYC - EarthCam",
    ip: "video3.earthcam.com",
    port: "1935",
    protocol: "hls",
    username: "",
    password: "",
    url_path: "/fecnetwork/hdtimes10.flv/chunklist.m3u8",
    status: "active",
    descricao: "Vista ao vivo da Times Square em Nova York via EarthCam",
    location: "Times Square, Nova York, EUA",
    model: "EarthCam HD Network",
    createdBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastConnection: new Date().toISOString(),
    connection_options: "protocol=hls,adaptive_bitrate=true"
  },
  {
    id: 4,
    nome: "Lauderdale By The Sea - Florida",
    ip: "videos3.earthcam.com",
    port: "80",
    protocol: "hls",
    username: "",
    password: "",
    url_path: "/fecnetwork/9314.flv/playlist.m3u8",
    status: "active",
    descricao: "Vista da praia de Lauderdale By The Sea, Florida",
    location: "Lauderdale By The Sea, Florida, EUA",
    model: "EarthCam Network",
    createdBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastConnection: new Date().toISOString(),
    connection_options: "protocol=hls,adaptive_bitrate=true"
  },
  {
    id: 5,
    nome: "São Paulo Traffic Cam",
    ip: "www.weatherbug.com",
    port: "443",
    protocol: "https",
    username: "",
    password: "",
    url_path: "/traffic-cam/sao-paulo-sao-paulo-br",
    status: "active",
    descricao: "Câmera de trânsito de São Paulo via WeatherBug",
    location: "São Paulo, SP, Brasil",
    model: "WeatherBug Traffic Cam",
    createdBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastConnection: new Date().toISOString(),
    connection_options: "protocol=https,format=mjpeg"
  },
  {
    id: 6,
    nome: "Ubatuba Traffic Cam",
    ip: "www.weatherbug.com",
    port: "443",
    protocol: "https",
    username: "",
    password: "",
    url_path: "/traffic-cam/ubatuba-sao-paulo-br",
    status: "active",
    descricao: "Câmera de trânsito de Ubatuba via WeatherBug",
    location: "Ubatuba, SP, Brasil",
    model: "WeatherBug Traffic Cam",
    createdBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastConnection: new Date().toISOString(),
    connection_options: "protocol=https,format=mjpeg"
  },
  {
    id: 7,
    nome: "PR-18 Km 2.5 San Juan",
    ip: "www.dtop.gov.pr",
    port: "443",
    protocol: "https",
    username: "",
    password: "",
    url_path: "/camaras-trafico/pr18-km2-5",
    status: "active",
    descricao: "Câmera de trânsito PR-18 Km 2.5 em San Juan, Porto Rico",
    location: "San Juan, Porto Rico",
    model: "DTOP Traffic Cam",
    createdBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastConnection: new Date().toISOString(),
    connection_options: "protocol=https,format=jpeg,refresh_rate=30000"
  },
  {
    id: 8,
    nome: "Waikato Traffic Cam",
    ip: "www.journeys.nzta.govt.nz",
    port: "443",
    protocol: "https",
    username: "",
    password: "",
    url_path: "/traffic-cameras/waikato",
    status: "active",
    descricao: "Câmera de trânsito de Waikato, Nova Zelândia",
    location: "Waikato, Nova Zelândia",
    model: "NZTA Journey Planner",
    createdBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastConnection: new Date().toISOString(),
    connection_options: "protocol=https,format=jpeg,refresh_rate=60000"
  }
];

// Função para limpar e atualizar o banco de dados
function cleanAndUpdateDatabase() {
  const dbPath = path.join(__dirname, '..', 'src', 'db.json');
  
  try {
    // Lê o banco atual
    const dbData = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
    
    // Mantém apenas a câmera "home" (ID: 2)
    const homeCam = dbData.cameras.find(c => c.id === 2);
    
    if (homeCam) {
      dbData.cameras = [homeCam];
      console.log('✅ Câmera "home" mantida');
    } else {
      dbData.cameras = [];
      console.log('⚠️  Câmera "home" não encontrada, iniciando com array vazio');
    }
    
    // Adiciona as novas câmeras reais
    realCameras.forEach(camera => {
      dbData.cameras.push(camera);
      console.log(`✅ Câmera real adicionada: ${camera.nome}`);
    });
    
    // Salva o banco atualizado
    fs.writeFileSync(dbPath, JSON.stringify(dbData, null, 2));
    console.log(`\n🎉 Banco de dados limpo e atualizado com sucesso!`);
    console.log(`📊 Total de câmeras: ${dbData.cameras.length}`);
    console.log(`🌐 Câmeras reais funcionais: ${realCameras.length}`);
    
    // Lista as câmeras por protocolo
    const protocolCount = {};
    dbData.cameras.forEach(cam => {
      protocolCount[cam.protocol] = (protocolCount[cam.protocol] || 0) + 1;
    });
    
    console.log('\n📋 Protocolos configurados:');
    Object.entries(protocolCount).forEach(([protocol, count]) => {
      console.log(`  - ${protocol.toUpperCase()}: ${count} câmera(s)`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao atualizar banco de dados:', error);
  }
}

// Executa o script
console.log('🧹 Limpando câmeras antigas e adicionando câmeras reais...\n');
cleanAndUpdateDatabase();

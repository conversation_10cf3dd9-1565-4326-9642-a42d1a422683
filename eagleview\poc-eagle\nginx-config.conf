server {
    listen 80;
    server_name **************;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Configuração para arquivos estáticos
    location /_next/static/ {
        alias /opt/poc-eagle/.next/static/;
        expires 365d;
        access_log off;
    }

    location /public/ {
        alias /opt/poc-eagle/public/;
        expires 365d;
        access_log off;
    }
} 
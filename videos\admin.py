from django.contrib import admin
from django.utils.html import format_html
from .models import Timelapse, FrameImage, ProcessingJob, ServerConnection, ServerDirectory


@admin.register(Timelapse)
class TimelapseAdmin(admin.ModelAdmin):
    list_display = ['title', 'tenant', 'created_by', 'created_at', 'video_preview']
    list_filter = ['tenant', 'created_at', 'created_by']
    search_fields = ['title', 'tenant__name']
    readonly_fields = ['created_at', 'video_preview']
    
    def video_preview(self, obj):
        if obj.video:
            return format_html(
                '<video width="300" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "Sem vídeo"
    video_preview.short_description = "Preview do Vídeo"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Se é um novo objeto
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(FrameImage)
class FrameImageAdmin(admin.ModelAdmin):
    list_display = ['filename', 'timelapse', 'order', 'selected', 'image_preview']
    list_filter = ['timelapse', 'selected', 'uploaded_at']
    search_fields = ['filename', 'timelapse__title']
    list_editable = ['order', 'selected']
    readonly_fields = ['uploaded_at', 'image_preview']
    
    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 150px;" />',
                obj.image.url
            )
        return "Sem imagem"
    image_preview.short_description = "Preview da Imagem"


@admin.register(ProcessingJob)
class ProcessingJobAdmin(admin.ModelAdmin):
    list_display = ['id', 'timelapse', 'status', 'started_at', 'completed_at']
    list_filter = ['status', 'started_at']
    search_fields = ['timelapse__title']
    readonly_fields = ['started_at', 'completed_at']


@admin.register(ServerConnection)
class ServerConnectionAdmin(admin.ModelAdmin):
    list_display = ('name', 'connection_type', 'host', 'port', 'username', 'is_active', 'last_connected', 'group')
    list_filter = ('connection_type', 'is_active', 'group', 'created_at')
    search_fields = ('name', 'host', 'username')
    readonly_fields = ('last_connected', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Informações Básicas', {
            'fields': ('name', 'connection_type', 'group', 'is_active')
        }),
        ('Configurações de Conexão', {
            'fields': ('host', 'port', 'username', 'password', 'private_key_path')
        }),
        ('Configurações Remotas', {
            'fields': ('remote_path',)
        }),
        ('Informações do Sistema', {
            'fields': ('last_connected', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(group__in=request.user.groups.all())
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "group" and not request.user.is_superuser:
            kwargs["queryset"] = request.user.groups.all()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(ServerDirectory)
class ServerDirectoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'path', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at', 'allowed_groups']
    search_fields = ['name', 'path', 'description']
    filter_horizontal = ['allowed_groups']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Informações Básicas', {
            'fields': ('name', 'path', 'description', 'is_active')
        }),
        ('Permissões', {
            'fields': ('allowed_groups',),
            'description': 'Deixe vazio para permitir acesso a todos os grupos'
        }),
        ('Metadados', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


# Personalizando o título do admin
admin.site.site_header = "EagleView Admin"
admin.site.site_title = "EagleView"
admin.site.index_title = "Painel de Controle"

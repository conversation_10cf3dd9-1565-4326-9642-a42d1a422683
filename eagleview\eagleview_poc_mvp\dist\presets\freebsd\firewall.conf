#!/bin/sh

# firewall command
FwCMD="/sbin/ipfw -q"
${FwCMD} -f flush


# Networks defines
# Safe zones
${FwCMD} table 22 add 127.0.0.1


# Safe zones allow policy
${FwCMD} add 45 allow ip from table\(22\) to me
${FwCMD} add 45 allow ip from me to table\(22\)

# ssh access
#${FwCMD} add 46 deny ip from any to me  dst-port 22
#${FwCMD} add 46 deny ip from me to any src-port 22

# snmp access
#${FwCMD} add 47 deny ip from any to me  dst-port 161
#${FwCMD} add 47 deny ip from me to any src-port 161

# mysql access
${FwCMD} add 48 deny ip from any to me  dst-port 3306
${FwCMD} add 48 deny ip from me to any src-port 3306


${FwCMD} add 65535 allow all from any to any


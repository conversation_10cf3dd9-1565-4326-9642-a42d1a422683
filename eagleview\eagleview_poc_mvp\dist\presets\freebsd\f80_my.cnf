[client]
port                            = 3306
socket                          = /tmp/mysql.sock

[mysql]
prompt                          = \u@\h [\d]>_
no_auto_rehash

[mysqld]
user                            = mysql
port                            = 3306
socket                          = /tmp/mysql.sock
bind-address                    = 127.0.0.1

basedir                         = /usr/local
datadir                         = /var/db/mysql
tmpdir                          = /var/db/mysql_tmpdir
replica-load-tmpdir             = /var/db/mysql_tmpdir
secure-file-priv                = /var/db/mysql_secure

# Logging & Replication
log-bin                         = mysql-bin
log-output                      = TABLE
relay-log-recovery              = 1
server-id                       = 1
sync_binlog                     = 1
sync_relay_log                  = 1
binlog_cache_size               = 16M
binlog_expire_logs_seconds      = 2592000
enforce-gtid-consistency        = 1
gtid-mode                       = ON

# General Behavior
default_password_lifetime       = 0
explicit-defaults-for-timestamp = 1
safe-user-create                = 1
lower_case_table_names          = 1

# Performance Tuning
open_files_limit                = 32768
table_open_cache                = 16384
table_definition_cache          = 8192
net_retry_count                 = 16384

# Buffers & Cache
key_buffer_size                 = 512M
myisam_sort_buffer_size         = 64M
read_buffer_size                = 4M
sort_buffer_size                = 4M
max_allowed_packet              = 64M

# Timeout Settings
wait_timeout                    = 86400
interactive_timeout             = 86400
connect_timeout                 = 60
net_read_timeout                = 120
net_write_timeout               = 120

# InnoDB Config
innodb_buffer_pool_size         = 1G
innodb_data_home_dir            = /var/db/mysql
innodb_log_group_home_dir       = /var/db/mysql
innodb_data_file_path           = ibdata1:128M:autoextend
innodb_temp_data_file_path      = ibtmp1:128M:autoextend
innodb_flush_method             = O_DIRECT
innodb_redo_log_capacity        = 512M
innodb_log_buffer_size          = 16M
innodb_write_io_threads         = 8
innodb_read_io_threads          = 8
innodb_autoinc_lock_mode        = 2

# Connections
max_connections                 = 500
max_connect_errors              = 1000000
skip-name-resolve               = 1

# Slow query
slow-query-log                  = 1
long_query_time                 = 0.5

# SQL Mode
sql_mode                        = NO_ENGINE_SUBSTITUTION

[mysqldump]
max_allowed_packet              = 256M
quote_names
quick


'use client';
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Logo from '@/components/Logo';
import { dbService } from '@/services/db.service';
import { Button } from '@/app/components/Button';

interface FormData {
  nome: string;
  email: string;
  senha: string;
  confirmarSenha: string;
}

interface ValidationErrors {
  nome?: string;
  email?: string;
  senha?: string;
  confirmarSenha?: string;
  global?: string;
}

const Cadastro = () => {
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    nome: '',
    email: '',
    senha: '',
    confirmarSenha: ''
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Limpa o erro do campo que está sendo editado
    if (errors[name as keyof ValidationErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    // Avalia a força da senha se o campo for senha
    if (name === 'senha') {
      evaluatePasswordStrength(value);
    }
  };

  // Avalia a força da senha
  const evaluatePasswordStrength = (password: string) => {
    let strength = 0;

    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    setPasswordStrength(strength);
  };

  const validateForm = useCallback((): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    // Validações de nome
    if (!formData.nome.trim()) {
      newErrors.nome = 'Nome é obrigatório';
      isValid = false;
    } else if (formData.nome.trim().length < 3) {
      newErrors.nome = 'Nome deve ter pelo menos 3 caracteres';
      isValid = false;
    }

    // Validações de email
    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
      isValid = false;
    }

    // Validações de senha
    if (!formData.senha) {
      newErrors.senha = 'Senha é obrigatória';
      isValid = false;
    } else if (formData.senha.length < 8) {
      newErrors.senha = 'Senha deve ter pelo menos 8 caracteres';
      isValid = false;
    } else if (!/[A-Z]/.test(formData.senha)) {
      newErrors.senha = 'Senha deve conter pelo menos uma letra maiúscula';
      isValid = false;
    } else if (!/[0-9]/.test(formData.senha)) {
      newErrors.senha = 'Senha deve conter pelo menos um número';
      isValid = false;
    } else if (!/[^A-Za-z0-9]/.test(formData.senha)) {
      newErrors.senha = 'Senha deve conter pelo menos um caractere especial';
      isValid = false;
    }

    // Validação da confirmação de senha
    if (!formData.confirmarSenha) {
      newErrors.confirmarSenha = 'Confirmação de senha é obrigatória';
      isValid = false;
    } else if (formData.senha !== formData.confirmarSenha) {
      newErrors.confirmarSenha = 'As senhas não coincidem';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  }, [formData]);

  useEffect(() => {
    if (formSubmitted) {
      validateForm();
    }
  }, [formData, formSubmitted, validateForm]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormSubmitted(true);

    // Valida o formulário
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Verifica se o e-mail já está cadastrado
      const existingUser = await dbService.getUserByEmail(formData.email);
      if (existingUser) {
        setErrors({ global: 'Este e-mail já está cadastrado' });
        setIsLoading(false);
        return;
      }

      // Cria o novo usuário
      await dbService.createUser({
        nome: formData.nome,
        email: formData.email,
        senha: formData.senha,
        role: 'user',
        status: 'active',
        lastLogin: null,
        updatedAt: new Date().toISOString(),
        preferences: {
          language: 'pt-BR',
          theme: 'light',
          notifications: true
        }
      });

      // Redireciona para o login com sucesso
      router.push('/login?cadastro=success');
    } catch (err) {
      console.error('Erro ao criar conta:', err);
      setErrors({ global: 'Erro ao criar conta. Por favor, tente novamente.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Renderiza a barra de força da senha
  const renderPasswordStrengthBar = () => {
    const getColorClass = () => {
      if (passwordStrength === 0) return 'bg-gray-300';
      if (passwordStrength === 1) return 'bg-red-500';
      if (passwordStrength === 2) return 'bg-amber-500';
      if (passwordStrength === 3) return 'bg-blue-500';
      return 'bg-emerald-500';
    };

    const getStrengthText = () => {
      if (passwordStrength === 0) return 'Muito fraca';
      if (passwordStrength === 1) return 'Fraca';
      if (passwordStrength === 2) return 'Média';
      if (passwordStrength === 3) return 'Forte';
      return 'Muito forte';
    };

    const getTextColorClass = () => {
      if (passwordStrength <= 1) return 'text-red-600';
      if (passwordStrength === 2) return 'text-amber-600';
      if (passwordStrength === 3) return 'text-blue-600';
      return 'text-emerald-600';
    };

    return (
      <div className="mt-3">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-gray-700">Força da senha</span>
          {formData.senha && (
            <span className={`text-xs font-semibold ${getTextColorClass()}`}>
              {getStrengthText()}
            </span>
          )}
        </div>
        <div className="flex h-2 w-full overflow-hidden rounded-full bg-gray-200">
          <div
            className={`transition-all duration-500 ease-out rounded-full ${getColorClass()}`}
            style={{ width: `${(passwordStrength / 4) * 100}%` }}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background decorativo */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl -translate-x-1/2 translate-y-1/2"></div>
        <div className="absolute top-1/3 left-1/3 w-64 h-64 bg-indigo-400/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-lg">
          <div className="card card-elevated p-8 lg:p-10 animate-slide-in backdrop-blur-sm bg-white/95">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                </div>
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-2">
                Criar Nova Conta
              </h1>
              <p className="text-gray-600">
                Junte-se ao Eagle View Camera System
              </p>
            </div>

            {errors.global && (
              <div className="alert alert-error mb-6 animate-slide-in">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{errors.global}</span>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Nome field */}
              <div className="form-group">
                <label htmlFor="nome" className="form-label">
                  <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Nome Completo
                </label>
                <input
                  type="text"
                  id="nome"
                  name="nome"
                  value={formData.nome}
                  onChange={handleChange}
                  className={`form-input focus-ring ${errors.nome ? 'error' : ''}`}
                  placeholder="Digite seu nome completo"
                  disabled={isLoading}
                  autoFocus
                />
                {errors.nome && <p className="form-message error">{errors.nome}</p>}
              </div>

              {/* Email field */}
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                  Endereço de Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`form-input focus-ring ${errors.email ? 'error' : ''}`}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />
                {errors.email && <p className="form-message error">{errors.email}</p>}
              </div>

              {/* Password field */}
              <div className="form-group">
                <label htmlFor="senha" className="form-label">
                  <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Senha
                </label>
                <input
                  type="password"
                  id="senha"
                  name="senha"
                  value={formData.senha}
                  onChange={handleChange}
                  className={`form-input focus-ring ${errors.senha ? 'error' : ''}`}
                  placeholder="••••••••"
                  disabled={isLoading}
                />
                {renderPasswordStrengthBar()}
                {errors.senha && <p className="form-message error">{errors.senha}</p>}

                {/* Password requirements */}
                <div className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <p className="text-xs font-medium text-gray-700 mb-2">Requisitos da senha:</p>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className={`flex items-center gap-1 ${formData.senha.length >= 8 ? 'text-emerald-600' : 'text-gray-500'}`}>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>8+ caracteres</span>
                    </div>
                    <div className={`flex items-center gap-1 ${/[A-Z]/.test(formData.senha) ? 'text-emerald-600' : 'text-gray-500'}`}>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Maiúscula</span>
                    </div>
                    <div className={`flex items-center gap-1 ${/[0-9]/.test(formData.senha) ? 'text-emerald-600' : 'text-gray-500'}`}>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Número</span>
                    </div>
                    <div className={`flex items-center gap-1 ${/[^A-Za-z0-9]/.test(formData.senha) ? 'text-emerald-600' : 'text-gray-500'}`}>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Especial</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Confirm password field */}
              <div className="form-group">
                <label htmlFor="confirmarSenha" className="form-label">
                  <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Confirmar Senha
                </label>
                <input
                  type="password"
                  id="confirmarSenha"
                  name="confirmarSenha"
                  value={formData.confirmarSenha}
                  onChange={handleChange}
                  className={`form-input focus-ring ${errors.confirmarSenha ? 'error' : formData.confirmarSenha && formData.senha === formData.confirmarSenha ? 'success' : ''}`}
                  placeholder="••••••••"
                  disabled={isLoading}
                />
                {formData.confirmarSenha && formData.senha === formData.confirmarSenha && !errors.confirmarSenha && (
                  <p className="form-message success">✓ Senhas coincidem</p>
                )}
                {errors.confirmarSenha && <p className="form-message error">{errors.confirmarSenha}</p>}
              </div>

              {/* Submit button */}
              <button
                type="submit"
                disabled={isLoading}
                className="btn btn-primary btn-lg w-full hover-lift"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                    <span>Criando conta...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    <span>Criar Conta</span>
                  </div>
                )}
              </button>
            </form>

            {/* Footer */}
            <div className="mt-8 pt-6 border-t border-gray-100 text-center">
              <p className="text-gray-600 mb-4">
                Já tem uma conta?
              </p>
              <Link
                href="/login"
                className="btn btn-outline btn-md w-full group"
              >
                <svg className="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                <span>Fazer Login</span>
              </Link>
            </div>

            {/* Security notice */}
            <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-blue-900 mb-1">Seus dados estão seguros</h4>
                  <p className="text-sm text-blue-700">
                    Utilizamos criptografia de ponta a ponta para proteger suas informações pessoais.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-6 text-center">
        <p className="text-sm text-gray-500">
          © 2025 Eagle View Camera System. Todos os direitos reservados.
        </p>
      </div>
    </div>
  );
};

export default Cadastro;
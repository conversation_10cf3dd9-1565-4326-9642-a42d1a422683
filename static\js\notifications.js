// Sistema de Notificações Toast
class NotificationSystem {
    constructor() {
        this.container = this.createContainer();
        document.body.appendChild(this.container);
    }
    
    createContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        return container;
    }
    
    show(message, type = 'info', duration = 5000) {
        const toast = this.createToast(message, type);
        this.container.appendChild(toast);
        
        // Animar entrada
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // Auto-remover
        setTimeout(() => {
            this.remove(toast);
        }, duration);
        
        return toast;
    }
    
    createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${this.getBootstrapColor(type)} border-0`;
        toast.setAttribute('role', 'alert');
        
        const icon = this.getIcon(type);
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="${icon} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        onclick="notifications.remove(this.closest('.toast'))"></button>
            </div>
        `;
        
        return toast;
    }
    
    remove(toast) {
        toast.classList.add('fade');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
    
    getBootstrapColor(type) {
        const colors = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'primary'
        };
        return colors[type] || 'primary';
    }
    
    getIcon(type) {
        const icons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };
        return icons[type] || 'fas fa-info-circle';
    }
    
    success(message, duration = 4000) {
        return this.show(message, 'success', duration);
    }
    
    error(message, duration = 6000) {
        return this.show(message, 'error', duration);
    }
    
    warning(message, duration = 5000) {
        return this.show(message, 'warning', duration);
    }
    
    info(message, duration = 4000) {
        return this.show(message, 'info', duration);
    }
}

// Instância global
const notifications = new NotificationSystem();

// Progress Bar Controller
class ProgressController {
    constructor(elementId) {
        this.element = document.getElementById(elementId);
        this.bar = this.element?.querySelector('.progress-bar');
        this.text = this.element?.querySelector('.progress-text');
    }
    
    show() {
        if (this.element) {
            this.element.style.display = 'block';
        }
    }
    
    hide() {
        if (this.element) {
            this.element.style.display = 'none';
        }
    }
    
    setProgress(percent, text = '') {
        if (this.bar) {
            this.bar.style.width = `${percent}%`;
            this.bar.setAttribute('aria-valuenow', percent);
        }
        
        if (this.text && text) {
            this.text.textContent = text;
        }
    }
    
    setIndeterminate(text = 'Processando...') {
        if (this.bar) {
            this.bar.classList.add('progress-bar-striped', 'progress-bar-animated');
            this.bar.style.width = '100%';
        }
        
        if (this.text) {
            this.text.textContent = text;
        }
    }
    
    setComplete(text = 'Concluído!') {
        if (this.bar) {
            this.bar.classList.remove('progress-bar-striped', 'progress-bar-animated');
            this.bar.classList.add('bg-success');
            this.bar.style.width = '100%';
        }
        
        if (this.text) {
            this.text.textContent = text;
        }
    }
    
    setError(text = 'Erro no processamento') {
        if (this.bar) {
            this.bar.classList.remove('progress-bar-striped', 'progress-bar-animated');
            this.bar.classList.add('bg-danger');
        }
        
        if (this.text) {
            this.text.textContent = text;
        }
    }
}

// Upload Progress Handler
class UploadHandler {
    constructor(formId, progressId) {
        this.form = document.getElementById(formId);
        this.progress = new ProgressController(progressId);
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                this.handleSubmit(e);
            });
        }
    }
    
    handleSubmit(event) {
        const fileInput = this.form.querySelector('input[type="file"]');
        const files = fileInput?.files;
        
        if (!files || files.length === 0) {
            notifications.warning('Selecione pelo menos uma imagem.');
            event.preventDefault();
            return;
        }
        
        // Validar tipos de arquivo
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        const invalidFiles = [];
        
        for (let file of files) {
            if (!validTypes.includes(file.type)) {
                invalidFiles.push(file.name);
            }
        }
        
        if (invalidFiles.length > 0) {
            notifications.error(`Arquivos inválidos: ${invalidFiles.join(', ')}`);
            event.preventDefault();
            return;
        }
        
        // Validar tamanho total (100MB max)
        const totalSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);
        const maxSize = 100 * 1024 * 1024; // 100MB
        
        if (totalSize > maxSize) {
            notifications.error('Tamanho total dos arquivos excede 100MB.');
            event.preventDefault();
            return;
        }
        
        // Mostrar progresso
        this.progress.show();
        this.progress.setIndeterminate(`Enviando ${files.length} arquivo(s)...`);
        
        // Desabilitar botão de submit
        const submitBtn = this.form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
        }
        
        notifications.info(`Iniciando upload de ${files.length} arquivo(s)...`);
    }
}

// Video Generation Handler
class VideoGenerationHandler {
    constructor(formId) {
        this.form = document.getElementById(formId);
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                this.handleSubmit(e);
            });
        }
    }
    
    handleSubmit(event) {
        const selectedFrames = document.querySelectorAll('.frame-item.selected').length;
        
        if (selectedFrames < 2) {
            notifications.error('Selecione pelo menos 2 frames para gerar o timelapse.');
            event.preventDefault();
            return;
        }
        
        const fps = parseInt(document.getElementById('fps')?.value || 10);
        const duration = Math.round(selectedFrames / fps * 10) / 10;
        
        // Confirmar geração
        if (!confirm(`Gerar timelapse com ${selectedFrames} frames (${duration}s a ${fps} FPS)?`)) {
            event.preventDefault();
            return;
        }
        
        // Mostrar indicador
        const indicator = document.getElementById('processing-indicator');
        const button = document.getElementById('generate-btn');
        
        if (indicator) {
            indicator.style.display = 'block';
        }
        
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processando...';
        }
        
        notifications.info('Iniciando geração do timelapse. Isso pode levar alguns minutos...');
    }
}

// File Drop Handler
class FileDropHandler {
    constructor(dropAreaId, fileInputId) {
        this.dropArea = document.getElementById(dropAreaId);
        this.fileInput = document.getElementById(fileInputId);
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        if (!this.dropArea || !this.fileInput) return;
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.dropArea.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            this.dropArea.addEventListener(eventName, () => this.highlight(), false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            this.dropArea.addEventListener(eventName, () => this.unhighlight(), false);
        });
        
        this.dropArea.addEventListener('drop', (e) => this.handleDrop(e), false);
    }
    
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    highlight() {
        this.dropArea.classList.add('dragover');
    }
    
    unhighlight() {
        this.dropArea.classList.remove('dragover');
    }
    
    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            this.fileInput.files = files;
            notifications.success(`${files.length} arquivo(s) selecionado(s) via drag & drop`);
            
            // Auto-submit se o form existir
            const form = this.fileInput.closest('form');
            if (form) {
                form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
            }
        }
    }
}

// Inicialização automática quando DOM carrega
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar handlers se os elementos existirem
    if (document.getElementById('upload-form')) {
        new UploadHandler('upload-form', 'upload-progress');
    }
    
    if (document.getElementById('generate-form')) {
        new VideoGenerationHandler('generate-form');
    }
    
    if (document.getElementById('drop-area')) {
        new FileDropHandler('drop-area', 'images');
    }
    
    // Converter mensagens Django em notificações toast
    const djangoMessages = document.querySelectorAll('.alert.django-message');
    djangoMessages.forEach(msg => {
        const type = msg.classList.contains('alert-success') ? 'success' :
                    msg.classList.contains('alert-danger') ? 'error' :
                    msg.classList.contains('alert-warning') ? 'warning' : 'info';
        
        notifications.show(msg.textContent.trim(), type);
        msg.style.display = 'none'; // Esconder mensagem original
    });
});

// Exportar para uso global
window.notifications = notifications;
window.ProgressController = ProgressController; 
{% extends 'base.html' %}

{% block title %}{{ timelapse.title }} - EagleView{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{% url 'timelapse_list' %}">Projetos</a></li>
<li class="breadcrumb-item active">{{ timelapse.title }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-video me-2"></i>
                {{ timelapse.title }}
            </h1>
            <div class="btn-group">
                {% if timelapse.video %}
                    <a href="{{ timelapse.video.url }}" download="{{ timelapse.title }}.mp4" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>
                        Download
                    </a>
                {% endif %}
                <a href="{% url 'frame_editor' %}" class="btn btn-secondary">
                    <i class="fas fa-plus me-2"></i>
                    Novo Projeto
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Player de Vídeo -->
{% if timelapse.video %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-0">
                <div class="video-player">
                    <video controls class="w-100" poster="" preload="metadata">
                        <source src="{{ timelapse.video.url }}" type="video/mp4">
                        Seu navegador não suporta o elemento de vídeo.
                    </video>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Informações do Projeto -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Projeto
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Título</h6>
                        <p>{{ timelapse.title }}</p>
                        
                        <h6 class="text-muted">Criado por</h6>
                        <p>{{ timelapse.created_by.get_full_name|default:timelapse.created_by.username }}</p>
                        
                        <h6 class="text-muted">Empresa</h6>
                        <p>{{ timelapse.tenant.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Data de Criação</h6>
                        <p>{{ timelapse.created_at|date:"d/m/Y H:i" }}</p>
                        
                        <h6 class="text-muted">Total de Frames</h6>
                        <p>{{ frames.count }} frame{{ frames.count|pluralize }}</p>
                        
                        {% if timelapse.video %}
                        <h6 class="text-muted">Status</h6>
                        <p><span class="badge bg-success">Concluído</span></p>
                        {% else %}
                        <h6 class="text-muted">Status</h6>
                        <p><span class="badge bg-warning">Sem vídeo gerado</span></p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Ações
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if timelapse.video %}
                        <a href="{{ timelapse.video.url }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-play me-2"></i>
                            Reproduzir em Nova Aba
                        </a>
                        <a href="{{ timelapse.video.url }}" download="{{ timelapse.title }}.mp4" class="btn btn-secondary">
                            <i class="fas fa-download me-2"></i>
                            Download MP4
                        </a>
                    {% endif %}
                    
                    <a href="{% url 'frame_editor' %}" class="btn btn-outline-primary">
                        <i class="fas fa-copy me-2"></i>
                        Criar Novo Similar
                    </a>
                    
                    <button class="btn btn-outline-danger" onclick="deleteTimelapse({{ timelapse.pk }}, '{{ timelapse.title }}')">
                        <i class="fas fa-trash me-2"></i>
                        Excluir Projeto
                    </button>
                </div>
            </div>
        </div>
        
        {% if user.is_superuser or user.is_staff %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    Permissões de Acesso
                </h5>
            </div>
            <div class="card-body">
                <h6 class="text-muted">Grupos com Acesso:</h6>
                {% if timelapse.allowed_groups.all %}
                    {% for group in timelapse.allowed_groups.all %}
                        <span class="badge bg-info me-1 mb-1">{{ group.name }}</span>
                    {% endfor %}
                {% else %}
                    <p class="text-muted small">Apenas o grupo criador ({{ timelapse.tenant.name }})</p>
                {% endif %}
                
                <div class="mt-3">
                    <button class="btn btn-outline-warning btn-sm w-100" data-bs-toggle="modal" data-bs-target="#manageGroupsModal">
                        <i class="fas fa-edit me-2"></i>
                        Gerenciar Acesso
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Galeria de Frames -->
{% if frames %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images me-2"></i>
                    Frames Utilizados ({{ frames.count }})
                </h5>
            </div>
            <div class="card-body">
                <div class="frame-gallery">
                    {% for frame in frames %}
                    <div class="frame-item-view" data-bs-toggle="modal" data-bs-target="#frameModal" data-frame-url="{{ frame.image.url }}" data-frame-name="{{ frame.filename }}">
                        <img src="{{ frame.image.url }}" alt="{{ frame.filename }}" loading="lazy">
                        <div class="frame-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                        <div class="frame-info">
                            <div class="frame-filename">{{ frame.filename }}</div>
                            <div class="frame-order">Ordem: {{ frame.order }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Histórico de Processamento -->
{% if jobs %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Histórico de Processamento
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Status</th>
                                <th>Iniciado em</th>
                                <th>Concluído em</th>
                                <th>Duração</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for job in jobs %}
                            <tr>
                                <td>#{{ job.id }}</td>
                                <td>
                                    {% if job.status == 'completed' %}
                                        <span class="badge bg-success">Concluído</span>
                                    {% elif job.status == 'processing' %}
                                        <span class="badge bg-warning">Processando</span>
                                    {% elif job.status == 'failed' %}
                                        <span class="badge bg-danger">Falhou</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Pendente</span>
                                    {% endif %}
                                </td>
                                <td>{{ job.started_at|date:"d/m/Y H:i:s" }}</td>
                                <td>
                                    {% if job.completed_at %}
                                        {{ job.completed_at|date:"d/m/Y H:i:s" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if job.completed_at %}
                                        {% widthratio job.completed_at.timestamp|floatformat:0|add:0 1 1 %}{% widthratio job.started_at.timestamp|floatformat:0|add:0 1 -1 %}s
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Modal para visualizar frames -->
<div class="modal fade" id="frameModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="frameModalTitle">Frame</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="frameModalImage" src="" class="img-fluid" alt="">
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash me-2"></i>
                    Confirmar Exclusão
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o projeto <strong id="deleteTitle"></strong>?</p>
                <p class="text-muted small">Esta ação não pode ser desfeita e todos os frames associados também serão removidos.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-1"></i>
                    Excluir
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.frame-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
}

.frame-item-view {
    position: relative;
    background: var(--surface-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
}

.frame-item-view:hover {
    border-color: var(--highlight-orange);
    transform: translateY(-2px);
}

.frame-item-view img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.frame-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
    color: white;
    font-size: 1.5rem;
}

.frame-item-view:hover .frame-overlay {
    opacity: 1;
}

.frame-info {
    padding: 8px;
}

.frame-filename {
    font-size: 0.8rem;
    color: var(--text-white);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.frame-order {
    font-size: 0.7rem;
    color: var(--text-secondary);
}

.video-player {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-player video {
    width: 100%;
    height: auto;
    max-height: 70vh;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Modal de frame
document.getElementById('frameModal').addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    const frameUrl = button.getAttribute('data-frame-url');
    const frameName = button.getAttribute('data-frame-name');
    
    document.getElementById('frameModalTitle').textContent = frameName;
    document.getElementById('frameModalImage').src = frameUrl;
});

// Exclusão de timelapse
let deleteTimelapseId = null;

function deleteTimelapse(id, title) {
    deleteTimelapseId = id;
    document.getElementById('deleteTitle').textContent = title;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (deleteTimelapseId) {
        fetch(`/api/delete/${deleteTimelapseId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{% url "timelapse_list" %}';
            } else {
                alert('Erro ao excluir o projeto: ' + data.error);
            }
        })
        .catch(error => {
            alert('Erro ao excluir o projeto');
        });
        
        bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
    }
});
</script>
{% endblock %} 
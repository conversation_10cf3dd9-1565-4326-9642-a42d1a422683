# EagleView

Sistema de Vigilância Descomplicado

![Logo EagleView](logo.png)

## Sobre o EagleView

O EagleView é um sistema de vigilância moderno e amigável, projetado para simplicidade e eficiência. Com sua interface intuitiva e recursos poderosos, o EagleView facilita o monitoramento e gerenciamento de suas câmeras de segurança.

Desenvolvido para ser uma solução completa de vigilância por vídeo, o EagleView permite que você configure, monitore e gerencie múltiplas câmeras de segurança a partir de uma única interface web. O sistema é altamente personalizável e pode ser adaptado para atender às necessidades específicas de residências, empresas e instalações de qualquer porte.

## Funcionalidades Completas

### Monitoramento e Gravação
- **Visualização ao vivo**: Acompanhe todas as suas câmeras em tempo real
- **Gravação contínua**: Armazene vídeos de forma ininterrupta
- **Gravação por detecção de movimento**: Economize espaço gravando apenas quando houver movimento
- **Gravação programada**: Configure horários específicos para gravação
- **Múltiplos fluxos de vídeo**: Suporte a diferentes resoluções e taxas de quadros
- **Parede de vídeo (Video Wall)**: Visualize múltiplas câmeras simultaneamente em layout personalizável

### Gerenciamento de Vídeo
- **Arquivamento automático**: Rotação e gerenciamento inteligente de espaço em disco
- **Exportação de vídeos**: Salve gravações em formatos compatíveis para uso externo
- **Reprodução avançada**: Controles de reprodução com velocidade variável e navegação por linha do tempo
- **Busca inteligente**: Encontre rapidamente eventos específicos nas gravações
- **Marcadores e anotações**: Marque momentos importantes nas gravações

### Detecção e Análise
- **Detecção de movimento**: Alertas e gravações baseados em movimento na cena
- **Reconhecimento de objetos** (com módulo de IA opcional): Identifique pessoas, veículos e outros objetos
- **Zonas de interesse**: Configure áreas específicas para monitoramento
- **Alertas personalizáveis**: Receba notificações baseadas em eventos específicos

### Administração do Sistema
- **Gerenciamento de usuários**: Crie contas com diferentes níveis de acesso
- **Controle de permissões granular**: Defina exatamente o que cada usuário pode ver e fazer
- **Registro de eventos**: Acompanhe todas as atividades do sistema
- **Backup automático**: Proteja suas configurações e banco de dados
- **Atualizações simplificadas**: Mantenha seu sistema sempre atualizado
- **Diagnóstico e monitoramento**: Verifique a saúde do sistema em tempo real

### Integração e Compatibilidade
- **Suporte a múltiplos modelos de câmeras**: Compatível com diversas marcas e modelos
- **Protocolos padrão**: Suporte a ONVIF, RTSP e outros protocolos comuns
- **API REST**: Integre com outros sistemas e aplicações
- **Acesso remoto**: Monitore suas câmeras de qualquer lugar via internet

### Interface e Usabilidade
- **Interface web responsiva**: Acesse pelo computador, tablet ou smartphone
- **Personalização da interface**: Adapte a aparência conforme suas preferências
- **Suporte a múltiplos idiomas**: Interface disponível em diversos idiomas
- **Modo PWA (Progressive Web App)**: Instale como aplicativo em dispositivos móveis

## Requisitos de Sistema

### Requisitos de Hardware
- **Processador**: Intel Core i5 ou equivalente (recomendado i7 ou superior para mais de 8 câmeras)
- **Memória RAM**: Mínimo de 8GB (recomendado 16GB ou mais para sistemas maiores)
- **Armazenamento**: Depende da quantidade de câmeras e dias de gravação
  - Aproximadamente 10GB por dia para cada câmera em resolução 1080p
  - Recomendado usar discos rígidos dedicados para armazenamento de vídeo
- **Rede**: Conexão Ethernet Gigabit recomendada

### Sistemas Operacionais Suportados
- **FreeBSD**: Versões 13.3+, 14.0+
- **Debian Linux**: Versão 12 (Bookworm)
- **Outros sistemas baseados em Linux**: Compatibilidade parcial, não oficialmente suportados

### Software Necessário
- **Servidor Web**: Apache 2.4+
- **Banco de Dados**: MySQL/MariaDB
- **PHP**: Versão 8.2+
- **FFmpeg**: Para processamento de vídeo
- **Memcached**: Para cache do sistema
- **Redis**: Para armazenamento de dados em tempo real (opcional)

## Instalação

### Instalação no FreeBSD

1. **Preparação do sistema**
   ```bash
   # Instale o FreeBSD (versão 14.0 ou superior recomendada)
   # Certifique-se de que o sistema está atualizado
   freebsd-update fetch install
   ```

2. **Download do instalador**
   ```bash
   # Baixe o instalador do EagleView
   fetch http://eagleview.com/download/installer.sh
   chmod +x installer.sh
   ```

3. **Execute o instalador**
   ```bash
   ./installer.sh
   ```

4. **Siga as instruções na tela**
   - Escolha o tipo de instalação (nova ou migração)
   - Selecione a versão do FreeBSD
   - Configure as senhas e diretórios conforme necessário

5. **Acesse o sistema**
   - Após a conclusão da instalação, acesse o sistema através do navegador:
   - http://endereço-do-servidor/wr/

### Instalação no Debian Linux

1. **Preparação do sistema**
   ```bash
   # Instale o Debian 12 (Bookworm)
   # Atualize o sistema
   apt update
   apt upgrade -y
   ```

2. **Download do instalador**
   ```bash
   # Baixe o instalador do EagleView
   wget http://eagleview.com/download/installer.sh
   chmod +x installer.sh
   ```

3. **Execute o instalador**
   ```bash
   ./installer.sh
   ```

4. **Siga as instruções na tela**
   - Escolha Debian 12 como sistema operacional
   - Configure as senhas e diretórios conforme necessário

5. **Acesse o sistema**
   - Após a conclusão da instalação, acesse o sistema através do navegador:
   - http://endereço-do-servidor/wr/

## Configuração Inicial

### Primeiro Acesso
1. Acesse o sistema através do navegador usando o endereço http://endereço-do-servidor/wr/
2. Faça login com as credenciais padrão:
   - Usuário: `admin`
   - Senha: `admin`
3. Altere imediatamente a senha padrão por motivos de segurança

### Configuração de Armazenamento
1. Acesse o menu **Sistema > Armazenamento**
2. Adicione novos locais de armazenamento conforme necessário
3. Configure as políticas de rotação e retenção de vídeos

### Adição de Câmeras
1. Acesse o menu **Câmeras > Modelos**
2. Adicione os modelos de câmeras que você utilizará
3. Acesse o menu **Câmeras > Câmeras**
4. Clique em "Adicionar" para configurar uma nova câmera
5. Preencha os dados de conexão (endereço IP, porta, usuário, senha)
6. Selecione o modelo correspondente
7. Configure as opções de gravação conforme necessário

### Configuração de Usuários
1. Acesse o menu **Sistema > Gerenciamento de Usuários**
2. Adicione novos usuários conforme necessário
3. Configure as permissões para cada usuário

## Uso Básico

### Visualização ao Vivo
1. Acesse o menu **Câmeras ao Vivo**
2. Selecione as câmeras que deseja visualizar
3. Utilize os controles para ajustar a visualização

### Reprodução de Gravações
1. Acesse o menu **Arquivo**
2. Selecione a câmera e o período desejado
3. Utilize os controles de reprodução para navegar pelo vídeo

### Exportação de Vídeos
1. Durante a reprodução, selecione o trecho que deseja exportar
2. Clique no botão "Exportar"
3. Escolha o formato e as opções de exportação
4. Aguarde a conclusão do processo
5. Acesse o menu **Exportar > Meus Registros** para baixar o arquivo

## Solução de Problemas Comuns

### Problemas de Conexão com Câmeras
- **Problema**: Não é possível conectar a uma câmera
- **Solução**:
  - Verifique se a câmera está ligada e conectada à rede
  - Confirme se o endereço IP, porta, usuário e senha estão corretos
  - Teste a conexão direta com a câmera usando outro software
  - Verifique se o modelo da câmera é compatível com o sistema

### Problemas de Desempenho
- **Problema**: Sistema lento ou instável
- **Solução**:
  - Verifique os recursos do servidor (CPU, memória, disco)
  - Reduza a resolução ou taxa de quadros das câmeras
  - Ajuste as configurações de gravação para otimizar o uso de recursos
  - Verifique se há espaço suficiente nos dispositivos de armazenamento

### Problemas de Gravação
- **Problema**: Vídeos não estão sendo gravados
- **Solução**:
  - Verifique se há espaço disponível no armazenamento
  - Confirme se as permissões de diretório estão corretas
  - Verifique as configurações de gravação da câmera
  - Consulte os logs do sistema para identificar possíveis erros

### Problemas de Acesso
- **Problema**: Não é possível acessar o sistema
- **Solução**:
  - Verifique se o servidor web está em execução
  - Confirme se o banco de dados está funcionando corretamente
  - Verifique os logs do servidor web para identificar erros
  - Reinicie os serviços do sistema se necessário

## Como Contribuir

O EagleView é um software de código aberto e agradecemos contribuições da comunidade. Aqui estão algumas maneiras de contribuir:

### Reportando Problemas
1. Verifique se o problema já não foi reportado na seção de issues
2. Forneça informações detalhadas sobre o problema, incluindo:
   - Versão do EagleView
   - Sistema operacional e versão
   - Passos para reproduzir o problema
   - Logs relevantes
   - Capturas de tela, se aplicável

### Contribuindo com Código
1. Faça um fork do repositório
2. Crie um branch para sua contribuição
3. Implemente suas alterações, seguindo o estilo de código do projeto
4. Escreva testes para suas alterações, se aplicável
5. Envie um pull request com uma descrição clara das alterações

### Diretrizes de Contribuição
- Mantenha o código limpo e bem documentado
- Siga as convenções de nomenclatura existentes
- Teste suas alterações antes de enviar
- Respeite o estilo de código do projeto
- Um problema por pull request

## Licença e Direitos Autorais

O EagleView é distribuído sob a licença GNU General Public License v3.0 (GPL-3.0).

### Resumo da Licença
- Você pode usar, modificar e distribuir o software
- Se você distribuir o software, deve disponibilizar o código fonte
- Qualquer software derivado deve ser distribuído sob a mesma licença
- Não há garantia para o software

### Direitos Autorais
Copyright © 2023-2025 EagleView Team

### Atribuições
O EagleView utiliza várias bibliotecas e componentes de terceiros, cada um com suas próprias licenças:
- jQuery: MIT License
- Ion.RangeSlider: MIT License
- Diversos componentes JavaScript e CSS: Licenças variadas, conforme documentado nos respectivos arquivos

## Recursos Oficiais

* [Página do projeto](https://eagleview.com/)
* [Documentação](https://eagleview.com/wiki/)
* [Chat da comunidade](https://t.me/eagleview)

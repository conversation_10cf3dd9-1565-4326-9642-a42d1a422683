# 🦅 EagleView - Plataforma de Geração de Timelapses

Uma plataforma moderna e intuitiva para criação de timelapses de alta qualidade, desenvolvida em Django com design em modo escuro e interface responsiva.

## ✨ Características

- **🎨 Design Moderno**: Interface em modo escuro com paleta laranja/azul/branco
- **📱 Responsivo**: Funciona perfeitamente em desktop, tablet e mobile
- **🏢 Multi-tenant**: Sistema de empresas com controle de acesso por grupos
- **🎬 Processamento de Vídeo**: Geração de timelapses 4K usando FFmpeg
- **🖼️ Galeria Intuitiva**: Seleção visual de frames com drag & drop
- **📊 Dashboard Completo**: Estatísticas e acesso rápido às funcionalidades
- **🔒 Segurança**: Autenticação Django com controle de acesso baseado em grupos

## 🛠️ Stack Tecnológica

| Componente | Tecnologia | Justificativa |
|------------|------------|---------------|
| **Backend** | Django 5.2 | Framework completo com admin, auth e ORM |
| **Banco de Dados** | SQLite (dev) → PostgreSQL (prod) | Simplicidade inicial com migração fácil |
| **Processamento** | FFmpeg + Python subprocess | Único dependência externa real |
| **Frontend** | Django Templates + Bootstrap 5 | Zero complexidade JS, renderização server-side |
| **Armazenamento** | Pasta `media/` → S3 (futuro) | Mantém simplicidade inicial |
| **Estilo** | CSS customizado + Font Awesome | Design consistente e moderno |

## 🚀 Instalação Rápida

### Pré-requisitos
- Python 3.10+
- FFmpeg instalado e no PATH do sistema
- Git

### 1. Clonar o Repositório
```bash
git clone <repository-url>
cd eagleview_django
```

### 2. Configurar Ambiente Virtual
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. Instalar Dependências
```bash
pip install django pillow ffmpeg-python
```

### 4. Configurar Banco de Dados
```bash
python manage.py migrate
python manage.py createsuperuser
python manage.py setup_demo
```

### 5. Executar Servidor
```bash
python manage.py runserver
```

**🌐 Acesso**: http://localhost:8000

## 👥 Contas de Demonstração

### Usuário Demo
- **Login**: `demo`
- **Senha**: `demo123`
- **Empresa**: Empresa Demo

### Administrador
- Criado durante o `createsuperuser`
- **Admin**: http://localhost:8000/admin/

## 📋 Como Usar

### 1. **Fazer Login**
Acesse `/login/` e entre com suas credenciais.

### 2. **Upload de Frames**
- Vá para "Gerar Timelapse"
- Selecione múltiplas imagens (JPG/PNG)
- Aguarde o upload ser concluído

### 3. **Configurar Timelapse**
- Defina um título para o projeto
- Escolha o FPS (24 FPS recomendado)
- Selecione os frames desejados na galeria

### 4. **Gerar Vídeo**
- Clique em "Gerar Timelapse"
- O sistema processará automaticamente
- O vídeo estará disponível para download

### 5. **Gerenciar Projetos**
- Visualize todos os projetos em "Meus Projetos"
- Faça download dos vídeos gerados
- Exclua projetos desnecessários

## 🎨 Design System

### Paleta de Cores
```css
--bg-dark: #121212          /* Fundo principal */
--surface-secondary: #1E1E1E /* Cards e modais */
--highlight-orange: #FF6A00  /* Botões primários */
--highlight-blue: #005B96    /* Links e estados ativos */
--text-white: #FFFFFF        /* Texto principal */
--text-secondary: #B3B3B3    /* Texto secundário */
```

### Layout
- **Sidebar**: 280px fixo à esquerda
- **Header**: 64px de altura
- **Tipografia**: Inter (Google Fonts)
- **Ícones**: Font Awesome 6

## 📁 Estrutura do Projeto

```
eagleview_django/
├── eagleview/              # Configurações do Django
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── videos/                 # App principal
│   ├── models.py          # Modelos (Timelapse, FrameImage, ProcessingJob)
│   ├── views.py           # Views principais
│   ├── admin.py           # Configuração do admin
│   ├── urls.py            # URLs do app
│   └── management/        # Comandos personalizados
├── templates/             # Templates HTML
│   ├── base.html         # Template base
│   ├── registration/     # Templates de login
│   └── videos/           # Templates do app
├── static/               # Arquivos estáticos
│   ├── css/             # Estilos customizados
│   ├── js/              # JavaScript
│   └── images/          # Imagens (logo)
├── media/               # Uploads (frames e vídeos)
└── requirements.txt     # Dependências
```

## 🔧 Modelos de Dados

### Timelapse
- `tenant`: Empresa (ForeignKey para Group)
- `title`: Título do projeto
- `video`: Arquivo de vídeo gerado
- `created_at`: Data de criação
- `created_by`: Usuário criador

### FrameImage
- `timelapse`: Projeto relacionado
- `image`: Arquivo de imagem
- `filename`: Nome original
- `order`: Ordem na sequência
- `selected`: Se está selecionado

### ProcessingJob
- `timelapse`: Projeto relacionado
- `status`: Status do processamento
- `started_at`: Início do processamento
- `completed_at`: Conclusão do processamento
- `error_message`: Mensagem de erro (se houver)

## 🚀 Deploy em Produção

### Configurações Recomendadas

1. **Variáveis de Ambiente**
```python
DEBUG = False
ALLOWED_HOSTS = ['seu-dominio.com']
SECRET_KEY = 'sua-chave-secreta-super-segura'
```

2. **Banco de Dados PostgreSQL**
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'eagleview',
        'USER': 'usuario',
        'PASSWORD': 'senha',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

3. **Arquivos Estáticos**
```python
STATIC_ROOT = '/var/www/eagleview/static/'
MEDIA_ROOT = '/var/www/eagleview/media/'
```

### Stack de Deploy Recomendada
- **Servidor**: Ubuntu 22.04 LTS
- **Web Server**: Nginx
- **WSGI**: Gunicorn
- **Banco**: PostgreSQL 14+
- **Storage**: Wasabi/S3 (opcional)

## 🔮 Próximas Funcionalidades

- [ ] **Fila de Processamento**: Django-Q ou Celery
- [ ] **Upload via FTP**: Importação automática de câmeras
- [ ] **Preview em Tempo Real**: WebSocket para progresso
- [ ] **Filtros Avançados**: Cor, brilho, contraste
- [ ] **Templates de Projeto**: Configurações predefinidas
- [ ] **API REST**: Para integrações externas
- [ ] **Mobile App**: React Native
- [ ] **Analytics**: Métricas de uso

## 🐛 Solução de Problemas

### FFmpeg não encontrado
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# Windows (com Chocolatey)
choco install ffmpeg

# macOS (com Homebrew)
brew install ffmpeg
```

### Erro de permissão na pasta media
```bash
# Linux
sudo chown -R www-data:www-data media/
sudo chmod -R 755 media/

# Ou para desenvolvimento
chmod -R 777 media/
```

### Vídeos não reproduzem
- Verifique se o codec H.264 está disponível
- Confirme que o FFmpeg está na versão 4.0+
- Teste com um frame individual primeiro

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🤝 Contribuições

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📞 Suporte

- **Documentação**: Este README
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

---

**⚡ EagleView** - Transformando sequências de imagens em timelapses profissionais com simplicidade e elegância. 
/*
dirty magic class ;)
*/
.somemagic {
    display: none;
}


.row1 {

}

.row2 {

}

.row3 {

}

.row5 {


}

 .ui-datepicker.ui-datepicker-inline {
   min-width: 350px !important;
   width: 100% !important;
 }

.row5:hover {
    background-color: #e1e1e1;
}


.ui-state-highlight {

    color: #000000 !important;

}

.ui-state-active {
    background: #2980b9 !important; 
    color: #FFFFFF !important;

}

.ui-state-hover {
    background: #ffcc33 !important; 
    color: #000000 !important;
}

.loginform label {	
    font-size: 20px; 
    color: #666; 
    padding-left: 4px;
}

.loginform { 

    border: 1px solid #ddd; 
    padding: 6px 6px 6px 12px; 
    background: #fff;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px; 		
    background: -webkit-gradient(linear, 0% 0%, 0% 40%, from(#EEE), to(#FFFFFF)); 
    background: -moz-linear-gradient(0% 40% 90deg,#FFF, #EEE); 
    -webkit-box-shadow:0px 0 50px #ccc;
    -moz-box-shadow:0px 0 50px #ccc; 
    box-shadow:0px 0 50px #ccc;	
    background-image: url('../../iconz/wolflogin.png');
    background-repeat: no-repeat;
    background-position: right;
}	

.loginform fieldset { border: none; }


.loginform input, textarea { 		
    padding: 4px; 
    margin: 4px 0 20px 0; 
    background: #fff; 
    color: #555; 
    border: 1px #ddd solid;
    -webkit-box-shadow: 0px 0px 4px #aaa;
    -moz-box-shadow: 0px 0px 4px #aaa; 
    box-shadow: 0px 0px 4px #aaa;
    -webkit-transition: background 0.3s linear;							
}

.loginform textarea {		

}

.loginform input:hover, textarea:hover { 
    background: #eee; 
} 

.loginform  legend
{
    float: left;
    color: #000000;
    border: 1px solid #C4C4C4;
    padding: 8px; 
    margin: 8px;
    width: 60%;
} 

.mgcontainer {
    height:350px; 
    width:256px; 
    border:1px #aaa solid; 
    float:left; 
    margin:5px;
    background-color: #fbf6f6;
}

.mgheader {
    height:32px; 
    width:100%;
    text-align: center;
    font-weight: bold;
    float: left;
    background-color: #fff298;
}

.mgheaderprimary {
    height:32px; 
    width:100%;
    text-align: center;
    font-weight: bold;
    float: left;
    background-color: #adc8ff;
}


.mgsubcontrol {
    background: #3498db;
    background-image: -webkit-linear-gradient(top, #3498db, #2980b9);
    background-image: -moz-linear-gradient(top, #3498db, #2980b9);
    background-image: -ms-linear-gradient(top, #3498db, #2980b9);
    background-image: -o-linear-gradient(top, #3498db, #2980b9);
    background-image: linear-gradient(to bottom, #3498db, #2980b9);
    -webkit-border-radius: 28;
    -moz-border-radius: 28;
    border-radius: 28px;
    font-family: Arial;
    color: #ffffff;

    padding: 10px 20px 10px 20px;
    text-decoration: none;
    margin: 20%;
}

.mgsubcontrol:hover {
    background: #3cb0fd;
    background-image: -webkit-linear-gradient(top, #3cb0fd, #3498db);
    background-image: -moz-linear-gradient(top, #3cb0fd, #3498db);
    background-image: -ms-linear-gradient(top, #3cb0fd, #3498db);
    background-image: -o-linear-gradient(top, #3cb0fd, #3498db);
    background-image: linear-gradient(to bottom, #3cb0fd, #3498db);
    text-decoration: none;

}


.mgunsubcontrol {
    background: #d40000;
    background-image: -webkit-linear-gradient(top, #d40000, #f76e4f);
    background-image: -moz-linear-gradient(top, #d40000, #f76e4f);
    background-image: -ms-linear-gradient(top, #d40000, #f76e4f);
    background-image: -o-linear-gradient(top, #d40000, #f76e4f);
    background-image: linear-gradient(to bottom, #d40000, #f76e4f);
    -webkit-border-radius: 28;
    -moz-border-radius: 28;
    border-radius: 28px;
    font-family: Arial;
    color: #ffffff;
    padding: 10px 20px 10px 20px;
    text-decoration: none;
    margin: 20%;
}

.mgunsubcontrol:hover {
    background: #3cb0fd;
    background-image: -webkit-linear-gradient(top, #d40000, #f76e4f);
    background-image: -moz-linear-gradient(top, #d40000, #f76e4f);
    background-image: -ms-linear-gradient(top, #d40000, #f76e4f);
    background-image: -o-linear-gradient(top, #d40000, #f76e4f);
    background-image: linear-gradient(to bottom, #d40000, #f76e4f);
    text-decoration: none;
}



.mgviewcontrol{
    background: #44d934;
    background-image: -webkit-linear-gradient(top, #44d934, #00943e);
    background-image: -moz-linear-gradient(top, #44d934, #00943e);
    background-image: -ms-linear-gradient(top, #44d934, #00943e);
    background-image: -o-linear-gradient(top, #44d934, #00943e);
    background-image: linear-gradient(to bottom, #44d934, #00943e);
    -webkit-border-radius: 28;
    -moz-border-radius: 28;
    border-radius: 28px;
    font-family: Arial;
    color: #ffffff;
    padding: 10px 20px 10px 20px;
    text-decoration: none;
    margin-left: 30%;
}

.mgviewcontrol:hover {
    background: #009116;
    background-image: -webkit-linear-gradient(top, #009116, #0ccf57);
    background-image: -moz-linear-gradient(top, #009116, #0ccf57);
    background-image: -ms-linear-gradient(top, #009116, #0ccf57);
    background-image: -o-linear-gradient(top, #009116, #0ccf57);
    background-image: linear-gradient(to bottom, #009116, #0ccf57);
    text-decoration: none;
}

.mgguidecontrol {
    -moz-box-shadow: 0px 1px 0px 0px #fff6af;
    -webkit-box-shadow: 0px 1px 0px 0px #fff6af;
    box-shadow: 0px 1px 0px 0px #fff6af;
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #ffec64), color-stop(1, #ffab23));
    background:-moz-linear-gradient(top, #ffec64 5%, #ffab23 100%);
    background:-webkit-linear-gradient(top, #ffec64 5%, #ffab23 100%);
    background:-o-linear-gradient(top, #ffec64 5%, #ffab23 100%);
    background:-ms-linear-gradient(top, #ffec64 5%, #ffab23 100%);
    background:linear-gradient(to bottom, #ffec64 5%, #ffab23 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffec64', endColorstr='#ffab23',GradientType=0);
    background-color:#ffec64;
    -moz-border-radius:6px;
    -webkit-border-radius:6px;
    border-radius:6px;
    border:1px solid #ffaa22;
    display:inline-block;
    cursor:pointer;
    color:#333333;
    font-family:Arial;
    font-weight:bold;
    padding:6px 24px;
    text-decoration:none;
    text-shadow:0px 1px 0px #ffee66;
}
.mgguidecontrol:hover {
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #ffab23), color-stop(1, #ffec64));
    background:-moz-linear-gradient(top, #ffab23 5%, #ffec64 100%);
    background:-webkit-linear-gradient(top, #ffab23 5%, #ffec64 100%);
    background:-o-linear-gradient(top, #ffab23 5%, #ffec64 100%);
    background:-ms-linear-gradient(top, #ffab23 5%, #ffec64 100%);
    background:linear-gradient(to bottom, #ffab23 5%, #ffec64 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffab23', endColorstr='#ffec64',GradientType=0);
    background-color:#ffab23;
}
.mgguidecontrol:active {
    position:relative;
    top:1px;
}



.trinity-col {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 992px){
    .trinity-col {
        width: 44.33333333%;
    }
}

@media (min-width: 992px){
    .trinity-col {
        float: left;
    }
}


.trinity-list {
    padding: 10px;
    font-size: 10pt;
}

.trinity-bl1 {
    margin-bottom: 15px;
    display: block;
    height: 340px;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, .2);
}

.trinity-bl1 p {

    padding: 5px;
    border-radius: 5px;
    color: white;
    background: #6cb121;
    margin: 0 auto;
    left: 0;
    bottom: 35px;
    text-transform: uppercase;
    right: 0;
    width: 75%;
}

.trinity-button {
    text-decoration: none !important;
    border-radius: 5px;
    display: block;
    float: left;
    color: white;
    background: #6cb121;
    margin: 10px auto;
    margin-left: 10px;
    left: 0;
    bottom: 35px;
    right: 0;
    padding: 10px 10px 10px 10px;
}

.trinity-button-s {
    text-decoration: none !important;
    display: block;
    border-radius: 5px;
    color: white;
    background: #6cb121;
    margin: 0 auto;
    left: 0;
    bottom: 35px;
    text-transform: uppercase;
    right: 0;
    width: 65%;
    padding: 10px 10px 10px 10px;
}

.trinity-button-u {
    text-decoration: none !important;
    display: block;
    border-radius: 5px;
    color: white;
    background: #a1a79c;
    margin: 0 auto;
    left: 0;
    bottom: 35px;
    text-transform: uppercase;
    right: 0;
    width: 65%;
    padding: 10px 10px 10px 10px;
}


.trinity-price {
    font-size: 35pt;
    padding-top: 20px;
    /* font-family: 'Myriad Pro Bold';*/
}

.trinity-green {
    margin-top: 10px;
    height: 40px;
    background: #6cb121;
    color: white;
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase;
    line-height: 40px;
}


.trinity-tariff-b s{
    background: rgba(85, 148, 27, 0.59);
}

.trinity-price sup {
    text-transform: uppercase;
    font-size: 15pt;
    /*font-family: 'Myriad Pro Regular';*/
}

sup {
    top: -0.5em;
}
sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}


.announcementstable {

}

.announcementslink {
    font-size: x-large;
}

.anreadbutton {
    -moz-box-shadow:inset 0px 1px 0px 0px #a4e271;
    -webkit-box-shadow:inset 0px 1px 0px 0px #a4e271;
    box-shadow:inset 0px 1px 0px 0px #a4e271;
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #89c403), color-stop(1, #77a809));
    background:-moz-linear-gradient(top, #89c403 5%, #77a809 100%);
    background:-webkit-linear-gradient(top, #89c403 5%, #77a809 100%);
    background:-o-linear-gradient(top, #89c403 5%, #77a809 100%);
    background:-ms-linear-gradient(top, #89c403 5%, #77a809 100%);
    background:linear-gradient(to bottom, #89c403 5%, #77a809 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#89c403', endColorstr='#77a809',GradientType=0);
    background-color:#89c403;
    -moz-border-radius:6px;
    -webkit-border-radius:6px;
    border-radius:6px;
    border:1px solid #74b807;
    display:inline-block;
    cursor:pointer;
    color:#ffffff !important;
    font-family:Arial;
    font-size:15px;
    font-weight:bold;
    padding:6px 24px;
    text-decoration:none;
    text-shadow:0px 1px 0px #528009;
}
.anreadbutton:hover {
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #77a809), color-stop(1, #89c403));
    background:-moz-linear-gradient(top, #77a809 5%, #89c403 100%);
    background:-webkit-linear-gradient(top, #77a809 5%, #89c403 100%);
    background:-o-linear-gradient(top, #77a809 5%, #89c403 100%);
    background:-ms-linear-gradient(top, #77a809 5%, #89c403 100%);
    background:linear-gradient(to bottom, #77a809 5%, #89c403 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#77a809', endColorstr='#89c403',GradientType=0);
    text-decoration: none !important;
    color: #ffffff !important;
    background-color:#77a809;
}

.anreadbutton:active {
    position:relative;
    top:1px;
}

.anunreadbutton {
    -moz-box-shadow:inset 0px 1px 0px 0px #f5978e;
    -webkit-box-shadow:inset 0px 1px 0px 0px #f5978e;
    box-shadow:inset 0px 1px 0px 0px #f5978e;
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #f24537), color-stop(1, #c62d1f));
    background:-moz-linear-gradient(top, #f24537 5%, #c62d1f 100%);
    background:-webkit-linear-gradient(top, #f24537 5%, #c62d1f 100%);
    background:-o-linear-gradient(top, #f24537 5%, #c62d1f 100%);
    background:-ms-linear-gradient(top, #f24537 5%, #c62d1f 100%);
    background:linear-gradient(to bottom, #f24537 5%, #c62d1f 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f24537', endColorstr='#c62d1f',GradientType=0);
    background-color:#f24537;
    -moz-border-radius:6px;
    -webkit-border-radius:6px;
    border-radius:6px;
    border:1px solid #d02718;
    display:inline-block;
    cursor:pointer;
    color:#ffffff !important;
    font-family:Arial;
    font-size:15px;
    font-weight:bold;
    padding:6px 24px;
    text-decoration:none;
    text-shadow:0px 1px 0px #810e05;
}
.anunreadbutton:hover {
    background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #c62d1f), color-stop(1, #f24537));
    background:-moz-linear-gradient(top, #c62d1f 5%, #f24537 100%);
    background:-webkit-linear-gradient(top, #c62d1f 5%, #f24537 100%);
    background:-o-linear-gradient(top, #c62d1f 5%, #f24537 100%);
    background:-ms-linear-gradient(top, #c62d1f 5%, #f24537 100%);
    background:linear-gradient(to bottom, #c62d1f 5%, #f24537 100%);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#c62d1f', endColorstr='#f24537',GradientType=0);
    text-decoration: none !important;
    color: #ffffff !important;
    background-color:#c62d1f;

}
.anunreadbutton:active {
    position:relative;
    top:1px;
}

.resp-table {
    word-break:break-all;
}


/* Alerts */

.alert_info {
    display: block;
    width: 95%;
    margin: 20px 3% 0 3%;
    margin-top: 20px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #B5E5EF url(../../icn_alert_info.png) no-repeat;
    background-position: 10px 10px;
    border: 1px solid #77BACE;
    color: #082B33;
    padding: 10px 0;
    text-indent: 40px;
    font-size: 14px;}

.alert_warning {
    display: block;
    width: 95%;
    margin: 20px 3% 0 3%;
    margin-top: 20px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #F5F3BA url(../../icn_alert_warning.png) no-repeat;
    background-position: 10px 10px;
    border: 1px solid #C7A20D;
    color: #796616;
    padding: 10px 0;
    text-indent: 40px;
    font-size: 14px;}

.alert_error {
    display: block;
    width: 95%;
    margin: 20px 3% 0 3%;
    margin-top: 20px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #F3D9D9 url(../../icn_alert_error.png) no-repeat;
    background-position: 10px 10px;
    border: 1px solid #D20009;
    color: #7B040F;
    padding: 10px 0;
    text-indent: 40px;
    font-size: 14px;}

.alert_success {
    display: block;
    width: 95%;
    margin: 20px 3% 0 3%;
    margin-top: 20px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #E2F6C5 url(../../icn_alert_success.png) no-repeat;
    background-position: 10px 10px;
    border: 1px solid #79C20D;
    color: #32510F;
    padding: 10px 0;
    text-indent: 40px;
    font-size: 14px;}


.ubButton {
    -moz-box-shadow:inset 0px 1px 0px 0px #ffffff;
    -webkit-box-shadow:inset 0px 1px 0px 0px #ffffff;
    box-shadow:inset 0px 1px 0px 0px #ffffff;
    background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #ededed), color-stop(1, #dfdfdf) );
    background:-moz-linear-gradient( center top, #ededed 5%, #dfdfdf 100% );
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ededed', endColorstr='#dfdfdf');
    background-color:#ededed;
    -moz-border-radius:6px;
    -webkit-border-radius:6px;
    border-radius:6px;
    border:1px solid #dcdcdc;
    display:inline-block;
    font-family:arial;
    font-size:15px;
    font-weight:bold;
    padding:6px 24px;
    text-decoration:none;
    text-shadow:1px 1px 0px #ffffff;
    color:#777777;
    margin: 0px 0px 4px 0px;
}

.ubButton:hover {
    background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #dfdfdf), color-stop(1, #ededed) );
    background:-moz-linear-gradient( center top, #dfdfdf 5%, #ededed 100% );
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dfdfdf', endColorstr='#ededed');
    background-color:#dfdfdf;
    text-decoration: none;
    color: #777777;
}

.ubButton:active {
    /* commented due android FF issues */
    /* position:relative;  
    top:1px; */
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.6);
}

.ubButton:visited {
    color: #777777;
}

.ubButton:link {
    color: #777777;
}


/* glamour forms =) */

.glamour label {	
    /* font-size: 20px; */
    color: #666; 
    padding-left: 4px;
}

.glamour { 
    float: left;
    border: 1px solid #ddd; 
    padding: 6px 6px 6px 12px; 
    background: #fff;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px; 		
    background: -webkit-gradient(linear, 0% 0%, 0% 40%, from(#EEE), to(#FFFFFF)); 
    /* following line commented due the FF 50.0 gradient issue */
    /* background: -moz-linear-gradient(0% 40% 90deg,#FFF, #EEE);  */
    -webkit-box-shadow:0px 0 50px #ccc;
    -moz-box-shadow:0px 0 50px #ccc; 
    box-shadow:0px 0 50px #ccc;		 		
}	

.glamour fieldset { border: none; }


.glamour textarea {		

}

.glamour input:hover, textarea:hover { 
    /** background: #eee; **/
} 

.glamour  legend
{
    float: left;
    color: #000000;
    /* background: #E0E0E0; */
    border: 1px solid #C4C4C4;
    padding: 8px; 
    margin: 8px;
    width: 60%;
} 

.fileeditorarea {
    background-color: #000000;
    color: #DEE3E7;
    font-size: larger;
    max-width: 95%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
}

.fileeditorarea:hover {
    background-color: #000000;
    color: #DEE3E7;
    font-size: larger;
}

.diff span{
    display:block;
    min-height:1.333em;
    margin-top:-1px;
    padding:0 3px;
}

.diffDeleted span{
    border:1px solid rgb(255,192,192);
    background:rgb(255,224,224);
}

.diffInserted span{
    border:1px solid rgb(192,255,192);
    background:rgb(224,255,224);
}

.confirmcancel {
    box-shadow:inset 0px 1px 0px 0px #f5978e;
    background:linear-gradient(to bottom, #f24537 5%, #c62d1f 100%);
    background-color:#f24537;
    border-radius:6px;
    border:1px solid #d02718;
    display:inline-block;
    cursor:pointer;
    color:#ffffff !important;
    font-family:Arial;
    font-size:15px;
    font-weight:bold;
    padding:6px 24px;
    text-decoration:none;
    text-shadow:0px 1px 0px #810e05;
}
.confirmcancel:hover {
    background:linear-gradient(to bottom, #c62d1f 5%, #f24537 100%);
    background-color:#c62d1f;
    color:#ffffff;
    text-decoration:none;
}
.confirmcancel:active {
    position:relative;
    top:1px;
    color:#ffffff;
}

.confirmagree {
    box-shadow:inset 0px 1px 0px 0px #a4e271;
    background:linear-gradient(to bottom, #89c403 5%, #77a809 100%);
    background-color:#89c403;
    border-radius:6px;
    border:1px solid #74b807;
    display:inline-block;
    cursor:pointer;
    color:#ffffff !important;
    font-family:Arial;
    font-size:15px;
    font-weight:bold;
    padding:6px 24px;
    text-decoration:none;
    text-shadow:0px 1px 0px #528009;
}
.confirmagree:hover {
    background:linear-gradient(to bottom, #77a809 5%, #89c403 100%);
    background-color:#77a809;
    color:#ffffff;
    text-decoration:none;
}
.confirmagree:active {
    position:relative;
    top:1px;
    color:#ffffff;
}

.dashboard{
    width:100%;
}

.dashtask {
    float: left;
    display: block;
    padding: 10px;
    margin: 5px;
    border:solid 1px #EEE;
    border-radius:10px;
    text-align: center;
    font-size: 8pt;
    overflow: hidden;
    box-shadow: 3px 3px 4px rgba(0,0,0,0);
    line-height: 100%;
}

.dashtask:hover {
    -webkit-box-shadow: 3px 3px 4px rgba(50, 50, 50, 0.75);
    -moz-box-shadow:    3px 3px 4px rgba(50, 50, 50, 0.75);
    box-shadow:         3px 3px 4px rgba(50, 50, 50, 0.75);

}


/* Paleta de cores moderna e profissional */
:root {
    --bg-primary: #0A0B0D;
    --bg-secondary: #11141A;
    --bg-tertiary: #1C1F26;
    --surface-primary: #161B22;
    --surface-secondary: #1C2128;
    --surface-tertiary: #262C36;
    --highlight-orange: #FF6A00;
    --highlight-orange-hover: #FF7F1A;
    --highlight-orange-light: rgba(255, 106, 0, 0.1);
    --highlight-blue: #0066CC;
    --highlight-blue-hover: #0052A3;
    --highlight-blue-light: rgba(0, 102, 204, 0.1);
    --text-primary: #F8FAFC;
    --text-secondary: #94A3B8;
    --text-muted: #64748B;
    --border-primary: #334155;
    --border-secondary: #1E293B;
    --border-tertiary: #475569;
    --success: #10B981;
    --success-light: rgba(16, 185, 129, 0.1);
    --warning: #F59E0B;
    --warning-light: rgba(245, 158, 11, 0.1);
    --danger: #EF4444;
    --danger-light: rgba(239, 68, 68, 0.1);
    --info: #3B82F6;
    --info-light: rgba(59, 130, 246, 0.1);
    --sidebar-width: 300px;
    --header-height: 70px;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
}

/* Importação de fontes */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Reset e base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(255, 106, 0, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 102, 204, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Melhorias na tipografia */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

small {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Layout wrapper */
.wrapper {
    display: flex;
    width: 100%;
    min-height: 100vh;
}

/* Sidebar ultra moderna */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: rgba(22, 27, 34, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    color: var(--text-primary);
    transition: var(--transition-smooth);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, 
        rgba(255, 106, 0, 0.02) 0%, 
        transparent 30%, 
        transparent 70%, 
        rgba(0, 102, 204, 0.02) 100%);
    pointer-events: none;
}

.sidebar-header {
    padding: 24px;
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header .logo {
    height: 42px;
    width: auto;
    filter: brightness(1.2) drop-shadow(0 2px 8px rgba(255, 106, 0, 0.3));
    transition: var(--transition-smooth);
}

.sidebar-header .logo:hover {
    transform: scale(1.05);
    filter: brightness(1.3) drop-shadow(0 4px 12px rgba(255, 106, 0, 0.4));
}

.sidebar-header h4 {
    color: var(--text-primary);
    font-weight: 800;
    font-size: 1.375rem;
    margin: 0;
    letter-spacing: -0.05em;
    background: linear-gradient(135deg, var(--highlight-orange), var(--highlight-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar .components {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar ul li {
    padding: 0;
    margin: 2px 8px;
}

.sidebar ul li a {
    padding: 14px 20px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 14px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-smooth);
    border-radius: var(--radius-md);
    position: relative;
    margin: 2px 12px;
    overflow: hidden;
}

.sidebar ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 106, 0, 0.1), transparent);
    transition: var(--transition-smooth);
}

.sidebar ul li a:hover::before {
    left: 100%;
}

.sidebar ul li a:hover {
    color: var(--text-primary);
    background: rgba(38, 44, 54, 0.6);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.sidebar ul li.active a {
    background: linear-gradient(135deg, var(--highlight-orange), var(--highlight-blue));
    color: var(--text-primary);
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(255, 106, 0, 0.4);
    transform: translateX(2px);
}

.sidebar ul li.active a::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 16px;
    width: 6px;
    height: 6px;
    background: var(--text-primary);
    border-radius: 50%;
    transform: translateY(-50%);
}

.sidebar ul li a i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

/* Sidebar footer aprimorado */
.sidebar-footer {
    padding: 24px;
    border-top: 1px solid var(--border-primary);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    position: relative;
    z-index: 1;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    padding: 8px 12px;
    margin: 8px -12px 0;
    border-radius: 6px;
    transition: all 0.2s;
    font-size: 0.875rem;
    font-weight: 500;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.logout-btn:hover,
.logout-btn:focus {
    color: var(--danger);
    background: rgba(218, 54, 51, 0.1);
    outline: none;
}

/* Content area */
#content {
    width: 100%;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--bg-primary);
}

/* Top header melhorado */
.top-header {
    background: var(--surface-primary);
    border-bottom: 1px solid var(--border-primary);
    padding: 16px 24px;
    height: var(--header-height);
    backdrop-filter: blur(8px);
    position: sticky;
    top: 0;
    z-index: 100;
}

.top-header .container-fluid {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Garantir que o botão hambúrguer seja sempre visível */
#sidebarCollapse {
    display: flex !important;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    z-index: 1000;
}

/* Breadcrumb melhorado */
.breadcrumb {
    background: none;
    margin: 0;
    padding: 0;
    font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted);
    content: "→";
}

.breadcrumb-item a {
    color: var(--highlight-blue);
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb-item a:hover {
    color: var(--highlight-orange);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 600;
}

/* Content area */
.content-area {
    padding: 24px;
}

/* Cards e Panels ultra modernos */
.card {
    background: rgba(28, 33, 40, 0.8);
    backdrop-filter: blur(10px) saturate(180%);
    -webkit-backdrop-filter: blur(10px) saturate(180%);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    color: var(--text-primary);
    transition: var(--transition-smooth);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--highlight-orange), var(--highlight-blue));
    opacity: 0;
    transition: var(--transition-smooth);
}

.card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
    border-color: var(--border-tertiary);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: linear-gradient(135deg, var(--surface-tertiary) 0%, var(--surface-secondary) 100%);
    border-bottom: 1px solid var(--border-primary);
    padding: 24px;
    position: relative;
}

.card-header h5, .card-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.card-body {
    padding: 24px;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Botões ultra modernos */
.btn {
    font-weight: 600;
    border-radius: var(--radius-md);
    padding: 12px 24px;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    line-height: 1.4;
    position: relative;
    overflow: hidden;
    background-clip: padding-box;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.btn:hover::before {
    left: 100%;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, var(--highlight-orange), var(--highlight-orange-hover));
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--highlight-orange-hover), #d14900);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-primary);
}

.btn-secondary {
    background: var(--surface-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
    background: var(--surface-secondary);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success);
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(35, 134, 54, 0.3);
}

.btn-success:hover {
    background: #1f7a2e;
    transform: translateY(-1px);
    color: var(--text-primary);
}

.btn-danger {
    background: var(--danger);
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(218, 54, 51, 0.3);
}

.btn-danger:hover {
    background: #c5302d;
    transform: translateY(-1px);
    color: var(--text-primary);
}

.btn-info {
    background: var(--info);
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(31, 111, 235, 0.3);
}

.btn-info:hover {
    background: #1c5ed6;
    transform: translateY(-1px);
    color: var(--text-primary);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1rem;
}

/* Forms melhorados */
.form-control {
    background: var(--surface-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 0.875rem;
    transition: all 0.2s;
    font-family: inherit;
}

.form-control:focus {
    border-color: var(--highlight-orange);
    box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.2);
    background: var(--surface-secondary);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.875rem;
    display: block;
}

.form-text {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 4px;
}

.form-select {
    background: var(--surface-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 0.875rem;
}

.form-select:focus {
    border-color: var(--highlight-orange);
    box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.2);
    outline: none;
}

/* Alerts melhorados */
.alert {
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 1rem;
    border: 1px solid;
    font-size: 0.875rem;
    line-height: 1.5;
}

.alert-success {
    background: rgba(35, 134, 54, 0.15);
    color: #3fb950;
    border-color: rgba(35, 134, 54, 0.3);
}

.alert-danger {
    background: rgba(218, 54, 51, 0.15);
    color: #f85149;
    border-color: rgba(218, 54, 51, 0.3);
}

.alert-warning {
    background: rgba(218, 139, 0, 0.15);
    color: #d29922;
    border-color: rgba(218, 139, 0, 0.3);
}

.alert-info {
    background: rgba(31, 111, 235, 0.15);
    color: #58a6ff;
    border-color: rgba(31, 111, 235, 0.3);
}

.alert-heading {
    color: inherit;
    font-weight: 600;
    margin-bottom: 8px;
}

/* Modals melhorados */
.modal-content {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.6);
}

.modal-header {
    background: var(--surface-tertiary);
    border-bottom: 1px solid var(--border-primary);
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    padding: 24px;
    color: var(--text-primary);
}

.modal-footer {
    background: var(--surface-tertiary);
    border-top: 1px solid var(--border-primary);
    padding: 16px 24px;
    border-radius: 0 0 12px 12px;
}

/* Tabelas melhoradas */
.table {
    color: var(--text-primary);
    border-collapse: collapse;
    width: 100%;
}

.table th,
.table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-primary);
    vertical-align: middle;
}

.table th {
    background: var(--surface-tertiary);
    color: var(--text-primary);
    font-weight: 600;
    border-top: none;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: background-color 0.2s;
}

.table tbody tr:hover {
    background: var(--surface-tertiary);
}

/* Galeria de frames melhorada */
.frame-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 24px;
}

.frame-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid var(--border-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--surface-secondary);
}

.frame-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
    border-color: var(--border-secondary);
}

.frame-item.selected {
    border-color: var(--highlight-orange);
    box-shadow: 0 8px 24px rgba(255, 106, 0, 0.3);
}

.frame-item img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
    transition: transform 0.3s;
}

.frame-item:hover img {
    transform: scale(1.05);
}

.frame-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: end;
}

.frame-info {
    flex: 1;
}

.frame-filename {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.frame-order {
    font-size: 0.75rem;
    opacity: 0.8;
}

.frame-checkbox {
    opacity: 0;
    transition: opacity 0.3s ease;
    transform: scale(1.2);
}

.frame-item:hover .frame-checkbox,
.frame-item.selected .frame-checkbox {
    opacity: 1;
}

/* Player de vídeo */
.video-player {
    background: var(--surface-secondary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.video-player video {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
}

/* Controles de timeline */
.timeline-controls {
    background: var(--surface-tertiary);
    padding: 20px;
    border-radius: 12px;
    margin-top: 20px;
    border: 1px solid var(--border-primary);
}

.timeline-controls h5 {
    color: var(--text-primary);
    margin-bottom: 16px;
    font-weight: 600;
}

.range-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: var(--surface-secondary);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.range-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--highlight-orange);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255, 106, 0, 0.3);
}

.range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--highlight-orange);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(255, 106, 0, 0.3);
}

/* Drop area melhorada */
.drop-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drop-area.drag-active {
    border-color: #0d6efd;
    background: #e7f1ff;
}

.upload-icon {
    font-size: 48px;
    color: #0d6efd;
    margin-bottom: 15px;
}

.upload-tip {
    padding: 10px;
    border-radius: 6px;
    background: #f8f9fa;
}

.upload-tip i {
    font-size: 24px;
    display: block;
    margin-bottom: 5px;
}

#upload-btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* Animação de loading */
.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast notifications */
.toast {
    background: var(--surface-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    color: var(--text-primary);
}

.toast-header {
    background: var(--surface-tertiary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

/* Menu Hambúrguer Moderno */
#sidebarCollapse {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    padding: 12px 16px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

#sidebarCollapse::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 106, 0, 0.2), transparent);
    transition: left 0.5s ease;
}

#sidebarCollapse:hover {
    background: linear-gradient(135deg, rgba(255, 106, 0, 0.2), rgba(255, 106, 0, 0.1));
    border-color: var(--highlight-orange);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 106, 0, 0.3);
}

#sidebarCollapse:hover::before {
    left: 100%;
}

#sidebarCollapse:active {
    transform: scale(0.95);
}

#sidebarCollapse i {
    font-size: 1.125rem;
    transition: transform 0.3s ease;
}

#sidebarCollapse:hover i {
    transform: rotate(90deg);
}

/* Responsive design melhorado */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 1050; /* Acima de modals */
        box-shadow: none;
    }

    .sidebar.active {
        transform: translateX(0);
        box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
    }

    #content {
        margin-left: 0;
        width: 100%;
    }
    
    /* Overlay para fechar menu em mobile */
    .sidebar.active::after {
        content: '';
        position: fixed;
        top: 0;
        left: var(--sidebar-width);
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
        backdrop-filter: blur(2px);
    }
}

@media (max-width: 576px) {
    .frame-gallery {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.8rem;
    }
}

/* Sidebar collapsed state */
.sidebar.collapsed {
    min-width: 70px;
    max-width: 70px;
}

.sidebar.collapsed .sidebar-header h4,
.sidebar.collapsed .components ul li a span {
    display: none;
}

.sidebar.collapsed ~ #content {
    margin-left: 70px;
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--highlight-orange);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scrollbar customizado */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--surface-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-primary);
}

/* Melhorias adicionais de visibilidade */
.text-white,
.text-light {
    color: var(--text-primary) !important;
}

.text-muted {
    color: var(--text-secondary) !important;
}

.bg-dark {
    background-color: var(--surface-secondary) !important;
}

.border-secondary {
    border-color: var(--border-primary) !important;
}

/* Melhorias específicas para formulários */
.form-control:focus,
.form-select:focus {
    border-color: var(--highlight-orange) !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 106, 0, 0.25) !important;
}

/* Melhorias para dropdowns */
.dropdown-menu {
    background-color: var(--surface-secondary);
    border: 1px solid var(--border-primary);
}

.dropdown-item {
    color: var(--text-primary);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
}

/* Melhorias para modals */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Melhorias para badges e etiquetas */
.badge {
    font-weight: 500;
}

.badge.bg-primary {
    background-color: var(--highlight-orange) !important;
}

.badge.bg-secondary {
    background-color: var(--surface-tertiary) !important;
}

.badge.bg-success {
    background-color: var(--success) !important;
}

.badge.bg-danger {
    background-color: var(--danger) !important;
}

.badge.bg-warning {
    background-color: var(--warning) !important;
    color: var(--bg-primary) !important;
}

.badge.bg-info {
    background-color: var(--info) !important;
}

/* Melhorias para lista de itens */
.list-group {
    --bs-list-group-bg: var(--surface-secondary);
    --bs-list-group-border-color: var(--border-primary);
}

.list-group-item {
    background-color: var(--surface-secondary);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

.list-group-item:hover {
    background-color: var(--surface-tertiary);
}

/* Melhorias para paginação */
.pagination {
    --bs-pagination-bg: var(--surface-secondary);
    --bs-pagination-border-color: var(--border-primary);
    --bs-pagination-color: var(--text-primary);
    --bs-pagination-hover-bg: var(--surface-tertiary);
    --bs-pagination-hover-border-color: var(--border-primary);
    --bs-pagination-active-bg: var(--highlight-orange);
    --bs-pagination-active-border-color: var(--highlight-orange);
}

/* Melhorias para tooltips */
.tooltip .tooltip-inner {
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
    font-weight: 500;
}

/* Melhorias para popovers */
.popover {
    --bs-popover-bg: var(--surface-secondary);
    --bs-popover-border-color: var(--border-primary);
}

.popover-body {
    color: var(--text-primary);
}

/* Melhorias para tabs */
.nav-tabs {
    --bs-nav-tabs-border-color: var(--border-primary);
    --bs-nav-tabs-link-color: var(--text-secondary);
    --bs-nav-tabs-link-hover-border-color: var(--border-primary);
    --bs-nav-tabs-link-active-color: var(--text-primary);
    --bs-nav-tabs-link-active-bg: var(--surface-secondary);
    --bs-nav-tabs-link-active-border-color: var(--border-primary);
}

/* Melhorias para contraste geral */
strong, b {
    color: var(--text-primary);
    font-weight: 600;
}

em, i {
    color: var(--text-primary);
}

code {
    background-color: var(--surface-tertiary);
    color: var(--highlight-orange);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875em;
}

pre {
    background-color: var(--surface-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px;
}

/* Melhorias para seleção de texto */
::selection {
    background-color: rgba(255, 106, 0, 0.3);
    color: var(--text-primary);
}

::-moz-selection {
    background-color: rgba(255, 106, 0, 0.3);
    color: var(--text-primary);
}

/* Animações adicionais para UX moderna */
/* Removed pulse animation for cleaner design */

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

/* Classes utilitárias para animações */
.animate-in {
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.shimmer {
    background: linear-gradient(90deg,
        var(--surface-secondary) 25%,
        var(--surface-tertiary) 50%,
        var(--surface-secondary) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Melhorias na galeria de frames */
.frame-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.frame-item {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--surface-tertiary);
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-sm);
}

.frame-item:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Melhorias no header superior */
.top-header {
    background: rgba(22, 27, 34, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid var(--border-primary);
    padding: 16px 24px;
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-md);
}

/* Melhorias nos alertas */
.alert {
    border-radius: var(--radius-md);
    border: none;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-success::before {
    background: var(--success);
}

.alert-danger::before {
    background: var(--danger);
}

.alert-warning::before {
    background: var(--warning);
}

.alert-info::before {
    background: var(--info);
}

/* Melhorias nos formulários */
.form-control, .form-select {
    background: rgba(38, 44, 54, 0.6);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    transition: var(--transition-smooth);
}

.form-control:focus, .form-select:focus {
    background: rgba(38, 44, 54, 0.8);
    border-color: var(--highlight-orange);
    box-shadow: 0 0 0 0.25rem rgba(255, 106, 0, 0.15);
}

/* Estilo para drag and drop area melhorado */
.drop-area {
    border: 2px dashed var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 40px;
    text-align: center;
    transition: var(--transition-smooth);
    background: rgba(38, 44, 54, 0.3);
}

.drop-area.dragover {
    border-color: var(--highlight-orange);
    background: var(--highlight-orange-light);
    transform: scale(1.02);
}

/* Melhorias nas tabelas */
.table {
    background: transparent;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.table th {
    background: var(--surface-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
    font-weight: 600;
}

.table td {
    border-color: var(--border-secondary);
    color: var(--text-primary);
}

/* Estilos específicos para cards de estatísticas - Design Limpo */
.stat-card {
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    background: var(--surface-secondary);
    border-radius: var(--radius-md);
    transition: var(--transition-smooth);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: var(--border-tertiary);
}

.stat-icon {
    position: relative;
    margin-bottom: 1rem;
}

.icon-circle {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    border: 1px solid var(--border-primary);
}

.bg-gradient-primary {
    background: var(--highlight-blue);
    border-color: var(--highlight-blue);
}

.bg-gradient-primary i {
    color: white;
}

.bg-gradient-info {
    background: var(--info);
    border-color: var(--info);
}

.bg-gradient-info i {
    color: white;
}

.bg-gradient-orange {
    background: var(--highlight-orange);
    border-color: var(--highlight-orange);
}

.bg-gradient-orange i {
    color: white;
}

/* Removed icon-circle::before for cleaner design */

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0.5rem;
}

/* Removed quick-actions-card specific styles for cleaner design */

/* Melhorias para cards de projetos recentes */
.project-card {
    transition: var(--transition-smooth);
    border: 1px solid var(--border-primary);
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-tertiary);
}

.project-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-sm);
}

.project-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 106, 0, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: var(--transition-smooth);
}

.project-card:hover .project-thumbnail::after {
    opacity: 1;
}

/* Melhorias para seção de dicas */
.tips-card {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border: 1px solid var(--border-primary);
    position: relative;
}

.tips-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--warning), var(--highlight-orange));
}

.tip-item {
    transition: var(--transition-smooth);
    padding: 20px;
    border-radius: var(--radius-md);
}

.tip-item:hover {
    background: rgba(255, 106, 0, 0.05);
    transform: translateX(4px);
}

.tip-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--highlight-orange), var(--warning));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

/* Botões de ação especiais */
.action-btn {
    min-height: 120px;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

/* Melhorias responsivas */
@media (max-width: 992px) {
    .stat-card .stat-number {
        font-size: 2rem;
    }
    
    .icon-circle {
        width: 60px;
        height: 60px;
    }
    
    .action-btn {
        min-height: 100px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    #content {
        margin-left: 0;
    }
    
    .stat-trend {
        position: static;
        transform: none;
        width: 100%;
        margin-top: 16px;
    }
}

/* Melhorias para estados de loading */
.loading-card {
    position: relative;
}

.loading-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 25%, 
        rgba(255, 255, 255, 0.05) 50%, 
        transparent 75%);
    animation: shimmer 2s infinite;
}

/* Melhorias para vídeos de preview */
.video-preview {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.video-preview::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.video-preview:hover::after {
    opacity: 1;
}

/* Estilos específicos para templates melhorados */

/* Cards de busca */
.search-card {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border: 1px solid var(--border-primary);
    position: relative;
}

.search-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--info), var(--highlight-blue));
}

/* Cards de criação */
.create-card {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border: 1px solid var(--border-primary);
    position: relative;
}

.create-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--success), var(--highlight-orange));
}

/* Cards de projetos recentes */
.recent-card {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border: 1px solid var(--border-primary);
    position: relative;
}

.recent-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--info), var(--highlight-blue));
}

.recent-project-item {
    background: rgba(38, 44, 54, 0.4);
    border: 1px solid var(--border-primary);
    transition: var(--transition-smooth);
    text-decoration: none;
    color: var(--text-primary);
}

.recent-project-item:hover {
    background: rgba(38, 44, 54, 0.8);
    border-color: var(--border-tertiary);
    transform: translateX(4px);
    color: var(--text-primary);
    text-decoration: none;
}

.project-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 106, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Cards de upload */
.upload-card {
    background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
    border: 1px solid var(--border-primary);
    position: relative;
}

.upload-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--highlight-orange), var(--warning));
}

.upload-icon {
    /* Removed float animation */
}

.upload-tip {
    padding: 16px;
    border-radius: var(--radius-sm);
    background: rgba(0, 0, 0, 0.2);
    transition: var(--transition-smooth);
}

.upload-tip:hover {
    background: rgba(255, 106, 0, 0.1);
    transform: translateY(-2px);
}

/* Área de drag and drop melhorada */
.drop-area {
    border: 2px dashed var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 40px;
    text-align: center;
    transition: var(--transition-smooth);
    background: rgba(38, 44, 54, 0.3);
    position: relative;
    overflow: hidden;
}

.drop-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 106, 0, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-smooth);
}

.drop-area:hover::before,
.drop-area.dragover::before {
    opacity: 1;
}

.drop-area.dragover {
    border-color: var(--highlight-orange);
    background: var(--highlight-orange-light);
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Badges melhorados */
.badge {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: var(--radius-sm);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--surface-tertiary), var(--surface-secondary));
}

/* Melhorias nos formulários */
.form-control-lg {
    border-radius: var(--radius-md);
    border: 2px solid var(--border-primary);
    background: rgba(38, 44, 54, 0.6);
    color: var(--text-primary);
    transition: var(--transition-smooth);
}

.form-control-lg:focus {
    border-color: var(--highlight-orange);
    box-shadow: 0 0 0 0.25rem rgba(255, 106, 0, 0.15);
    background: rgba(38, 44, 54, 0.8);
}

/* Input group melhorado */
.input-group-lg .input-group-text {
    background: rgba(38, 44, 54, 0.8);
    border: 2px solid var(--border-primary);
    border-right: none;
    color: var(--text-secondary);
}

/* Progress bar melhorada */
.progress {
    background: rgba(38, 44, 54, 0.6);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--highlight-orange), var(--highlight-blue));
    transition: width 0.3s ease;
}

/* Melhorias de responsividade específicas */
@media (max-width: 768px) {
    .upload-card .card-body {
        padding: 16px;
    }
    
    .drop-area {
        padding: 24px 16px;
    }
    
    .upload-icon i {
        font-size: 2.5rem !important;
    }
    
    .recent-project-item {
        padding: 16px !important;
    }
}

/* Estados de loading específicos */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(255, 106, 0, 0.3);
    border-top: 4px solid var(--highlight-orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Melhorias específicas para o menu hambúrguer */
@media (max-width: 768px) {
    body.sidebar-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100vh;
    }
    
    /* Melhorar visibilidade do botão hambúrguer em mobile */
    #sidebarCollapse {
        border: 2px solid rgba(255, 106, 0, 0.4) !important;
        background: linear-gradient(135deg, rgba(255, 106, 0, 0.15), rgba(255, 106, 0, 0.1)) !important;
        box-shadow: 0 4px 12px rgba(255, 106, 0, 0.2) !important;
    }
    
    #sidebarCollapse:hover {
        border-color: var(--highlight-orange) !important;
        background: linear-gradient(135deg, rgba(255, 106, 0, 0.25), rgba(255, 106, 0, 0.15)) !important;
        box-shadow: 0 6px 20px rgba(255, 106, 0, 0.4) !important;
    }
    
    /* Garantir que a sidebar tenha prioridade máxima */
    .sidebar {
        z-index: 1055 !important;
    }
    
    .sidebar.active {
        box-shadow: 0 0 100px rgba(0, 0, 0, 0.8) !important;
    }
}

/* Melhorias para desktop */
@media (min-width: 769px) {
    /* Sidebar sempre visível em desktop, mas sem override forçado */
    .sidebar {
        transform: translateX(0);
        position: fixed;
    }
    
    /* Permitir colapso em desktop se necessário */
    .sidebar.collapsed {
        transform: translateX(-100%);
    }
    
    /* Efeito hover sutil no botão hambúrguer em desktop */
    #sidebarCollapse:hover {
        transform: scale(1.1);
    }
}

/* Garantir transições suaves em todas as resoluções */
.sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

#sidebarCollapse {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Estado de focus melhorado para acessibilidade */
#sidebarCollapse:focus {
    outline: 3px solid rgba(255, 106, 0, 0.5) !important;
    outline-offset: 2px;
} 
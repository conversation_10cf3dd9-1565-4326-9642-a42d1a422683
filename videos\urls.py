from django.urls import path
from . import views

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('projetos/', views.timelapse_list, name='timelapse_list'),
    path('projetos/<int:pk>/', views.timelapse_detail, name='timelapse_detail'),
    path('editor/', views.frame_editor, name='frame_editor'),
    path('editor/<int:timelapse_id>/', views.frame_editor, name='frame_editor_with_id'),
    
    # Navegação de diretórios do servidor (apenas admin)
    path('diretorios/', views.server_directory_browse, name='server_directory_list'),
    path('diretorios/<int:directory_id>/', views.server_directory_browse, name='server_directory_browse'),
    path('diretorios/<int:directory_id>/<path:path>/', views.server_directory_browse, name='server_directory_browse_path'),
    path('api/criar-timelapse/', views.create_timelapse_from_directory, name='create_timelapse_from_directory'),
    
    # APIs existentes
    path('api/job-status/<int:job_id>/', views.job_status, name='job_status'),
    path('api/delete/<int:pk>/', views.delete_timelapse, name='delete_timelapse'),
    path('api/preview/<int:timelapse_id>/', views.preview_timelapse, name='preview_timelapse'),
    path('api/notifications/', views.notifications_status, name='notifications_status'),
    
    # URLs para configurações de servidor
    path('configuracoes/', views.server_settings, name='server_settings'),
    path('configuracoes/novo/', views.server_create, name='server_create'),
    path('configuracoes/<int:server_id>/editar/', views.server_edit, name='server_edit'),
    path('configuracoes/<int:server_id>/deletar/', views.server_delete, name='server_delete'),
    path('configuracoes/<int:server_id>/testar/', views.server_test, name='server_test'),
    path('configuracoes/<int:server_id>/navegar/', views.server_browse, name='server_browse'),
    path('configuracoes/<int:server_id>/importar/', views.server_import_images, name='server_import_images'),
    
    # APIs para servidor
    path('api/servidor/<int:server_id>/testar/', views.server_api_test, name='server_api_test'),
    path('api/servidor/<int:server_id>/imagens/', views.server_api_list_images, name='server_api_list_images'),
] 
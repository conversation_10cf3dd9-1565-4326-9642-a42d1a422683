'use client';
import { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../layout';
import type { AuthContextType } from '../layout';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import { dbService } from '@/services/db.service';
import { Button } from '@/app/components/Button';
import { Loader2, Save } from 'lucide-react';

interface SettingsForm {
  system: {
    defaultProtocol: string;
    reconnectAttempts: number;
    reconnectInterval: number;
    streamQuality: string;
    maxCameras: number;
    maintenance: {
      backupEnabled: boolean;
      backupInterval: string;
      retentionDays: number;
    };
  };
  security: {
    passwordPolicy: {
      minLength: number;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      requireUppercase: boolean;
    };
    session: {
      timeout: number;
      maxAttempts: number;
      lockDuration: number;
    };
  };
  notifications: {
    email: {
      enabled: boolean;
      server: string;
      port: number;
      username: string;
    };
    telegram: {
      enabled: boolean;
      botToken: string;
      chatId: string;
    };
  };
  storage: {
    type: string;
    path: string;
    maxSize: string;
    autoCleanup: boolean;
    retentionDays: number;
  };
}

const SettingsPage = () => {
  const auth = useContext(AuthContext) as AuthContextType;
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('system');
  const [showSuccess, setShowSuccess] = useState(false);
  const [settings, setSettings] = useState<SettingsForm>({
    system: {
      defaultProtocol: 'flv',
      reconnectAttempts: 3,
      reconnectInterval: 2000,
      streamQuality: 'high',
      maxCameras: 10,
      maintenance: {
        backupEnabled: true,
        backupInterval: '24h',
        retentionDays: 30
      }
    },
    security: {
      passwordPolicy: {
        minLength: 8,
        requireNumbers: true,
        requireSpecialChars: true,
        requireUppercase: true
      },
      session: {
        timeout: 30,
        maxAttempts: 3,
        lockDuration: 15
      }
    },
    notifications: {
      email: {
        enabled: true,
        server: 'smtp.example.com',
        port: 587,
        username: '<EMAIL>'
      },
      telegram: {
        enabled: false,
        botToken: '',
        chatId: ''
      }
    },
    storage: {
      type: 'local',
      path: './recordings',
      maxSize: '500GB',
      autoCleanup: true,
      retentionDays: 30
    }
  });

  useEffect(() => {
    if (!auth.isAuthenticated) {
      router.push('/login');
      return;
    }

    // Verificar se o usuário tem permissão de admin
    if (auth.user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    const loadSettings = async () => {
      try {
        setIsLoading(true);
        const settingsData = await dbService.getSettings();
        setSettings(settingsData as SettingsForm);
      } catch (error) {
        console.error('Erro ao carregar configurações:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [auth.isAuthenticated, auth.user?.role, router]);

  const handleChange = (section: string, subSection: string | null, field: string, value: any) => {
    setSettings(prevSettings => {
      const newSettings = { ...prevSettings };
      
      if (subSection) {
        // @ts-ignore
        newSettings[section][subSection][field] = value;
      } else {
        // @ts-ignore
        newSettings[section][field] = value;
      }
      
      return newSettings;
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await dbService.updateSettings(settings);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (!auth.isAuthenticated || auth.user?.role !== 'admin') return null;

  const renderSystemTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Configurações de Streaming</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Protocolo Padrão</label>
            <select 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.defaultProtocol}
              onChange={(e) => handleChange('system', null, 'defaultProtocol', e.target.value)}
            >
              <option value="rtsp">RTSP</option>
              <option value="flv">FLV</option>
              <option value="hls">HLS</option>
              <option value="webrtc">WebRTC</option>
              <option value="mjpeg">MJPEG</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Qualidade do Stream</label>
            <select 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.streamQuality}
              onChange={(e) => handleChange('system', null, 'streamQuality', e.target.value)}
            >
              <option value="low">Baixa</option>
              <option value="medium">Média</option>
              <option value="high">Alta</option>
              <option value="ultra">Ultra</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tentativas de Reconexão</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.reconnectAttempts}
              onChange={(e) => handleChange('system', null, 'reconnectAttempts', parseInt(e.target.value))}
              min="1"
              max="10"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Intervalo de Reconexão (ms)</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.reconnectInterval}
              onChange={(e) => handleChange('system', null, 'reconnectInterval', parseInt(e.target.value))}
              min="1000"
              max="10000"
              step="500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Número Máximo de Câmeras</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.maxCameras}
              onChange={(e) => handleChange('system', null, 'maxCameras', parseInt(e.target.value))}
              min="1"
              max="100"
            />
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Manutenção do Sistema</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.system.maintenance.backupEnabled}
                onChange={(e) => handleChange('system', 'maintenance', 'backupEnabled', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Habilitar backup automático</span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Intervalo de Backup</label>
            <select 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.maintenance.backupInterval}
              onChange={(e) => handleChange('system', 'maintenance', 'backupInterval', e.target.value)}
              disabled={!settings.system.maintenance.backupEnabled}
            >
              <option value="6h">6 horas</option>
              <option value="12h">12 horas</option>
              <option value="24h">24 horas</option>
              <option value="48h">48 horas</option>
              <option value="7d">7 dias</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Dias de Retenção</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.system.maintenance.retentionDays}
              onChange={(e) => handleChange('system', 'maintenance', 'retentionDays', parseInt(e.target.value))}
              min="1"
              max="365"
              disabled={!settings.system.maintenance.backupEnabled}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Política de Senhas</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tamanho Mínimo</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.security.passwordPolicy.minLength}
              onChange={(e) => handleChange('security', 'passwordPolicy', 'minLength', parseInt(e.target.value))}
              min="6"
              max="20"
            />
          </div>
          
          <div className="md:col-span-2 space-y-2">
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.security.passwordPolicy.requireNumbers}
                onChange={(e) => handleChange('security', 'passwordPolicy', 'requireNumbers', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Exigir números</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.security.passwordPolicy.requireSpecialChars}
                onChange={(e) => handleChange('security', 'passwordPolicy', 'requireSpecialChars', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Exigir caracteres especiais</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.security.passwordPolicy.requireUppercase}
                onChange={(e) => handleChange('security', 'passwordPolicy', 'requireUppercase', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Exigir letras maiúsculas</span>
            </label>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Configurações de Sessão</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tempo Limite de Sessão (minutos)</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.security.session.timeout}
              onChange={(e) => handleChange('security', 'session', 'timeout', parseInt(e.target.value))}
              min="5"
              max="240"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tentativas Máximas de Login</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.security.session.maxAttempts}
              onChange={(e) => handleChange('security', 'session', 'maxAttempts', parseInt(e.target.value))}
              min="1"
              max="10"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Duração do Bloqueio (minutos)</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.security.session.lockDuration}
              onChange={(e) => handleChange('security', 'session', 'lockDuration', parseInt(e.target.value))}
              min="1"
              max="60"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Configurações de Email</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.notifications.email.enabled}
                onChange={(e) => handleChange('notifications', 'email', 'enabled', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Habilitar notificações por email</span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Servidor SMTP</label>
            <input 
              type="text" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.notifications.email.server}
              onChange={(e) => handleChange('notifications', 'email', 'server', e.target.value)}
              disabled={!settings.notifications.email.enabled}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Porta</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.notifications.email.port}
              onChange={(e) => handleChange('notifications', 'email', 'port', parseInt(e.target.value))}
              disabled={!settings.notifications.email.enabled}
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">Usuário</label>
            <input 
              type="text" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.notifications.email.username}
              onChange={(e) => handleChange('notifications', 'email', 'username', e.target.value)}
              disabled={!settings.notifications.email.enabled}
            />
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Configurações do Telegram</h3>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.notifications.telegram.enabled}
                onChange={(e) => handleChange('notifications', 'telegram', 'enabled', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Habilitar notificações via Telegram</span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Token do Bot</label>
            <input 
              type="text" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.notifications.telegram.botToken}
              onChange={(e) => handleChange('notifications', 'telegram', 'botToken', e.target.value)}
              disabled={!settings.notifications.telegram.enabled}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">ID do Chat</label>
            <input 
              type="text" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.notifications.telegram.chatId}
              onChange={(e) => handleChange('notifications', 'telegram', 'chatId', e.target.value)}
              disabled={!settings.notifications.telegram.enabled}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderStorageTab = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Configurações de Armazenamento</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Armazenamento</label>
            <select 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.storage.type}
              onChange={(e) => handleChange('storage', null, 'type', e.target.value)}
            >
              <option value="local">Local</option>
              <option value="s3">Amazon S3</option>
              <option value="nfs">NFS</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Caminho de Armazenamento</label>
            <input 
              type="text" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.storage.path}
              onChange={(e) => handleChange('storage', null, 'path', e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tamanho Máximo</label>
            <input 
              type="text" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.storage.maxSize}
              onChange={(e) => handleChange('storage', null, 'maxSize', e.target.value)}
              placeholder="Ex: 500GB, 1TB"
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={settings.storage.autoCleanup}
                onChange={(e) => handleChange('storage', null, 'autoCleanup', e.target.checked)}
              />
              <span className="text-sm font-medium text-gray-700">Limpeza automática</span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Dias de Retenção</label>
            <input 
              type="number" 
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={settings.storage.retentionDays}
              onChange={(e) => handleChange('storage', null, 'retentionDays', parseInt(e.target.value))}
              min="1"
              max="365"
              disabled={!settings.storage.autoCleanup}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'system':
        return renderSystemTab();
      case 'security':
        return renderSecurityTab();
      case 'notifications':
        return renderNotificationsTab();
      case 'storage':
        return renderStorageTab();
      default:
        return renderSystemTab();
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Configurações do Sistema</h1>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="border-b border-gray-200">
              <nav className="flex justify-center md:justify-start -mb-px overflow-x-auto" aria-label="Tabs">
                <button
                  onClick={() => setActiveTab('system')}
                  className={`whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm ${
                    activeTab === 'system'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Sistema
                </button>
                <button
                  onClick={() => setActiveTab('security')}
                  className={`whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm ${
                    activeTab === 'security'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Segurança
                </button>
                <button
                  onClick={() => setActiveTab('notifications')}
                  className={`whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm ${
                    activeTab === 'notifications'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Notificações
                </button>
                <button
                  onClick={() => setActiveTab('storage')}
                  className={`whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm ${
                    activeTab === 'storage'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Armazenamento
                </button>
              </nav>
            </div>

            <div className="p-6">
              {showSuccess && (
                <div className="mb-4 bg-green-100 border border-green-200 text-green-700 px-4 py-3 rounded relative" role="alert">
                  <span className="block sm:inline">Configurações salvas com sucesso!</span>
                </div>
              )}

              {renderTabContent()}

              <div className="mt-8 flex justify-end">
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  isLoading={isSaving}
                  leftIcon={!isSaving && <Save className="h-4 w-4" />}
                  className="bg-blue-600 text-white"
                >
                  {isSaving ? 'Salvando...' : 'Salvar Configurações'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default SettingsPage; 
# Fluxo de Usuários - EagleView Django

## Tipos de Usuários

### 1. Usuário Administrador
- **Acesso**: `/admin`, navegação de diretórios e todas as funcionalidades do sistema
- **Credenciais Atuais**:
  - Usuário: douglas
  - Senha: 18031999
- **Permissões**:
  - Acesso total ao painel administrativo Django
  - Gerenciamento de usuários e grupos
  - Configuração de diretórios do servidor (`/diretorios/`)
  - Navegação e seleção de imagens do servidor
  - Criação de timelapses a partir de imagens do servidor
  - Gerenciamento de permissões de acesso aos vídeos
  - Monitoramento e logs do sistema
  - Gerenciamento de conexões com servidores

### 2. Usuário Comum
- **Acesso**: Interface principal do sistema
- **Funcionalidades**:
  - Visualização do dashboard
  - Acesso a timelapses do seu grupo ou com permissão específica
  - Visualização de vídeos gerados
  - Download de vídeos (se autorizado)
  - Gerenciamento do próprio perfil

## Fluxo de Navegação

### 1. Página Inicial
- URL: `/`
- Acesso público
- Opções:
  - Login
  - Redefinição de senha (se implementado)

### 2. Login
- URL: `/login`
- Campos:
  - Usuário
  - Senha
- Redirecionamentos:
  - Sucesso: Dashboard
  - Falha: Mensagem de erro

### 3. Dashboard
- URL: `/dashboard`
- Requer autenticação
- Exibe:
  - Visão geral do sistema
  - Estatísticas dos timelapses
  - Ações rápidas

### 4. Lista de Projetos (Timelapses)
- URL: `/projetos/`
- Requer autenticação
- Funcionalidades:
  - Listagem de timelapses com base nas permissões do usuário
  - Busca por título ou criador
  - Acesso aos detalhes do projeto

### 5. Detalhes do Projeto
- URL: `/projetos/<id>/`
- Requer autenticação
- Funcionalidades:
  - Reprodução do vídeo gerado
  - Download do vídeo
  - Informações detalhadas do projeto
  - Gerenciamento de permissões (apenas admin)

## Fluxo de Administração

### 1. Painel Administrativo
- URL: `/admin`
- Acesso exclusivo para administradores
- Funcionalidades:
  - Gerenciamento de usuários e grupos
  - Configuração de diretórios do servidor
  - Configurações do sistema
  - Logs e monitoramento

### 2. Navegação de Diretórios (Nova Funcionalidade)
- URL: `/diretorios/`
- Acesso exclusivo para administradores
- Funcionalidades:
  - Listagem de diretórios configurados
  - Navegação hierárquica de pastas
  - Visualização de imagens disponíveis
  - Seleção múltipla de imagens
  - Criação de timelapses diretamente do servidor

### 3. Criação de Timelapse a partir do Servidor
- Processo:
  1. Navegar para `/diretorios/`
  2. Selecionar diretório configurado
  3. Navegar pelas subpastas (se necessário)
  4. Selecionar imagens desejadas
  5. Configurar título e FPS
  6. Gerar timelapse automaticamente
  7. Vídeo é armazenado no servidor em `/media/generated_timelapses/`

### 4. Gerenciamento de Permissões
- Localização: Detalhes do projeto (apenas admin)
- Funcionalidades:
  - Definir quais grupos podem visualizar o vídeo
  - Controle granular de acesso
  - Histórico de permissões

## Estrutura de Arquivos no Servidor

### Diretórios de Origem (Configuráveis)
- **Localização**: Definida pelo administrador
- **Exemplo**: `/var/www/eagleview/camera_images/`
- **Formatos suportados**: JPG, JPEG, PNG, BMP, TIFF
- **Navegação**: Hierárquica com suporte a subdiretórios

### Vídeos Gerados
- **Localização**: `/media/generated_timelapses/`
- **Formato**: MP4 com codec H.264
- **Nomenclatura**: `timelapse_{id}_{timestamp}.mp4`
- **Acesso**: Controlado por permissões de grupo

### Configuração de Exemplo
1. **Diretório**: "Câmeras de Segurança"
   - Caminho: `/var/security_cameras/`
   - Grupos permitidos: Segurança, Administradores
   
2. **Diretório**: "Construção Civil"
   - Caminho: `/var/construction_site/`
   - Grupos permitidos: Engenharia, Supervisores

## Sistema de Permissões

### Hierarquia de Acesso
1. **Superusuário**: Acesso total a todos os recursos
2. **Staff/Admin**: Navegação de diretórios + criação de timelapses
3. **Usuário Comum**: Visualização de timelapses autorizados

### Controle de Grupos
- Cada timelapse pertence a um grupo (tenant)
- Administradores podem conceder acesso adicional a outros grupos
- Usuários veem apenas timelapses do seu grupo ou com permissão específica

## Funcionalidades Técnicas

### Geração de Vídeo
- **Engine**: FFmpeg
- **Formatos de entrada**: Imagens estáticas
- **Formato de saída**: MP4 (H.264)
- **FPS configurável**: 12, 24, 30, 60 FPS
- **Processamento**: Síncrono (pode ser migrado para assíncrono com Celery)

### Otimizações
- Listagem eficiente de diretórios
- Suporte a navegação de grandes volumes de imagens
- Validação de formatos de arquivo
- Controle de acesso granular

## Deploy no Servidor Hetzner

### Especificações do Servidor
- **Endereço**: 95.216.101.172
- **Domínio**: eagleviewtimelapse.com.br
- **Hardware**: Intel i7-7700, 64GB RAM, 8TB HDD
- **Localização**: Helsinki, Finlândia

### Estrutura de Deploy
```
/var/www/eagleview/
├── eagleview_django/          # Código da aplicação
├── media/
│   ├── generated_timelapses/  # Vídeos gerados
│   └── uploads/              # Outros uploads
├── static/                   # Arquivos estáticos
├── camera_feeds/             # Imagens das câmeras
│   ├── site1/
│   ├── site2/
│   └── site3/
└── backups/                  # Backups do sistema
```

### Configurações Necessárias
1. **Nginx**: Proxy reverso + servir arquivos estáticos
2. **Gunicorn**: Servidor WSGI
3. **PostgreSQL**: Banco de dados de produção
4. **FFmpeg**: Processamento de vídeo
5. **SSL**: Certificado Let's Encrypt

## Próximos Passos

### Tarefas Restantes
- [ ] Deploy no servidor Hetzner
- [ ] Configuração de domínio DNS
- [ ] Configuração de VPN (WireGuard)
- [ ] Configuração de SSL/HTTPS
- [ ] Backup automatizado
- [ ] Monitoramento do sistema
- [ ] Documentação de API (se necessário)

### Melhorias Futuras
- [ ] Processamento assíncrono com Celery + Redis
- [ ] Interface de upload via web (se necessário)
- [ ] Notificações em tempo real
- [ ] API REST para integração
- [ ] Sistema de logs avançado
- [ ] Compressão automática de vídeos
- [ ] Thumbnails automáticos

---

*Última atualização: Janeiro 2025*
*Sistema operacional no servidor Hetzner com navegação de diretórios implementada* 
'use client';
import { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../layout';
import type { AuthContextType } from '../layout';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import { dbService } from '@/services/db.service';
import { Button } from '@/app/components/Button';
import { PlusIcon, EyeIcon, Loader2, AlertTriangle } from 'lucide-react';

interface Camera {
  id: number;
  nome: string;
  descricao?: string;
  ip: string;
  username: string;
  password: string;
  port: string;
  protocol: string;
  status: string;
  location?: string;
  model?: string;
  firmware?: string;
  lastConnection: string;
}

const CameraCard = ({
  camera,
  onView,
  onEdit,
  onDelete
}: {
  camera: Camera;
  onView: (id: number) => void;
  onEdit: (camera: Camera) => void;
  onDelete: (id: number) => void;
}) => {
  const statusColors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    error: 'bg-red-100 text-red-800',
    maintenance: 'bg-yellow-100 text-yellow-800'
  };

  const statusColor = statusColors[camera.status as keyof typeof statusColors] || statusColors.inactive;
  const lastConnection = new Date(camera.lastConnection).toLocaleString('pt-BR');

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="p-5">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{camera.nome}</h3>
            <p className="text-sm text-gray-500">{camera.ip}:{camera.port}</p>
          </div>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColor}`}>
            {camera.status === 'active' ? 'Ativo' :
             camera.status === 'inactive' ? 'Inativo' :
             camera.status === 'error' ? 'Erro' : 'Manutenção'}
          </span>
        </div>

        <div className="mt-4 text-sm text-gray-600">
          <p><span className="font-medium">Protocolo:</span> {camera.protocol.toUpperCase()}</p>
          <p><span className="font-medium">Última conexão:</span> {lastConnection}</p>
          {camera.location && <p><span className="font-medium">Localização:</span> {camera.location}</p>}
          {camera.model && <p><span className="font-medium">Modelo:</span> {camera.model}</p>}
        </div>
      </div>

      <div className="px-5 py-3 bg-gray-50 flex justify-end space-x-2">
        <Button
          onClick={() => onEdit(camera)}
          variant="outline"
          size="sm"
          leftIcon={<svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>}
          className="border-gray-300 bg-white text-gray-700"
        >
          Editar
        </Button>
        <Button
          onClick={() => onDelete(camera.id)}
          variant="danger"
          size="sm"
          leftIcon={<svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>}
          className="bg-red-600 text-white"
        >
          Excluir
        </Button>
        <Button
          onClick={() => onView(camera.id)}
          variant="primary"
          size="sm"
          leftIcon={<EyeIcon className="h-4 w-4" />}
          className="bg-blue-600 text-white"
        >
          Visualizar
        </Button>
      </div>
    </div>
  );
};

const CamerasPage = () => {
  const auth = useContext(AuthContext) as AuthContextType;
  const router = useRouter();
  const [cameras, setCameras] = useState<Camera[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCamera, setSelectedCamera] = useState<Camera | null>(null);
  const [cameraToDelete, setCameraToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const defaultCameraState = {
    nome: '',
    descricao: '',
    ip: '',
    username: '',
    password: '',
    port: '554',
    protocol: 'rtsp',
    status: 'inactive',
    location: '',
    model: '',
    url_path: '',
    connection_options: ''
  };

  const [newCamera, setNewCamera] = useState(defaultCameraState);
  const [editCamera, setEditCamera] = useState(defaultCameraState);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!auth.isAuthenticated) {
      router.push('/login');
      return;
    }

    loadCameras();
  }, [auth.isAuthenticated, router]);

  const loadCameras = async () => {
    try {
      setIsLoading(true);
      const camerasData = await dbService.getCameras();
      setCameras(camerasData);
    } catch (error) {
      console.error('Erro ao carregar câmeras:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>, isEdit = false) => {
    const { name, value } = e.target;
    if (isEdit) {
      setEditCamera(prev => ({
        ...prev,
        [name]: value
      }));
    } else {
      setNewCamera(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleAddCamera = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError('');

    try {
      const camera = await dbService.createCamera(newCamera);
      setCameras(prev => [...prev, camera]);
      setShowAddModal(false);
      setNewCamera(defaultCameraState);
    } catch (error) {
      console.error('Erro ao adicionar câmera:', error);
      setError('Erro ao adicionar câmera. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEditCamera = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCamera) return;

    setIsSaving(true);
    setError('');

    try {
      const updatedCamera = await dbService.updateCamera(selectedCamera.id, editCamera);
      if (updatedCamera) {
        setCameras(prev => prev.map(cam =>
          cam.id === selectedCamera.id ? { ...cam, ...updatedCamera } : cam
        ));
        setShowEditModal(false);
        setSelectedCamera(null);
      }
    } catch (error) {
      console.error('Erro ao atualizar câmera:', error);
      setError('Erro ao atualizar câmera. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteCamera = async () => {
    if (cameraToDelete === null) return;

    setIsDeleting(true);

    try {
      const result = await dbService.deleteCamera(cameraToDelete);
      if (result.success) {
        setCameras(prev => prev.filter(cam => cam.id !== cameraToDelete));
        setShowDeleteModal(false);
        setCameraToDelete(null);
      }
    } catch (error) {
      console.error('Erro ao excluir câmera:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleViewCamera = (id: number) => {
    router.push(`/streaming?camera=${id}`);
  };

  const openEditModal = (camera: Camera) => {
    setSelectedCamera(camera);
    setEditCamera({
      nome: camera.nome,
      descricao: camera.descricao || '',
      ip: camera.ip,
      username: camera.username,
      password: camera.password,
      port: camera.port,
      protocol: camera.protocol,
      status: camera.status,
      location: camera.location || '',
      model: camera.model || '',
      url_path: camera.url_path || '',
      connection_options: camera.connection_options || ''
    });
    setShowEditModal(true);
  };

  const openDeleteModal = (id: number) => {
    setCameraToDelete(id);
    setShowDeleteModal(true);
  };

  if (!auth.isAuthenticated) return null;

  return (
    <AppLayout>
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Gerenciamento de Câmeras</h1>
          <Button
            onClick={() => setShowAddModal(true)}
            leftIcon={<PlusIcon className="h-5 w-5" />}
            className="bg-blue-600 text-white"
          >
            Adicionar Câmera
          </Button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </div>
        ) : cameras.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Nenhuma câmera encontrada</h2>
            <p className="text-gray-600 mb-4">Adicione sua primeira câmera para começar a monitorar.</p>
            <Button
              onClick={() => setShowAddModal(true)}
              leftIcon={<PlusIcon className="h-5 w-5" />}
              className="bg-blue-600 text-white"
            >
              Adicionar Câmera
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {cameras.map(camera => (
              <CameraCard
                key={camera.id}
                camera={camera}
                onView={handleViewCamera}
                onEdit={openEditModal}
                onDelete={openDeleteModal}
              />
            ))}
          </div>
        )}

        {/* Modal de Adicionar Câmera */}
        {showAddModal && (
          <div className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm flex items-center justify-center z-50 p-4 overflow-y-auto">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Adicionar Nova Câmera</h2>
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
                    {error}
                  </div>
                )}

                <form onSubmit={handleAddCamera}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="md:col-span-2 p-4 bg-blue-50 rounded-lg border border-blue-100 mb-2">
                      <h3 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        Informações de Configuração
                      </h3>
                      <p className="text-sm text-blue-700 mb-1">Configure sua câmera preenchendo os campos abaixo. Os campos obrigatórios estão marcados com *.</p>
                      <p className="text-sm text-blue-700">Para câmeras TCP/UDP, certifique-se de fornecer o endereço IP correto e a porta apropriada.</p>
                    </div>
                    <div>
                      <label htmlFor="nome" className="block text-sm font-medium text-gray-700 mb-1">
                        Nome da Câmera *
                      </label>
                      <input
                        type="text"
                        id="nome"
                        name="nome"
                        value={newCamera.nome}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="ip" className="block text-sm font-medium text-gray-700 mb-1">
                        Endereço IP / Hostname *
                      </label>
                      <input
                        type="text"
                        id="ip"
                        name="ip"
                        value={newCamera.ip}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="port" className="block text-sm font-medium text-gray-700 mb-1">
                        Porta *
                      </label>
                      <input
                        type="text"
                        id="port"
                        name="port"
                        value={newCamera.port}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="protocol" className="block text-sm font-medium text-gray-700 mb-1">
                        Protocolo *
                      </label>
                      <select
                        id="protocol"
                        name="protocol"
                        value={newCamera.protocol}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      >
                        <optgroup label="Protocolos Comuns">
                          <option value="rtsp">RTSP</option>
                          <option value="tcp">TCP</option>
                          <option value="udp">UDP</option>
                          <option value="http">HTTP</option>
                        </optgroup>
                        <optgroup label="Streaming Web">
                          <option value="webrtc">WebRTC</option>
                          <option value="flv">FLV</option>
                          <option value="mjpeg">MJPEG</option>
                          <option value="hls">HLS</option>
                        </optgroup>
                        <optgroup label="Proprietários">
                          <option value="reolink">Reolink P2P</option>
                          <option value="onvif">ONVIF</option>
                        </optgroup>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                        Usuário *
                      </label>
                      <input
                        type="text"
                        id="username"
                        name="username"
                        value={newCamera.username}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Senha *
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={newCamera.password}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                        Localização
                      </label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={newCamera.location}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>

                    <div>
                      <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                        Modelo
                      </label>
                      <input
                        type="text"
                        id="model"
                        name="model"
                        value={newCamera.model}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>

                    <div>
                      <label htmlFor="url_path" className="block text-sm font-medium text-gray-700 mb-1">
                        Caminho URL <span className="text-xs text-gray-500">(opcional)</span>
                      </label>
                      <input
                        type="text"
                        id="url_path"
                        name="url_path"
                        value={newCamera.url_path}
                        onChange={handleInputChange}
                        placeholder="Ex: /video/stream"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="connection_options" className="block text-sm font-medium text-gray-700 mb-1">
                        Opções de Conexão <span className="text-xs text-gray-500">(opcional)</span>
                      </label>
                      <input
                        type="text"
                        id="connection_options"
                        name="connection_options"
                        value={newCamera.connection_options}
                        onChange={handleInputChange}
                        placeholder="Ex: transport=tcp&timeout=30"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      <p className="mt-1 text-xs text-gray-500">Parâmetros adicionais para a conexão, separados por & (ex: transport=tcp&timeout=30)</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="descricao" className="block text-sm font-medium text-gray-700 mb-1">
                      Descrição
                    </label>
                    <textarea
                      id="descricao"
                      name="descricao"
                      value={newCamera.descricao}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    ></textarea>
                  </div>

                  <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Exemplos de Configuração</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-600">
                      <div>
                        <p className="font-medium mb-1">Câmera RTSP:</p>
                        <ul className="list-disc list-inside space-y-1 pl-2">
                          <li>Protocolo: RTSP</li>
                          <li>Porta: 554</li>
                          <li>Caminho URL: /stream1 ou /h264/ch1/main/av_stream</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium mb-1">Câmera TCP:</p>
                        <ul className="list-disc list-inside space-y-1 pl-2">
                          <li>Protocolo: TCP</li>
                          <li>Porta: 80 ou 8000</li>
                          <li>Opções: transport=tcp</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium mb-1">Câmera UDP:</p>
                        <ul className="list-disc list-inside space-y-1 pl-2">
                          <li>Protocolo: UDP</li>
                          <li>Porta: 5000-5900 (típico)</li>
                          <li>Opções: transport=udp</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium mb-1">Câmera HTTP/MJPEG:</p>
                        <ul className="list-disc list-inside space-y-1 pl-2">
                          <li>Protocolo: HTTP ou MJPEG</li>
                          <li>Porta: 80</li>
                          <li>Caminho URL: /video.mjpg</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button
                      type="button"
                      onClick={() => setShowAddModal(false)}
                      variant="outline"
                      disabled={isSaving}
                      className="border-gray-300 bg-white text-gray-700"
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      isLoading={isSaving}
                      className="bg-blue-600 text-white"
                    >
                      {isSaving ? 'Salvando...' : 'Adicionar Câmera'}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Modal de Editar Câmera */}
        {showEditModal && selectedCamera && (
          <div className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm flex items-center justify-center z-50 p-4 overflow-y-auto">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Editar Câmera</h2>
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
                    {error}
                  </div>
                )}

                <form onSubmit={handleEditCamera}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="md:col-span-2 p-4 bg-blue-50 rounded-lg border border-blue-100 mb-2">
                      <h3 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        Informações de Configuração
                      </h3>
                      <p className="text-sm text-blue-700 mb-1">Configure sua câmera preenchendo os campos abaixo. Os campos obrigatórios estão marcados com *.</p>
                      <p className="text-sm text-blue-700">Para câmeras TCP/UDP, certifique-se de fornecer o endereço IP correto e a porta apropriada.</p>
                    </div>
                    <div>
                      <label htmlFor="nome" className="block text-sm font-medium text-gray-700 mb-1">
                        Nome da Câmera *
                      </label>
                      <input
                        type="text"
                        id="nome"
                        name="nome"
                        value={editCamera.nome}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="ip" className="block text-sm font-medium text-gray-700 mb-1">
                        Endereço IP / Hostname *
                      </label>
                      <input
                        type="text"
                        id="ip"
                        name="ip"
                        value={editCamera.ip}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="port" className="block text-sm font-medium text-gray-700 mb-1">
                        Porta *
                      </label>
                      <input
                        type="text"
                        id="port"
                        name="port"
                        value={editCamera.port}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="protocol" className="block text-sm font-medium text-gray-700 mb-1">
                        Protocolo *
                      </label>
                      <select
                        id="protocol"
                        name="protocol"
                        value={editCamera.protocol}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      >
                        <optgroup label="Protocolos Comuns">
                          <option value="rtsp">RTSP</option>
                          <option value="tcp">TCP</option>
                          <option value="udp">UDP</option>
                          <option value="http">HTTP</option>
                        </optgroup>
                        <optgroup label="Streaming Web">
                          <option value="webrtc">WebRTC</option>
                          <option value="flv">FLV</option>
                          <option value="mjpeg">MJPEG</option>
                          <option value="hls">HLS</option>
                        </optgroup>
                        <optgroup label="Proprietários">
                          <option value="reolink">Reolink P2P</option>
                          <option value="onvif">ONVIF</option>
                        </optgroup>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                        Usuário *
                      </label>
                      <input
                        type="text"
                        id="username"
                        name="username"
                        value={editCamera.username}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Senha *
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={editCamera.password}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                        Localização
                      </label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={editCamera.location}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>

                    <div>
                      <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                        Modelo
                      </label>
                      <input
                        type="text"
                        id="model"
                        name="model"
                        value={editCamera.model}
                        onChange={(e) => handleInputChange(e, true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>

                    <div>
                      <label htmlFor="url_path" className="block text-sm font-medium text-gray-700 mb-1">
                        Caminho URL <span className="text-xs text-gray-500">(opcional)</span>
                      </label>
                      <input
                        type="text"
                        id="url_path"
                        name="url_path"
                        value={editCamera.url_path}
                        onChange={(e) => handleInputChange(e, true)}
                        placeholder="Ex: /video/stream"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="connection_options" className="block text-sm font-medium text-gray-700 mb-1">
                        Opções de Conexão <span className="text-xs text-gray-500">(opcional)</span>
                      </label>
                      <input
                        type="text"
                        id="connection_options"
                        name="connection_options"
                        value={editCamera.connection_options}
                        onChange={(e) => handleInputChange(e, true)}
                        placeholder="Ex: transport=tcp&timeout=30"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      <p className="mt-1 text-xs text-gray-500">Parâmetros adicionais para a conexão, separados por & (ex: transport=tcp&timeout=30)</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="descricao" className="block text-sm font-medium text-gray-700 mb-1">
                      Descrição
                    </label>
                    <textarea
                      id="descricao"
                      name="descricao"
                      value={editCamera.descricao}
                      onChange={(e) => handleInputChange(e, true)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    ></textarea>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button
                      type="button"
                      onClick={() => setShowEditModal(false)}
                      variant="outline"
                      disabled={isSaving}
                      className="border-gray-300 bg-white text-gray-700"
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      isLoading={isSaving}
                      className="bg-blue-600 text-white"
                    >
                      {isSaving ? 'Salvando...' : 'Salvar Alterações'}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Modal de Confirmação de Exclusão */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-red-100 rounded-full p-2 mr-3">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900">Confirmar Exclusão</h2>
                </div>

                <p className="mb-6 text-gray-700">
                  Tem certeza que deseja excluir esta câmera? Esta ação não pode ser desfeita.
                </p>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    onClick={() => setShowDeleteModal(false)}
                    variant="outline"
                    disabled={isDeleting}
                    className="border-gray-300 bg-white text-gray-700"
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="button"
                    onClick={handleDeleteCamera}
                    variant="danger"
                    isLoading={isDeleting}
                    className="bg-red-600 text-white"
                  >
                    {isDeleting ? 'Excluindo...' : 'Excluir Câmera'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default CamerasPage;

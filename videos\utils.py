"""
Utilitários para processamento otimizado de vídeos e imagens
EagleView Django Project
"""

import os
import tempfile
import subprocess
import threading
from PIL import Image, ImageOps
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Classe para processamento otimizado de vídeos"""
    
    def __init__(self):
        self.quality_presets = {
            'preview': {
                'crf': 28,
                'preset': 'ultrafast',
                'scale': '640:360',
                'framerate': 8
            },
            'standard': {
                'crf': 23,
                'preset': 'medium',
                'scale': '1920:1080',
                'framerate': 30
            },
            'high': {
                'crf': 18,
                'preset': 'slow',
                'scale': '1920:1080',
                'framerate': 30
            }
        }
    
    def process_frames_parallel(self, frames, output_path, quality='standard', progress_callback=None):
        """Processar frames em paralelo para melhor performance"""
        try:
            preset = self.quality_presets.get(quality, self.quality_presets['standard'])
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Processar frames em paralelo
                processed_frames = self._resize_frames_parallel(frames, temp_dir, preset['scale'])
                
                if progress_callback:
                    progress_callback(30)  # 30% - frames processados
                
                # Gerar vídeo com FFmpeg otimizado
                success = self._generate_video_optimized(
                    processed_frames, 
                    output_path, 
                    preset,
                    progress_callback
                )
                
                if progress_callback:
                    progress_callback(100 if success else 0)
                
                return success
                
        except Exception as e:
            logger.error(f"Erro no processamento de vídeo: {e}")
            if progress_callback:
                progress_callback(0)
            return False
    
    def _resize_frames_parallel(self, frames, temp_dir, target_size):
        """Redimensionar frames em paralelo usando threading"""
        processed_frames = []
        threads = []
        
        def resize_frame(frame, index, output_path):
            try:
                # Abrir e redimensionar imagem
                with Image.open(frame.image.path) as img:
                    # Converter para RGB se necessário
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Redimensionar mantendo proporção
                    width, height = map(int, target_size.split(':'))
                    img_resized = ImageOps.fit(img, (width, height), Image.Resampling.LANCZOS)
                    
                    # Salvar frame processado
                    img_resized.save(output_path, 'JPEG', quality=85, optimize=True)
                    
            except Exception as e:
                logger.error(f"Erro ao processar frame {index}: {e}")
        
        # Criar threads para processamento paralelo
        for i, frame in enumerate(frames):
            output_path = os.path.join(temp_dir, f"frame_{i:06d}.jpg")
            processed_frames.append(output_path)
            
            thread = threading.Thread(
                target=resize_frame,
                args=(frame, i, output_path)
            )
            threads.append(thread)
            thread.start()
            
            # Limitar número de threads simultâneas
            if len(threads) >= 4:  # Máximo 4 threads
                for t in threads:
                    t.join()
                threads.clear()
        
        # Aguardar threads restantes
        for thread in threads:
            thread.join()
        
        return sorted(processed_frames)
    
    def _generate_video_optimized(self, frame_paths, output_path, preset, progress_callback=None):
        """Gerar vídeo usando FFmpeg com configurações otimizadas"""
        try:
            # Criar input pattern
            input_pattern = os.path.join(os.path.dirname(frame_paths[0]), "frame_%06d.jpg")
            
            # Comando FFmpeg otimizado
            cmd = [
                'ffmpeg', '-y',  # Sobrescrever arquivo de saída
                '-framerate', str(preset['framerate']),
                '-i', input_pattern,
                '-c:v', 'libx264',
                '-crf', str(preset['crf']),
                '-preset', preset['preset'],
                '-vf', f"scale={preset['scale']}:force_original_aspect_ratio=decrease,pad={preset['scale']}:(ow-iw)/2:(oh-ih)/2",
                '-pix_fmt', 'yuv420p',
                '-movflags', '+faststart',  # Otimização para streaming
                output_path
            ]
            
            if progress_callback:
                progress_callback(60)  # 60% - iniciando geração
            
            # Executar FFmpeg
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutos timeout
            )
            
            if process.returncode == 0:
                logger.info(f"Vídeo gerado com sucesso: {output_path}")
                return True
            else:
                logger.error(f"Erro FFmpeg: {process.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Timeout na geração de vídeo")
            return False
        except Exception as e:
            logger.error(f"Erro na geração de vídeo: {e}")
            return False

class ImageOptimizer:
    """Classe para otimização de imagens"""
    
    @staticmethod
    def optimize_uploaded_image(image_path, max_size=(1920, 1080), quality=85):
        """Otimizar imagem carregada para economizar espaço"""
        try:
            with Image.open(image_path) as img:
                # Converter para RGB se necessário
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Redimensionar se necessário
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # Salvar otimizada
                img.save(image_path, 'JPEG', quality=quality, optimize=True)
                
                return True
                
        except Exception as e:
            logger.error(f"Erro ao otimizar imagem: {e}")
            return False
    
    @staticmethod
    def get_image_info(image_path):
        """Obter informações da imagem"""
        try:
            with Image.open(image_path) as img:
                return {
                    'width': img.size[0],
                    'height': img.size[1],
                    'format': img.format,
                    'mode': img.mode,
                    'size_mb': os.path.getsize(image_path) / (1024 * 1024)
                }
        except Exception as e:
            logger.error(f"Erro ao obter info da imagem: {e}")
            return None

class PerformanceMonitor:
    """Monitor de performance para operações"""
    
    def __init__(self):
        self.metrics = {}
    
    def start_timer(self, operation):
        """Iniciar timer para uma operação"""
        import time
        self.metrics[operation] = {'start': time.time()}
    
    def end_timer(self, operation):
        """Finalizar timer e calcular duração"""
        import time
        if operation in self.metrics:
            self.metrics[operation]['end'] = time.time()
            self.metrics[operation]['duration'] = self.metrics[operation]['end'] - self.metrics[operation]['start']
            return self.metrics[operation]['duration']
        return 0
    
    def get_metrics(self):
        """Obter métricas coletadas"""
        return self.metrics
    
    def log_metrics(self):
        """Log das métricas coletadas"""
        for operation, data in self.metrics.items():
            if 'duration' in data:
                logger.info(f"Operação '{operation}': {data['duration']:.2f}s")

def check_system_resources():
    """Verificar recursos do sistema disponíveis"""
    import psutil
    
    return {
        'cpu_percent': psutil.cpu_percent(),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_free_gb': psutil.disk_usage('/').free / (1024**3),
        'cpu_count': psutil.cpu_count()
    }

def cleanup_temp_files(max_age_hours=24):
    """Limpar arquivos temporários antigos"""
    import time
    import glob
    
    temp_patterns = [
        os.path.join(settings.MEDIA_ROOT, 'temp', '*'),
        os.path.join(settings.MEDIA_ROOT, 'previews', '*'),
    ]
    
    cleaned_count = 0
    current_time = time.time()
    
    for pattern in temp_patterns:
        for file_path in glob.glob(pattern):
            try:
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > (max_age_hours * 3600):  # Converter horas para segundos
                    os.remove(file_path)
                    cleaned_count += 1
            except Exception as e:
                logger.error(f"Erro ao limpar arquivo {file_path}: {e}")
    
    logger.info(f"Limpeza completa: {cleaned_count} arquivos removidos")
    return cleaned_count 
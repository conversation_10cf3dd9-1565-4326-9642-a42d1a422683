# -*- coding: utf-8 -*-
"""
Script para extrair informações de um QR Code.

Este script utiliza as bibliotecas pyzbar e Pillow para ler
um arquivo de imagem contendo um QR Code e exibir o conteúdo decodificado.
"""

from pyzbar import pyzbar
from PIL import Image
import os

def extract_qr_code_info(image_path):
    """
    Lê uma imagem de QR Code e retorna o conteúdo decodificado.

    Args:
        image_path (str): O caminho para o arquivo de imagem do QR Code.

    Returns:
        str: O conteúdo decodificado do QR Code, ou None se não for encontrado.
    """
    try:
        # Verifica se o arquivo existe
        if not os.path.exists(image_path):
            print(f"Erro: O arquivo de imagem não foi encontrado em '{image_path}'")
            return None

        # Abre a imagem
        img = Image.open(image_path)

        # Decodifica os QR Codes na imagem
        decoded_objects = pyzbar.decode(img)

        if not decoded_objects:
            print("Nenhum QR Code encontrado na imagem.")
            return None

        # Extrai e retorna o conteúdo do primeiro QR Code encontrado
        qr_data = decoded_objects[0].data.decode('utf-8')
        return qr_data

    except FileNotFoundError:
        print(f"Erro: O arquivo de imagem não foi encontrado em '{image_path}'")
        return None
    except Exception as e:
        print(f"Ocorreu um erro ao processar a imagem: {e}")
        return None

if __name__ == "__main__":
    # Define o caminho relativo para a imagem do QR Code
    # Ajuste este caminho se o script estiver em um local diferente
    qr_image_relative_path = "../docs/acessos/camera1/qrcode1.png"

    # Obtém o diretório atual do script
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Constrói o caminho absoluto para a imagem
    qr_image_path = os.path.join(script_dir, qr_image_relative_path)

    # Extrai as informações do QR Code
    qr_info = extract_qr_code_info(qr_image_path)

    if qr_info:
        print("Informação extraída do QR Code:")
        print(qr_info)
    else:
        print("Não foi possível extrair informações do QR Code.")
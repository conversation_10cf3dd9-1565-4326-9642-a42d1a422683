from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from videos.models import Timelapse, FrameImage
import os


class Command(BaseCommand):
    help = 'Configura dados de demonstração para o EagleView'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Remove dados existentes antes de criar novos',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Removendo dados existentes...')
            Timelapse.objects.all().delete()
            # Não deletar usuários e grupos para não afetar o superuser
        
        # Criar grupo de exemplo se não existir
        empresa_demo, created = Group.objects.get_or_create(
            name='Empresa Demo'
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Grupo "{empresa_demo.name}" criado com sucesso!')
            )
        
        # Criar usuário de exemplo se não existir
        demo_user, created = User.objects.get_or_create(
            username='demo',
            defaults={
                'first_name': 'Usu<PERSON>rio',
                'last_name': 'Demo',
                'email': '<EMAIL>',
                'is_active': True,
            }
        )
        
        if created:
            demo_user.set_password('demo123')
            demo_user.save()
            self.stdout.write(
                self.style.SUCCESS(f'Usuário demo criado! Login: demo, Senha: demo123')
            )
        
        # Adicionar usuário ao grupo
        demo_user.groups.add(empresa_demo)
        
        # Criar projetos de exemplo
        if not Timelapse.objects.filter(tenant=empresa_demo).exists():
            projetos_demo = [
                {
                    'title': 'Construção do Edifício Principal',
                    'tenant': empresa_demo,
                    'created_by': demo_user
                },
                {
                    'title': 'Reforma da Fachada',
                    'tenant': empresa_demo,
                    'created_by': demo_user
                },
                {
                    'title': 'Paisagismo do Jardim',
                    'tenant': empresa_demo,
                    'created_by': demo_user
                }
            ]
            
            for projeto_data in projetos_demo:
                projeto = Timelapse.objects.create(**projeto_data)
                self.stdout.write(
                    self.style.SUCCESS(f'Projeto "{projeto.title}" criado!')
                )
        
        self.stdout.write(
            self.style.SUCCESS('\n✅ Configuração de demonstração concluída!')
        )
        self.stdout.write('📋 Informações de acesso:')
        self.stdout.write(f'   👤 Usuário: demo')
        self.stdout.write(f'   🔑 Senha: demo123')
        self.stdout.write(f'   🏢 Empresa: {empresa_demo.name}')
        self.stdout.write('\n🌐 Acesse: http://localhost:8000/login/')
        
        # Verificar se admin existe
        if User.objects.filter(is_superuser=True).exists():
            admin = User.objects.filter(is_superuser=True).first()
            self.stdout.write(f'\n🔧 Admin: {admin.username}')
            self.stdout.write('🌐 Admin: http://localhost:8000/admin/') 
<html>
<head>
<meta charset="utf-8"/>
<title>Oo</title>
<style type="text/css">
body {
  background: repeating-linear-gradient(0deg, #0E0D0E 25%, #0E0D0E 50%, #171819 50%, #171819 75%);
  background-size: 10px 10px;
  height: 100vh;
  overflow: hidden;
  display: flex;
  font-family: "Barlow", sans-serif;
  justify-content: center;
  align-items: center;
}

.glitch {
  position: relative;
  color: #fff;
  font-size: 80px;
}

.line:not(:first-child) {
  position: absolute;
  top: 0;
  left: 0;
}
.line:nth-child(1) {
  -webkit-animation: clip 3000ms -300ms linear infinite, glitch1 500ms -658ms linear infinite;
          animation: clip 3000ms -300ms linear infinite, glitch1 500ms -658ms linear infinite;
}
@-webkit-keyframes glitch1 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(-1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(3px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch1 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(-1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(3px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(2) {
  -webkit-animation: clip 3000ms -600ms linear infinite, glitch2 500ms -136ms linear infinite;
          animation: clip 3000ms -600ms linear infinite, glitch2 500ms -136ms linear infinite;
}
@-webkit-keyframes glitch2 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(4px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(2px);
    color: #AC1212;
  }
  95% {
    transform: translateX(-2px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch2 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(4px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(2px);
    color: #AC1212;
  }
  95% {
    transform: translateX(-2px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(3) {
  -webkit-animation: clip 3000ms -900ms linear infinite, glitch3 500ms -969ms linear infinite;
          animation: clip 3000ms -900ms linear infinite, glitch3 500ms -969ms linear infinite;
}
@-webkit-keyframes glitch3 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(5px);
    color: #AC1212;
  }
  95% {
    transform: translateX(1px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch3 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(5px);
    color: #AC1212;
  }
  95% {
    transform: translateX(1px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(4) {
  -webkit-animation: clip 3000ms -1200ms linear infinite, glitch4 500ms -484ms linear infinite;
          animation: clip 3000ms -1200ms linear infinite, glitch4 500ms -484ms linear infinite;
}
@-webkit-keyframes glitch4 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(0px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(1px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch4 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(0px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(1px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(5) {
  -webkit-animation: clip 3000ms -1500ms linear infinite, glitch5 500ms -570ms linear infinite;
          animation: clip 3000ms -1500ms linear infinite, glitch5 500ms -570ms linear infinite;
}
@-webkit-keyframes glitch5 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(1px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(2px);
    color: #AC1212;
  }
  95% {
    transform: translateX(-3px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch5 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(1px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(2px);
    color: #AC1212;
  }
  95% {
    transform: translateX(-3px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(6) {
  -webkit-animation: clip 3000ms -1800ms linear infinite, glitch6 500ms -907ms linear infinite;
          animation: clip 3000ms -1800ms linear infinite, glitch6 500ms -907ms linear infinite;
}
@-webkit-keyframes glitch6 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(0px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(5px);
    color: #AC1212;
  }
  95% {
    transform: translateX(0px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch6 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(0px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(5px);
    color: #AC1212;
  }
  95% {
    transform: translateX(0px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(7) {
  -webkit-animation: clip 3000ms -2100ms linear infinite, glitch7 500ms -686ms linear infinite;
          animation: clip 3000ms -2100ms linear infinite, glitch7 500ms -686ms linear infinite;
}
@-webkit-keyframes glitch7 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(1px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(4px);
    color: #AC1212;
  }
  95% {
    transform: translateX(4px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch7 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(1px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(4px);
    color: #AC1212;
  }
  95% {
    transform: translateX(4px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(8) {
  -webkit-animation: clip 3000ms -2400ms linear infinite, glitch8 500ms -361ms linear infinite;
          animation: clip 3000ms -2400ms linear infinite, glitch8 500ms -361ms linear infinite;
}
@-webkit-keyframes glitch8 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(0px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(-1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(5px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch8 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(0px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(-1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(5px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(9) {
  -webkit-animation: clip 3000ms -2700ms linear infinite, glitch9 500ms -639ms linear infinite;
          animation: clip 3000ms -2700ms linear infinite, glitch9 500ms -639ms linear infinite;
}
@-webkit-keyframes glitch9 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(-1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(0px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch9 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(-1px);
    color: #AC1212;
  }
  95% {
    transform: translateX(0px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
.line:nth-child(10) {
  -webkit-animation: clip 3000ms -3000ms linear infinite, glitch10 500ms -533ms linear infinite;
          animation: clip 3000ms -3000ms linear infinite, glitch10 500ms -533ms linear infinite;
}
@-webkit-keyframes glitch10 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(-3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(0px);
    color: #AC1212;
  }
  95% {
    transform: translateX(-4px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes glitch10 {
  0% {
    transform: translateX(0);
  }
  80% {
    transform: translateX(0);
    color: #fff;
  }
  85% {
    transform: translateX(-3px);
    color: #4E9A26;
  }
  90% {
    transform: translateX(0px);
    color: #AC1212;
  }
  95% {
    transform: translateX(-4px);
    color: #fff;
  }
  100% {
    transform: translateX(0);
  }
}

@-webkit-keyframes clip {
  0% {
    -webkit-clip-path: polygon(0 100%, 100% 100%, 100% 120%, 0 120%);
            clip-path: polygon(0 100%, 100% 100%, 100% 120%, 0 120%);
  }
  100% {
    -webkit-clip-path: polygon(0 -20%, 100% -20%, 100% 0%, 0 0);
            clip-path: polygon(0 -20%, 100% -20%, 100% 0%, 0 0);
  }
}

@keyframes clip {
  0% {
    -webkit-clip-path: polygon(0 100%, 100% 100%, 100% 120%, 0 120%);
            clip-path: polygon(0 100%, 100% 100%, 100% 120%, 0 120%);
  }
  100% {
    -webkit-clip-path: polygon(0 -20%, 100% -20%, 100% 0%, 0 0);
            clip-path: polygon(0 -20%, 100% -20%, 100% 0%, 0 0);
  }
}
</style>
</head>
<body>

<div class="glitch">
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
  <div class="line">NOTHING TO SEE HERE</div>
</div>
</body>
</html>
ls/**
 * Sistema de Notificações e Preview em Tempo Real
 * EagleView Django Project
 */

class RealTimeManager {
    constructor() {
        this.notificationCheckInterval = null;
        this.jobStatusChecks = new Map();
        this.init();
    }

    init() {
        this.startNotificationPolling();
        this.setupPreviewHandlers();
        this.setupJobStatusCheckers();
    }

    // Sistema de Notificações
    startNotificationPolling() {
        // Verificar notificações a cada 5 segundos
        this.notificationCheckInterval = setInterval(() => {
            this.checkNotifications();
        }, 5000);

        // Verificação inicial
        this.checkNotifications();
    }

    async checkNotifications() {
        try {
            const response = await fetch('/api/notifications/');
            const data = await response.json();
            
            if (data.notifications) {
                this.processNotifications(data.notifications);
            }
        } catch (error) {
            console.error('Erro ao verificar notificações:', error);
        }
    }

    processNotifications(notifications) {
        const container = this.getOrCreateNotificationContainer();
        
        notifications.forEach(notification => {
            const existingNotification = document.getElementById(`notification-${notification.id}`);
            
            if (!existingNotification) {
                this.createNotification(notification, container);
            } else {
                this.updateNotification(notification, existingNotification);
            }
        });
    }

    getOrCreateNotificationContainer() {
        let container = document.getElementById('realtime-notifications');
        if (!container) {
            container = document.createElement('div');
            container.id = 'realtime-notifications';
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
        return container;
    }

    createNotification(notification, container) {
        const notificationEl = document.createElement('div');
        notificationEl.id = `notification-${notification.id}`;
        notificationEl.className = `notification alert ${this.getNotificationClass(notification.status)}`;
        notificationEl.style.cssText = `
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
            animation: slideInRight 0.3s ease-out;
            position: relative;
        `;

        const content = this.getNotificationContent(notification);
        notificationEl.innerHTML = content;

        // Auto-remover notificações completadas após 10 segundos
        if (notification.status === 'COMPLETED' || notification.status === 'FAILED') {
            setTimeout(() => {
                this.removeNotification(notificationEl);
            }, 10000);
        }

        container.appendChild(notificationEl);
    }

    updateNotification(notification, element) {
        const content = this.getNotificationContent(notification);
        element.innerHTML = content;
        element.className = `notification alert ${this.getNotificationClass(notification.status)}`;
    }

    getNotificationClass(status) {
        switch (status) {
            case 'PENDING': return 'alert-info';
            case 'PROCESSING': return 'alert-warning';
            case 'COMPLETED': return 'alert-success';
            case 'FAILED': return 'alert-error';
            default: return 'alert-info';
        }
    }

    getNotificationContent(notification) {
        const statusIcon = this.getStatusIcon(notification.status);
        const statusText = this.getStatusText(notification.status);
        
        let content = `
            <div class="notification-header">
                <strong>${statusIcon} ${notification.timelapse_title}</strong>
                <button onclick="realTimeManager.removeNotification(this.closest('.notification'))" 
                        class="btn-close">×</button>
            </div>
            <div class="notification-body">
                <p>${statusText}</p>
        `;

        if (notification.status === 'PROCESSING') {
            content += `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 50%"></div>
                </div>
            `;
        }

        if (notification.status === 'COMPLETED') {
            content += `
                <a href="/projetos/${notification.timelapse_id}/" class="btn btn-sm btn-primary">
                    Ver Resultado
                </a>
            `;
        }

        if (notification.error_message) {
            content += `<p class="error-message">${notification.error_message}</p>`;
        }

        content += `</div>`;
        return content;
    }

    getStatusIcon(status) {
        switch (status) {
            case 'PENDING': return '⏳';
            case 'PROCESSING': return '⚙️';
            case 'COMPLETED': return '✅';
            case 'FAILED': return '❌';
            default: return 'ℹ️';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'PENDING': return 'Aguardando processamento...';
            case 'PROCESSING': return 'Gerando vídeo...';
            case 'COMPLETED': return 'Vídeo gerado com sucesso!';
            case 'FAILED': return 'Falha na geração do vídeo.';
            default: return 'Status desconhecido';
        }
    }

    removeNotification(element) {
        element.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }

    // Sistema de Preview
    setupPreviewHandlers() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-preview') || e.target.closest('.btn-preview')) {
                e.preventDefault();
                const button = e.target.closest('.btn-preview');
                const timelapseId = button.dataset.timelapseId;
                this.generatePreview(timelapseId, button);
            }
        });
    }

    async generatePreview(timelapseId, button) {
        const originalText = button.innerHTML;
        button.innerHTML = '⚙️ Gerando Preview...';
        button.disabled = true;

        try {
            const formData = new FormData();
            const response = await fetch(`/api/preview/${timelapseId}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showPreviewModal(data.preview_url, timelapseId);
                showNotification('✅ Preview gerado com sucesso!', 'success');
            } else {
                showNotification(`❌ ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('Erro ao gerar preview:', error);
            showNotification('❌ Erro ao gerar preview', 'error');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    showPreviewModal(previewUrl, timelapseId) {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">📽️ Preview do Timelapse</h5>
                        <button type="button" class="btn-close" onclick="this.closest('.modal').remove()">×</button>
                    </div>
                    <div class="modal-body text-center">
                        <video controls autoplay style="width: 100%; max-width: 640px;">
                            <source src="${previewUrl}" type="video/mp4">
                            Seu navegador não suporta vídeo HTML5.
                        </video>
                        <p class="mt-3 text-muted">
                            <small>Preview gerado com qualidade reduzida para velocidade. O vídeo final terá qualidade superior.</small>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            Fechar
                        </button>
                        <a href="/projetos/${timelapseId}/" class="btn btn-primary">
                            Ver Projeto Completo
                        </a>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Sistema de Job Status
    setupJobStatusCheckers() {
        // Verificar se há elementos que precisam de monitoring
        document.addEventListener('DOMContentLoaded', () => {
            this.startJobMonitoring();
        });
    }

    startJobMonitoring() {
        const jobElements = document.querySelectorAll('[data-job-id]');
        jobElements.forEach(element => {
            const jobId = element.dataset.jobId;
            this.monitorJob(jobId, element);
        });
    }

    async monitorJob(jobId, element) {
        if (this.jobStatusChecks.has(jobId)) {
            return; // Já está sendo monitorado
        }

        const interval = setInterval(async () => {
            try {
                const response = await fetch(`/api/job-status/${jobId}/`);
                const data = await response.json();

                this.updateJobStatus(element, data);

                // Parar monitoring se job terminou
                if (data.status === 'COMPLETED' || data.status === 'FAILED') {
                    clearInterval(interval);
                    this.jobStatusChecks.delete(jobId);
                }
            } catch (error) {
                console.error('Erro ao verificar status do job:', error);
                clearInterval(interval);
                this.jobStatusChecks.delete(jobId);
            }
        }, 2000);

        this.jobStatusChecks.set(jobId, interval);
    }

    updateJobStatus(element, data) {
        // Atualizar progresso
        const progressBar = element.querySelector('.progress-fill');
        if (progressBar) {
            progressBar.style.width = `${data.progress}%`;
        }

        // Atualizar texto de status
        const statusText = element.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = this.getStatusText(data.status);
        }

        // Atualizar classe do elemento
        element.className = element.className.replace(/status-\w+/, `status-${data.status.toLowerCase()}`);

        // Se completou, mostrar link para vídeo
        if (data.status === 'COMPLETED' && data.video_url) {
            const videoLink = element.querySelector('.video-link');
            if (videoLink) {
                videoLink.href = data.video_url;
                videoLink.style.display = 'inline-block';
            }
        }
    }

    // Utilitários
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    destroy() {
        if (this.notificationCheckInterval) {
            clearInterval(this.notificationCheckInterval);
        }
        
        this.jobStatusChecks.forEach(interval => clearInterval(interval));
        this.jobStatusChecks.clear();
    }
}

// CSS para animações e estilos
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .notification {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
    }
    
    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .btn-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .progress-bar {
        width: 100%;
        height: 6px;
        background-color: rgba(255,255,255,0.3);
        border-radius: 3px;
        overflow: hidden;
        margin: 8px 0;
    }
    
    .progress-fill {
        height: 100%;
        background-color: var(--primary-color);
        transition: width 0.3s ease;
    }
    
    .error-message {
        font-size: 0.85em;
        margin-top: 8px;
        color: #ff4444;
    }
    
    .modal.show {
        background-color: rgba(0,0,0,0.5);
    }
    
    .btn-preview {
        margin-left: 8px;
    }
`;
document.head.appendChild(style);

// Inicializar manager
const realTimeManager = new RealTimeManager();

// Cleanup ao sair da página
window.addEventListener('beforeunload', () => {
    realTimeManager.destroy();
}); 
# Guia Rápido para Implantação

Este é um guia rápido para implantar o sistema POC-EAGLE no servidor.

## Opção 1: Usand<PERSON> o Script de Implantação

1. Conecte-se ao servidor:
   ```
   ssh root@**************
   ```

2. Transfira todos os arquivos do projeto para o servidor (do seu computador local):
   ```
   scp -r /caminho/local/POC-EAGLE/* root@**************:/tmp/poc-eagle/
   ```

3. No servidor, execute:
   ```
   cd /tmp/poc-eagle
   chmod +x deploy.sh
   ./deploy.sh
   ```

4. Acesse a aplicação:
   ```
   http://**************:3000/login
   ```

## Opção 2: Implantação Manual

1. Conecte-se ao servidor:
   ```
   ssh root@**************
   ```

2. Instale as dependências:
   ```
   apt update && apt upgrade -y
   curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
   apt install -y nodejs
   npm install -g pm2
   apt install -y nginx
   ```

3. Configure o projeto:
   ```
   mkdir -p /opt/poc-eagle
   # Transfira os arquivos para /opt/poc-eagle
   cd /opt/poc-eagle
   npm ci
   npm run build
   ```

4. Configure o Nginx:
   ```
   cp /opt/POC-EAGLE/nginx-config.conf /etc/nginx/sites-available/poc-eagle
   ln -sf /etc/nginx/sites-available/poc-eagle /etc/nginx/sites-enabled/
   rm -f /etc/nginx/sites-enabled/default
   nginx -t
   systemctl restart nginx
   ```

5. Inicie a aplicação com PM2:
   ```
   cd /opt/poc-eagle
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

6. Acesse a aplicação:
   ```
   http://**************:3000/login
   ```

## Comandos Úteis

- Verificar status: `pm2 status`
- Ver logs da aplicação: `pm2 logs poc-eagle`
- Ver logs do Nginx: `tail -f /var/log/nginx/error.log`
- Reiniciar aplicação: `pm2 restart poc-eagle`
- Reiniciar Nginx: `systemctl restart nginx` 
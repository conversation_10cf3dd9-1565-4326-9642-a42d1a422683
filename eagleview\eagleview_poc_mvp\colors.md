@import "tailwindcss";

:root {
  /* Esquema de cores principal - baseado na página de login */
  --primary: #3b82f6; /* azul mais vibrante */
  --primary-light: #60a5fa;
  --primary-dark: #2563eb;
  
  /* Cores de gradiente para fundos */
  --gradient-from: #eff6ff; /* from-blue-50 */
  --gradient-to: #e0e7ff;   /* to-indigo-100 */
  
  /* Cores de fundo e texto - clean */
  --background: #f8fafc;
  --foreground: #1e293b;
  --card-bg: #FFFFFF;
  
  /* Cores de destaque - mais vibrantes */
  --accent: #f59e0b;
  --accent-hover: #d97706;
  
  /* Cores de estado - mais brilhantes e distintas */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* Cores neutras - harmoniosas */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Bordas e sombras - mais suaves */
  --border-color: var(--gray-200);
  --border-radius: 0.5rem;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transições */
  --transition-fast: 0.15s ease;
  --transition: 0.3s ease;
  
  /* Espaçamento */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Regras globais para garantir que o layout ocupe toda a altura */
html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* Modo escuro desativado para manter sempre o tema claro */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 
    'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
  padding: 0;
}

/* Layout principal */
#__next, main {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ocultar o indicador de desenvolvimento do Next.js (ícone N no canto inferior esquerdo) */
#__next-build-watcher,
button[data-next-build-indicator] {
  display: none !important;
}

/* Classes de utilidade */
.card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: var(--spacing-6);
  border: 1px solid var(--border-color);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  padding: var(--spacing-2) var(--spacing-4);
  font-weight: 500;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
  box-shadow: var(--shadow);
}

.btn-accent {
  background: var(--accent);
  color: white;
}

.btn-accent:hover {
  background: var(--accent-hover);
  box-shadow: var(--shadow);
}

.form-input {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  color: var(--foreground);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  outline: none;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light)/30%;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236c757d'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--gray-700);
}

/* Status badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: var(--success)/10%;
  color: var(--success);
}

.badge-error {
  background-color: var(--error)/10%;
  color: var(--error);
}

.badge-warning {
  background-color: var(--warning)/10%;
  color: var(--warning);
}

.badge-info {
  background-color: var(--info)/10%;
  color: var(--info);
}

/* Gradientes - seguindo o estilo da página de login */
.bg-gradient-brand {
  background-image: linear-gradient(to bottom right, var(--gradient-from), var(--gradient-to));
}

/* Layout específico */
.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 18rem;
  z-index: 40;
}

.content-with-sidebar {
  margin-left: 18rem;
  width: calc(100% - 18rem);
}

/* Animações */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease;
}

.animate-slide-up {
  animation: slideUp 0.3s ease;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
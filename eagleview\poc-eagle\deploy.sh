#!/bin/bash

# Definir variáveis de ambiente
APP_DIR="/opt/POC-EAGLE"
BACKUP_DIR="/opt/backups/poc-eagle"
LOG_FILE="/var/log/poc-eagle-deploy.log"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Função para logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Função para verificar erros
check_error() {
    if [ $? -ne 0 ]; then
        log "ERRO: $1"
        exit 1
    fi
}

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"
check_error "Falha ao criar diretório de backup"

# Backup da aplicação atual
log "Criando backup da aplicação..."
tar -czf "$BACKUP_DIR/backup_$TIMESTAMP.tar.gz" -C "$APP_DIR" .
check_error "Falha ao criar backup"

# Instalar dependências
log "Instalando dependências..."
npm ci
check_error "Falha na instalação das dependências"

# Construir a aplicação
log "Construindo a aplicação..."
npm run build
check_error "Falha na construção da aplicação"

# Configurar Nginx
log "Configurando Nginx..."
if [ ! -d "/etc/nginx/sites-available" ]; then
    mkdir -p /etc/nginx/sites-available
    check_error "Falha ao criar diretório sites-available"
fi

if [ ! -d "/etc/nginx/sites-enabled" ]; then
    mkdir -p /etc/nginx/sites-enabled
    check_error "Falha ao criar diretório sites-enabled"
fi

# Fazer backup da configuração do Nginx se existir
if [ -f "/etc/nginx/sites-available/poc-eagle" ]; then
    log "Fazendo backup da configuração anterior do Nginx..."
    cp /etc/nginx/sites-available/poc-eagle "$BACKUP_DIR/nginx-poc-eagle-$TIMESTAMP.conf"
    check_error "Falha ao fazer backup da configuração do Nginx"
fi

# Copiar nova configuração
cp "$APP_DIR/nginx-config.conf" /etc/nginx/sites-available/poc-eagle
check_error "Falha ao copiar configuração do Nginx"

# Verificar se o link simbólico já existe e removê-lo se necessário
if [ -L "/etc/nginx/sites-enabled/poc-eagle" ]; then
    rm -f /etc/nginx/sites-enabled/poc-eagle
    check_error "Falha ao remover link simbólico antigo"
fi

# Criar novo link simbólico
ln -sf /etc/nginx/sites-available/poc-eagle /etc/nginx/sites-enabled/
check_error "Falha ao criar link simbólico"

# NÃO remover a configuração default do Nginx, pois existem outras aplicações

# Testar configuração do Nginx
log "Testando configuração do Nginx..."
nginx -t
check_error "Configuração do Nginx inválida"

# Recarregar (não reiniciar) o Nginx para preservar outras aplicações
log "Recarregando Nginx..."
systemctl reload nginx
check_error "Falha ao recarregar Nginx"

# Remover verificação de instalação do PM2 já que está instalado
log "Verificando aplicação no PM2..."

# Parar e remover instância anterior se existir
if pm2 list | grep -q "poc-eagle"; then
    log "Parando instância anterior da aplicação..."
    pm2 delete poc-eagle
    check_error "Falha ao remover instância anterior do PM2"
    sleep 2 # Aguardar a limpeza completa
fi

# Iniciar a aplicação com PM2
log "Iniciando a aplicação com PM2..."
pm2 start ecosystem.config.js
check_error "Falha ao iniciar aplicação com PM2"

# Salvar a nova configuração do PM2
log "Atualizando configuração do PM2..."
pm2 save --force  # Force garante que salvamos mesmo com outras aplicações rodando
check_error "Falha ao salvar configuração do PM2"

# Não precisamos do startup pois já está configurado
# Verificar status da aplicação
APP_STATUS=$(pm2 show poc-eagle)
if [[ $APP_STATUS == *"online"* ]]; then
    log "Aplicação implantada com sucesso!"
    
    # Mostrar recursos utilizados
    log "Status atual do PM2:"
    pm2 list | grep "poc-eagle"
else
    log "AVISO: A aplicação pode não estar rodando corretamente. Verifique os logs."
fi

echo "============================================"
echo "Implantação concluída! A aplicação está rodando na porta 3000"
echo "e está disponível via Nginx em http://38.102.126.144"
echo ""
echo "Comandos úteis:"
echo "- Verificar status: pm2 status"
echo "- Ver logs da aplicação: pm2 logs poc-eagle"
echo "- Ver logs do Nginx: tail -f /var/log/nginx/error.log"
echo "- Ver logs do deploy: tail -f $LOG_FILE"
echo "- Backups disponíveis em: $BACKUP_DIR"
echo "============================================" 
;System caching engine storage, possible values: files, memcached, fake, redis
UBCACHE_STORAGE="memcached"
;Optional memcached settings
;MEMCACHED_SERVER=localhost
;MEMCACHED_PORT=11211
;Optional option - debug mode of cache engine. Significantly reduces performance.
;UBCACHE_DEBUG=1
;Sets recorder default chunk time in seconds
RECORDER_CHUNK_TIME=60
;Run recorder on demand due camera activation event
RECORDER_ON_CAMERA_ACTIVATION=1
;log recorder debug log into exports/recorder_debug.log
RECORDER_DEBUG=0
;log recorder debug log into exports/rotator_debug.log
ROTATOR_DEBUG=0
;Percent of each storage space which must be free all time
STORAGE_RESERVED_SPACE=10
;MySQL dumps max age in days before rotation. Set option to 0 to disable rotation.
BACKUPS_MAX_AGE=7
;Rights set for limited user accounts. Comma separated.
LIMITED_RIGHTS="TASKBAR,LIVECAMS,ARCHIVE,EXPORT,WALL,MOTION"
;Percent of storage space reserved for videos exported by users.
EXPORTS_RESERVED_SPACE=10
;Use new rotator model?
ROTATOR_FAST=1
;Enable AI objects recognition?
NEURAL_ENABLED=0
;Full URL of supported AI engine
NEURAL_API_URL="http://dragonfly:8080"
;Is live wall enabled?
LIVE_WALL=1
;Is channel shots validation enabled?
CHANSHOTS_VALIDATION=1
;Embed channel screenshots into page body. Significantly reduces performance.
CHANSHOTS_EMBED=0
;Render some watermark file path on embedded channel screenshots? Example: "skins/wrcolor.png"
CHANSHOTS_WATERMARK=
;Web application custom name
WA_NAME="EagleView"
;Web application custom icons URLs, 192x192 and 512x512 png required
WA_ICON_192="skins/webapp/wa192.png"
WA_ICON_512="skins/webapp/wa512.png"
;Motion post detection is enabled?
MODET_ENABLED=1
;Force background schedule run on video export task creation?
EXPORT_FORCED_SCHED=1
;Enables quick camera search feature
QUICKSEARCH_ENABLED=1
;Is license keys module enabled?
LICENSES_ENABLED=0
;Is page load indicator enabled?
PAGE_LOAD_INDICATOR=1

from django.core.management.base import BaseCommand
import subprocess
import sys

class Command(BaseCommand):
    help = 'Verifica se o FFmpeg está instalado e disponível'
    
    def handle(self, *args, **options):
        self.stdout.write('Verificando FFmpeg...')
        
        try:
            # Tentar executar ffmpeg para verificar se está disponível
            result = subprocess.run(['ffmpeg', '-version'], 
                                 capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # Extrair informação da versão
                version_line = result.stdout.split('\n')[0]
                self.stdout.write(
                    self.style.SUCCESS(f'✓ FFmpeg encontrado: {version_line}')
                )
                
                # Verificar codecs importantes
                codec_result = subprocess.run(['ffmpeg', '-codecs'], 
                                           capture_output=True, text=True, timeout=10)
                
                if 'libx264' in codec_result.stdout:
                    self.stdout.write(self.style.SUCCESS('✓ Codec H.264 (libx264) disponível'))
                else:
                    self.stdout.write(self.style.WARNING('⚠ Codec H.264 (libx264) não encontrado'))
                
                return
                
        except subprocess.TimeoutExpired:
            self.stdout.write(self.style.ERROR('✗ FFmpeg não responde (timeout)'))
        except FileNotFoundError:
            self.stdout.write(self.style.ERROR('✗ FFmpeg não encontrado no PATH'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Erro ao verificar FFmpeg: {str(e)}'))
        
        # Instruções de instalação
        self.stdout.write('\n' + self.style.WARNING('INSTALAÇÃO DO FFMPEG:'))
        
        if sys.platform.startswith('win'):
            self.stdout.write('Windows:')
            self.stdout.write('1. Baixe o FFmpeg de: https://ffmpeg.org/download.html')
            self.stdout.write('2. Extraia o arquivo e adicione o diretório bin/ ao PATH')
            self.stdout.write('3. Ou instale via chocolatey: choco install ffmpeg')
            
        elif sys.platform.startswith('darwin'):
            self.stdout.write('macOS:')
            self.stdout.write('brew install ffmpeg')
            
        else:
            self.stdout.write('Linux (Ubuntu/Debian):')
            self.stdout.write('sudo apt update && sudo apt install ffmpeg')
            self.stdout.write('\nLinux (CentOS/RHEL):')
            self.stdout.write('sudo yum install ffmpeg') 
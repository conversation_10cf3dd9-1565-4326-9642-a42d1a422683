{"users": [{"id": 1, "nome": "Administrador", "email": "<EMAIL>", "senha": "teste123", "role": "admin", "status": "active", "lastLogin": "2024-03-19T10:00:00.000Z", "createdAt": "2024-03-19T10:00:00.000Z", "updatedAt": "2024-03-19T10:00:00.000Z", "preferences": {"language": "pt-BR", "theme": "light", "notifications": true}}], "cameras": [{"nome": "home", "ip": "**********", "username": "<EMAIL>", "password": "Timelapse@1!", "port": "4747", "protocol": "http", "status": "active", "descricao": "", "createdBy": 1, "updatedAt": "2025-05-15T05:24:22.906Z", "id": 2, "createdAt": "2025-05-15T05:16:00.031Z", "lastConnection": "2025-05-15T05:16:00.031Z", "location": "", "model": "", "url_path": "", "connection_options": ""}, {"id": 3, "nome": "Times Square NYC - EarthCam", "ip": "video3.earthcam.com", "port": "1935", "protocol": "hls", "username": "", "password": "", "url_path": "/fecnetwork/hdtimes10.flv/chunklist.m3u8", "status": "active", "descricao": "Vista ao vivo da Times Square em Nova York via EarthCam", "location": "Times Square, Nova York, EUA", "model": "EarthCam HD Network", "createdBy": 1, "createdAt": "2025-05-25T10:38:28.521Z", "updatedAt": "2025-05-25T10:38:28.521Z", "lastConnection": "2025-05-25T10:38:28.522Z", "connection_options": "protocol=hls,adaptive_bitrate=true"}, {"id": 4, "nome": "Lauderdale By The Sea - Florida", "ip": "videos3.earthcam.com", "port": "80", "protocol": "hls", "username": "", "password": "", "url_path": "/fecnetwork/9314.flv/playlist.m3u8", "status": "active", "descricao": "Vista da praia de Lauderdale By The Sea, Florida", "location": "Lauderdale By The Sea, Florida, EUA", "model": "EarthCam Network", "createdBy": 1, "createdAt": "2025-05-25T10:38:28.522Z", "updatedAt": "2025-05-25T10:38:28.522Z", "lastConnection": "2025-05-25T10:38:28.522Z", "connection_options": "protocol=hls,adaptive_bitrate=true"}, {"id": 5, "nome": "São Paulo Traffic Cam", "ip": "www.weatherbug.com", "port": "443", "protocol": "https", "username": "", "password": "", "url_path": "/traffic-cam/sao-paulo-sao-paulo-br", "status": "active", "descricao": "Câmera de trânsito de São Paulo via WeatherBug", "location": "São Paulo, SP, Brasil", "model": "WeatherBug Traffic Cam", "createdBy": 1, "createdAt": "2025-05-25T10:38:28.522Z", "updatedAt": "2025-05-25T10:38:28.522Z", "lastConnection": "2025-05-25T10:38:28.522Z", "connection_options": "protocol=https,format=mjpeg"}, {"id": 6, "nome": "Ubatuba Traffic Cam", "ip": "www.weatherbug.com", "port": "443", "protocol": "https", "username": "", "password": "", "url_path": "/traffic-cam/ubatuba-sao-paulo-br", "status": "active", "descricao": "Câmera de trânsito de Ubatuba via WeatherBug", "location": "Ubatuba, SP, Brasil", "model": "WeatherBug Traffic Cam", "createdBy": 1, "createdAt": "2025-05-25T10:38:28.522Z", "updatedAt": "2025-05-25T10:38:28.522Z", "lastConnection": "2025-05-25T10:38:28.522Z", "connection_options": "protocol=https,format=mjpeg"}, {"id": 7, "nome": "PR-18 Km 2.5 San Juan", "ip": "www.dtop.gov.pr", "port": "443", "protocol": "https", "username": "", "password": "", "url_path": "/camaras-trafico/pr18-km2-5", "status": "active", "descricao": "Câmera de trânsito PR-18 Km 2.5 em San Juan, Porto Rico", "location": "San Juan, Porto Rico", "model": "DTOP Traffic Cam", "createdBy": 1, "createdAt": "2025-05-25T10:38:28.522Z", "updatedAt": "2025-05-25T10:38:28.522Z", "lastConnection": "2025-05-25T10:38:28.522Z", "connection_options": "protocol=https,format=jpeg,refresh_rate=30000"}, {"id": 8, "nome": "Waikato Traffic Cam", "ip": "www.journeys.nzta.govt.nz", "port": "443", "protocol": "https", "username": "", "password": "", "url_path": "/traffic-cameras/waikato", "status": "active", "descricao": "Câmera de trânsito de Waikato, Nova Zelândia", "location": "Waikato, Nova Zelândia", "model": "NZTA Journey Planner", "createdBy": 1, "createdAt": "2025-05-25T10:38:28.522Z", "updatedAt": "2025-05-25T10:38:28.522Z", "lastConnection": "2025-05-25T10:38:28.522Z", "connection_options": "protocol=https,format=jpeg,refresh_rate=60000"}], "logs": [{"id": 1, "userId": 1, "action": "login", "details": "<PERSON>gin bem-sucedido", "ip": "***********", "timestamp": "2024-03-19T10:00:00.000Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 2, "timestamp": "2025-05-15T05:41:11.365Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 3, "timestamp": "2025-05-15T05:41:21.129Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:41:28.867Z"}, "ip": "localhost", "id": 4, "timestamp": "2025-05-15T05:41:28.991Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 5, "timestamp": "2025-05-15T05:42:12.797Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:42:12.512Z"}, "ip": "localhost", "id": 6, "timestamp": "2025-05-15T05:42:12.832Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 7, "timestamp": "2025-05-15T05:46:55.968Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:47:09.625Z"}, "ip": "localhost", "id": 8, "timestamp": "2025-05-15T05:47:10.089Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 9, "timestamp": "2025-05-15T05:47:32.037Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:47:31.835Z"}, "ip": "localhost", "id": 10, "timestamp": "2025-05-15T05:47:32.214Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 11, "timestamp": "2025-05-15T05:47:38.738Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:47:38.356Z"}, "ip": "localhost", "id": 12, "timestamp": "2025-05-15T05:47:38.860Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 13, "timestamp": "2025-05-15T05:47:50.526Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:47:52.774Z"}, "ip": "localhost", "id": 14, "timestamp": "2025-05-15T05:47:53.196Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 15, "timestamp": "2025-05-15T05:48:31.904Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:48:31.382Z"}, "ip": "localhost", "id": 16, "timestamp": "2025-05-15T05:48:31.956Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:48:47.211Z"}, "ip": "localhost", "id": 17, "timestamp": "2025-05-15T05:48:47.590Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 18, "timestamp": "2025-05-15T05:53:03.461Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:53:06.426Z"}, "ip": "localhost", "id": 19, "timestamp": "2025-05-15T05:53:06.795Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 20, "timestamp": "2025-05-15T05:55:20.577Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:55:23.314Z"}, "ip": "localhost", "id": 21, "timestamp": "2025-05-15T05:55:23.496Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "**********", "id": 22, "timestamp": "2025-05-15T05:57:24.560Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:57:50.639Z"}, "ip": "**********", "id": 23, "timestamp": "2025-05-15T05:57:50.809Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "**********", "id": 24, "timestamp": "2025-05-15T05:58:00.374Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T05:58:00.167Z"}, "ip": "**********", "id": 25, "timestamp": "2025-05-15T05:58:00.397Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 26, "timestamp": "2025-05-15T06:00:56.010Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T06:01:01.523Z"}, "ip": "localhost", "id": 27, "timestamp": "2025-05-15T06:01:01.869Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 28, "timestamp": "2025-05-15T06:03:03.334Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T06:03:02.380Z"}, "ip": "localhost", "id": 29, "timestamp": "2025-05-15T06:03:03.511Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 30, "timestamp": "2025-05-15T06:03:23.520Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T06:04:02.182Z"}, "ip": "localhost", "id": 31, "timestamp": "2025-05-15T06:04:02.444Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 32, "timestamp": "2025-05-15T06:04:09.029Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T06:04:08.752Z"}, "ip": "localhost", "id": 33, "timestamp": "2025-05-15T06:04:09.134Z"}, {"userId": 1, "action": "LOGIN", "details": "Login realizado com sucesso", "ip": "localhost", "id": 34, "timestamp": "2025-05-15T06:04:17.414Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T06:04:21.263Z"}, "ip": "localhost", "id": 35, "timestamp": "2025-05-15T06:04:21.510Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_DISCONNECT", "category": "camera", "severity": "info", "details": "Desconectando da câmera home", "ip": "localhost", "id": 36, "timestamp": "2025-05-15T06:04:27.042Z"}, {"userId": 1, "cameraId": 2, "action": "CAMERA_CONNECT", "category": "camera", "severity": "info", "details": "Conectando à câmera home", "metadata": {"protocol": "http", "timestamp": "2025-05-15T06:04:27.069Z"}, "ip": "localhost", "id": 37, "timestamp": "2025-05-15T06:04:27.221Z"}], "settings": {"system": {"defaultProtocol": "flv", "reconnectAttempts": 3, "reconnectInterval": 2000, "streamQuality": "high", "maxCameras": 10, "maintenance": {"backupEnabled": true, "backupInterval": "24h", "retentionDays": 30}}, "security": {"passwordPolicy": {"minLength": 8, "requireNumbers": true, "requireSpecialChars": true, "requireUppercase": true}, "session": {"timeout": 30, "maxAttempts": 3, "lockDuration": 15}}, "notifications": {"email": {"enabled": true, "server": "smtp.example.com", "port": 587, "username": "<EMAIL>"}, "telegram": {"enabled": false, "botToken": "", "chatId": ""}}, "storage": {"type": "local", "path": "./recordings", "maxSize": "500GB", "autoCleanup": true, "retentionDays": 30}}, "recordings": [{"id": 1, "cameraId": 1, "filename": "cam1_2024-03-19_10-00-00.mp4", "startTime": "2024-03-19T10:00:00.000Z", "endTime": "2024-03-19T11:00:00.000Z", "duration": 3600, "size": "1.2GB", "type": "scheduled", "status": "completed", "path": "/recordings/2024/03/19/", "metadata": {"resolution": "1920x1080", "fps": 30, "codec": "h264"}, "createdAt": "2024-03-19T10:00:00.000Z"}], "alerts": [{"id": 1, "cameraId": 1, "type": "motion", "severity": "medium", "status": "new", "message": "Movimento detectado na Camera Principal", "metadata": {"region": "entrada", "confidence": 0.85}, "timestamp": "2024-03-19T10:00:00.000Z", "acknowledgedBy": null, "acknowledgedAt": null}]}
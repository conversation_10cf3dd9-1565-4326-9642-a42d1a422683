{% extends 'base.html' %}
{% load static %}

{% block title %}Configurações de Servidor - EagleView{% endblock %}

{% block extra_css %}
<style>
    /* Card moderno para servidores */
    .server-card-modern {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        position: relative;
    }
    
    .server-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--highlight-orange), var(--highlight-blue));
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .server-card-modern:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 106, 0, 0.3);
    }
    
    .server-card-modern:hover::before {
        opacity: 1;
    }
    
    /* Status badges modernos */
    .server-status-modern.active {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
    }
    
    .server-status-modern.inactive {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        border: none;
    }
    
    .connection-type-modern {
        background: linear-gradient(135deg, var(--highlight-blue), #0056b3);
        color: white;
        border: none;
        text-transform: uppercase;
        font-weight: 600;
        letter-spacing: 0.5px;
    }
    
    /* Informações do servidor */
    .server-info-modern {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.5rem;
    }
    
    .info-item-modern {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .info-item-modern:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
    }
    
    .info-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--highlight-orange), var(--highlight-blue));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.75rem;
        color: white;
        font-size: 1.2rem;
    }
    
    .info-label-modern {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value-modern {
        color: var(--text-primary);
        font-weight: 600;
        font-size: 0.95rem;
        word-break: break-all;
    }
    
    /* Botões de ação modernos */
    .action-buttons {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .action-btn-modern {
        flex: 1;
        min-width: 120px;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        position: relative;
        overflow: hidden;
    }
    
    .action-btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }
    
    .action-btn-modern:hover::before {
        left: 100%;
    }
    
    .btn-browse {
        background: linear-gradient(135deg, var(--highlight-orange), #e85d00);
        color: white;
        border: none;
    }
    
    .btn-test {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
    }
    
    /* Estado vazio melhorado */
    .empty-state-modern {
        text-align: center;
        padding: 5rem 2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
        backdrop-filter: blur(10px);
        border: 2px dashed rgba(255, 106, 0, 0.3);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }
    
    .empty-state-modern::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, transparent, rgba(255, 106, 0, 0.1), transparent);
        animation: rotate 20s linear infinite;
    }
    
    .empty-state-content {
        position: relative;
        z-index: 1;
    }
    
    .empty-state-modern .empty-icon {
        font-size: 5rem;
        background: linear-gradient(135deg, var(--highlight-orange), var(--highlight-blue));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
        filter: drop-shadow(0 4px 8px rgba(255, 106, 0, 0.3));
    }
    
    /* Help modal */
    .help-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--highlight-orange);
    }
    
    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
        }
        
        .action-btn-modern {
            min-width: auto;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Cabeçalho Moderno -->
            <div class="d-flex justify-content-between align-items-center mb-5">
                <div>
                    <h1 class="mb-2 d-flex align-items-center">
                        <i class="fas fa-server me-3 text-primary float"></i>
                        Configurações de Servidor
                    </h1>
                    <p class="text-muted mb-0">Conecte-se a servidores SSH/FTP para importar imagens automaticamente</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'server_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Nova Configuração
                    </a>
                    <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="fas fa-question-circle me-2"></i>
                        Ajuda
                    </button>
                </div>
            </div>

            <!-- Lista de Servidores -->
            {% if servers %}
                <div class="row g-4">
                    {% for server in servers %}
                    <div class="col-xl-4 col-lg-6 col-md-6">
                        <div class="card h-100 server-card-modern">
                            <!-- Header do Card -->
                            <div class="card-header border-0 pb-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h5 class="card-title fw-bold mb-2">
                                            <i class="fas fa-server me-2 text-primary"></i>
                                            {{ server.name }}
                                        </h5>
                                        <div class="d-flex gap-2 align-items-center flex-wrap">
                                            <span class="badge connection-type-modern">{{ server.get_connection_type_display }}</span>
                                            <span class="badge server-status-modern {% if server.is_active %}active{% else %}inactive{% endif %}">
                                                <i class="fas fa-circle me-1"></i>
                                                {% if server.is_active %}Ativo{% else %}Inativo{% endif %}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary rounded-circle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="{% url 'server_edit' server.id %}">
                                                <i class="fas fa-edit me-2 text-primary"></i>Editar
                                            </a></li>
                                            <li><a class="dropdown-item" href="{% url 'server_browse' server.id %}">
                                                <i class="fas fa-images me-2 text-info"></i>Navegar Imagens
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="testConnection({{ server.id }})">
                                                <i class="fas fa-plug me-2 text-success"></i>Testar Conexão
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete({{ server.id }}, '{{ server.name }}')">
                                                <i class="fas fa-trash me-2"></i>Remover
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Informações do Servidor -->
                            <div class="card-body">
                                <div class="server-info-modern">
                                    <div class="info-grid">
                                        <div class="info-item-modern">
                                            <div class="info-icon">
                                                <i class="fas fa-globe"></i>
                                            </div>
                                            <div class="info-label-modern">Servidor</div>
                                            <div class="info-value-modern">{{ server.host }}:{{ server.port }}</div>
                                        </div>
                                        <div class="info-item-modern">
                                            <div class="info-icon">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="info-label-modern">Usuário</div>
                                            <div class="info-value-modern">{{ server.username }}</div>
                                        </div>
                                        <div class="info-item-modern">
                                            <div class="info-icon">
                                                <i class="fas fa-folder"></i>
                                            </div>
                                            <div class="info-label-modern">Pasta Remota</div>
                                            <div class="info-value-modern">{{ server.remote_path|default:"/home/" }}</div>
                                        </div>
                                        {% if server.last_connected %}
                                        <div class="info-item-modern">
                                            <div class="info-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="info-label-modern">Última Conexão</div>
                                            <div class="info-value-modern">{{ server.last_connected|date:"d/m/Y H:i" }}</div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Ações Modernas -->
                                <div class="action-buttons">
                                    <a href="{% url 'server_browse' server.id %}" class="action-btn-modern btn-browse">
                                        <i class="fas fa-images"></i>
                                        Navegar
                                    </a>
                                    <button class="action-btn-modern btn-test" onclick="testConnection({{ server.id }})">
                                        <i class="fas fa-plug"></i>
                                        Testar
                                    </button>
                                </div>
                            </div>
                                    <i class="fas fa-edit me-1"></i>
                                    Editar
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- Estado Vazio Moderno -->
                <div class="empty-state-modern">
                    <div class="empty-state-content">
                        <i class="fas fa-server empty-icon"></i>
                        <h3 class="mb-3">Nenhum servidor configurado</h3>
                        <p class="text-muted mb-4">
                            Configure conexões SSH ou FTP para importar imagens diretamente dos seus servidores de câmeras.<br>
                            Automatize o processo de criação de timelapses com importação remota.
                        </p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="{% url 'server_create' %}" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>
                                Configurar Primeiro Servidor
                            </a>
                            <button class="btn btn-outline-info btn-lg" data-bs-toggle="modal" data-bs-target="#helpModal">
                                <i class="fas fa-question-circle me-2"></i>
                                Como Configurar
                            </button>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal de Ajuda -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle text-info me-2"></i>
                    Como Configurar Servidores
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="help-card">
                    <h6><i class="fas fa-ssh me-2 text-primary"></i>Conexão SSH</h6>
                    <p class="mb-2">Para servidores Linux/Unix com acesso SSH:</p>
                    <ul class="mb-0">
                        <li>Porta padrão: 22</li>
                        <li>Requer chave SSH ou senha</li>
                        <li>Acesso direto aos arquivos do sistema</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h6><i class="fas fa-folder me-2 text-warning"></i>Conexão FTP</h6>
                    <p class="mb-2">Para servidores com protocolo FTP:</p>
                    <ul class="mb-0">
                        <li>Porta padrão: 21</li>
                        <li>Suporte a FTP e SFTP</li>
                        <li>Ideal para câmeras IP com servidor FTP</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h6><i class="fas fa-camera me-2 text-success"></i>Dicas de Configuração</h6>
                    <ul class="mb-0">
                        <li>Configure a pasta remota onde as imagens são salvas</li>
                        <li>Teste a conexão antes de salvar</li>
                        <li>Use chaves SSH para maior segurança</li>
                        <li>Organize as imagens por data nos servidores</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <a href="{% url 'server_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Configurar Agora
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmação de Exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirmar Exclusão
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja remover a configuração de servidor <strong id="serverName"></strong>?</p>
                <p class="text-muted small">Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        Remover
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(serverId, serverName) {
    document.getElementById('serverName').textContent = serverName;
    document.getElementById('deleteForm').action = `/configuracoes/${serverId}/deletar/`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function testConnection(serverId) {
    const button = event.target;
    const originalContent = button.innerHTML;
    
    // Mostrar loading
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testando...';
    button.disabled = true;
    
    fetch(`/api/servidor/${serverId}/testar/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            NotificationSystem.show('✅ ' + data.message, 'success');
        } else {
            NotificationSystem.show('❌ ' + data.message, 'error');
        }
    })
    .catch(error => {
        NotificationSystem.show('❌ Erro ao testar conexão', 'error');
    })
    .finally(() => {
        // Restaurar botão
        button.innerHTML = originalContent;
        button.disabled = false;
    });
}
</script>
{% endblock %} 
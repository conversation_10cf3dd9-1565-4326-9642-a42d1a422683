/*  Copyright (c) 2009 Facebook
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

td.sorted {
  color:#0000FF;
}

td.vbar, th.vbar {
  text-align: right;
  border-left:
  solid 1px #bdc7d8;
}

td.vbbar, th.vbar {
  text-align: right;
  border-left:
  solid 1px #bdc7d8;
  color:blue;
}

/* diff reports: display regressions in red */
td.vrbar {
  text-align: right;
  border-left:solid 1px #bdc7d8;
  color:red;
}

/* diff reports: display improvements in green */
td.vgbar {
  text-align: right;
  border-left: solid 1px #bdc7d8;
  color:green;
}

td.vwbar, th.vwbar {
  text-align: right;
  border-left: solid 1px white;
}

td.vwlbar, th.vwlbar {
  text-align: left;
  border-left: solid 1px white;
}

p.blue  {
  color:blue
}

.bubble {
  background-color:#C3D9FF
}

ul.xhprof_actions {
  float: right;
  padding-left: 16px;
  list-style-image: none;
  list-style-type: none;
  margin:10px 10px 10px 3em;
  position:relative;
}

ul.xhprof_actions li {
  border-bottom:1px solid #D8DFEA;
}

ul.xhprof_actions li a:hover {
  background:#3B5998 none repeat scroll 0 0;
  color:#FFFFFF;
}

